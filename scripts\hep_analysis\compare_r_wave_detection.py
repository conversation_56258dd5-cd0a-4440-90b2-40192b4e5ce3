#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比原版本和NeuroKit2版本的R波检测结果

这个脚本将：
1. 使用原版本07脚本检测R波
2. 使用NeuroKit2版本检测R波  
3. 对比两种方法的检测结果
4. 可视化R波位置差异
5. 分析哪种方法的0ms对齐更准确

作者: HEP Analysis Team
日期: 2024-12-19
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import mne
import warnings
warnings.filterwarnings('ignore')

# 导入两个版本的分析器
import sys
import importlib.util

# 导入NeuroKit2版本
spec_nk = importlib.util.spec_from_file_location("neurokit2_analyzer", "09_neurokit2_fixed_hep_analyzer.py")
neurokit2_analyzer = importlib.util.module_from_spec(spec_nk)
spec_nk.loader.exec_module(neurokit2_analyzer)
NeuroKit2FixedHEPAnalyzer = neurokit2_analyzer.NeuroKit2FixedHEPAnalyzer

# 导入原版本
spec_orig = importlib.util.spec_from_file_location("original_analyzer", "07_seven_conditions_hep_analyzer_eeg_filtered - 副本.py")
original_analyzer = importlib.util.module_from_spec(spec_orig)
spec_orig.loader.exec_module(original_analyzer)
HEPAnalyzer = original_analyzer.HEPAnalyzer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_test_data():
    """加载测试数据"""
    data_dir = Path("D:/ecgeeg/18-eegecg手动预处理5-重参考2-双侧乳突")
    test_files = list(data_dir.rglob("*_test_*.fif"))
    
    if not test_files:
        raise FileNotFoundError("未找到测试文件")
    
    test_file = test_files[0]
    print(f"📁 测试文件: {test_file.name}")
    
    # 读取数据
    raw = mne.io.read_raw_fif(test_file, preload=True, verbose=False)
    data = raw.get_data() * 1000  # mV -> μV
    sampling_rate = raw.info['sfreq']
    
    # 分离ECG数据
    ecg_data = data[61:, :]  # 后58个通道是ECG
    ecg11_signal = ecg_data[10, :]  # ECG11通道
    
    return {
        'raw': raw,
        'data': data,
        'ecg_data': ecg_data,
        'ecg11_signal': ecg11_signal,
        'sampling_rate': sampling_rate,
        'file_path': str(test_file),
        'file_name': test_file.name
    }

def detect_with_original_method(test_data):
    """使用原版本方法检测R波"""
    print(f"\n🔍 使用原版本方法检测R波...")
    
    # 初始化原版本分析器
    analyzer = HEPAnalyzer(
        data_dir="D:/ecgeeg/18-eegecg手动预处理5-重参考2-双侧乳突",
        validation_excel_path="dummy.xlsx",  # 占位符
        enable_quality_control=False
    )
    
    # 检测R波
    r_peaks, quality_score = analyzer.detect_r_waves_fixed_electrode(
        test_data['ecg_data'], test_data['sampling_rate']
    )
    
    print(f"    📊 原版本结果: {len(r_peaks)}个R波, 质量={quality_score:.3f}")
    
    return {
        'r_peaks': r_peaks,
        'quality_score': quality_score,
        'method': 'Original_Fixed_Electrode'
    }

def detect_with_neurokit2_method(test_data):
    """使用NeuroKit2方法检测R波"""
    print(f"\n🔍 使用NeuroKit2方法检测R波...")
    
    # 初始化NeuroKit2分析器
    analyzer = NeuroKit2FixedHEPAnalyzer(
        data_dir="D:/ecgeeg/18-eegecg手动预处理5-重参考2-双侧乳突",
        enable_quality_control=False
    )
    
    # 检测R波
    r_peaks, quality_info = analyzer.detect_r_waves_neurokit2(
        test_data['ecg_data'], test_data['sampling_rate']
    )
    
    print(f"    📊 NeuroKit2结果: {len(r_peaks)}个R波, 质量={quality_info['quality']:.3f}")
    print(f"    📊 最佳方法: {quality_info['method']}")
    
    return {
        'r_peaks': r_peaks,
        'quality_score': quality_info['quality'],
        'method': quality_info['method'],
        'quality_info': quality_info
    }

def compare_detection_results(original_result, neurokit2_result, test_data):
    """对比两种检测结果"""
    print(f"\n📊 对比检测结果...")
    
    orig_peaks = original_result['r_peaks']
    nk2_peaks = neurokit2_result['r_peaks']
    
    print(f"原版本: {len(orig_peaks)}个R波")
    print(f"NeuroKit2: {len(nk2_peaks)}个R波")
    print(f"差异: {len(nk2_peaks) - len(orig_peaks)}个R波")
    
    # 计算R-R间隔统计
    if len(orig_peaks) > 1:
        orig_rr = np.diff(orig_peaks) / test_data['sampling_rate']
        orig_hr = 60 / np.mean(orig_rr)
        orig_hr_std = np.std(60 / orig_rr)
        print(f"原版本心率: {orig_hr:.1f} ± {orig_hr_std:.1f} BPM")
    
    if len(nk2_peaks) > 1:
        nk2_rr = np.diff(nk2_peaks) / test_data['sampling_rate']
        nk2_hr = 60 / np.mean(nk2_rr)
        nk2_hr_std = np.std(60 / nk2_rr)
        print(f"NeuroKit2心率: {nk2_hr:.1f} ± {nk2_hr_std:.1f} BPM")
    
    # 分析R波位置差异
    if len(orig_peaks) > 0 and len(nk2_peaks) > 0:
        # 找到最接近的R波对
        min_len = min(len(orig_peaks), len(nk2_peaks))
        position_diffs = []
        
        for i in range(min_len):
            # 找到最接近的R波
            closest_idx = np.argmin(np.abs(nk2_peaks - orig_peaks[i]))
            if closest_idx < len(nk2_peaks):
                diff = nk2_peaks[closest_idx] - orig_peaks[i]
                position_diffs.append(diff)
        
        if position_diffs:
            mean_diff = np.mean(position_diffs)
            std_diff = np.std(position_diffs)
            max_diff = np.max(np.abs(position_diffs))
            
            print(f"R波位置差异: 平均{mean_diff:.1f}±{std_diff:.1f}样本点")
            print(f"最大位置差异: {max_diff:.1f}样本点 ({max_diff/test_data['sampling_rate']*1000:.1f}ms)")
    
    return {
        'position_differences': position_diffs if 'position_diffs' in locals() else [],
        'original_count': len(orig_peaks),
        'neurokit2_count': len(nk2_peaks)
    }

def visualize_comparison(original_result, neurokit2_result, test_data, save_dir):
    """可视化对比结果"""
    print(f"\n📊 生成对比可视化...")
    
    ecg_signal = test_data['ecg11_signal']
    sampling_rate = test_data['sampling_rate']
    time_axis = np.arange(len(ecg_signal)) / sampling_rate
    
    # 只显示前60秒的数据
    display_duration = 60  # 秒
    display_samples = int(display_duration * sampling_rate)
    
    if len(ecg_signal) > display_samples:
        ecg_display = ecg_signal[:display_samples]
        time_display = time_axis[:display_samples]
    else:
        ecg_display = ecg_signal
        time_display = time_axis
    
    # 筛选显示范围内的R波
    orig_peaks_display = original_result['r_peaks'][original_result['r_peaks'] < display_samples]
    nk2_peaks_display = neurokit2_result['r_peaks'][neurokit2_result['r_peaks'] < display_samples]
    
    # 创建对比图
    fig, axes = plt.subplots(3, 1, figsize=(15, 12))
    
    # 1. 原版本检测结果
    axes[0].plot(time_display, ecg_display, 'k-', linewidth=0.8, alpha=0.7, label='ECG11信号')
    if len(orig_peaks_display) > 0:
        orig_times = orig_peaks_display / sampling_rate
        orig_amplitudes = ecg_display[orig_peaks_display]
        axes[0].scatter(orig_times, orig_amplitudes, c='red', s=50, marker='o', 
                       alpha=0.8, zorder=5, label=f'R波 (n={len(orig_peaks_display)})')
        
        # 添加垂直线
        for t in orig_times:
            axes[0].axvline(t, color='red', alpha=0.3, linestyle='--', linewidth=1)
    
    axes[0].set_title(f'原版本检测结果 - 质量评分: {original_result["quality_score"]:.3f}', 
                     fontsize=12, fontweight='bold')
    axes[0].set_ylabel('幅度 (μV)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 2. NeuroKit2检测结果
    axes[1].plot(time_display, ecg_display, 'k-', linewidth=0.8, alpha=0.7, label='ECG11信号')
    if len(nk2_peaks_display) > 0:
        nk2_times = nk2_peaks_display / sampling_rate
        nk2_amplitudes = ecg_display[nk2_peaks_display]
        axes[1].scatter(nk2_times, nk2_amplitudes, c='blue', s=50, marker='s', 
                       alpha=0.8, zorder=5, label=f'R波 (n={len(nk2_peaks_display)})')
        
        # 添加垂直线
        for t in nk2_times:
            axes[1].axvline(t, color='blue', alpha=0.3, linestyle='--', linewidth=1)
    
    axes[1].set_title(f'NeuroKit2检测结果 ({neurokit2_result["method"]}) - 质量评分: {neurokit2_result["quality_score"]:.3f}', 
                     fontsize=12, fontweight='bold')
    axes[1].set_ylabel('幅度 (μV)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # 3. 叠加对比
    axes[2].plot(time_display, ecg_display, 'k-', linewidth=0.8, alpha=0.7, label='ECG11信号')
    
    if len(orig_peaks_display) > 0:
        orig_times = orig_peaks_display / sampling_rate
        orig_amplitudes = ecg_display[orig_peaks_display]
        axes[2].scatter(orig_times, orig_amplitudes, c='red', s=60, marker='o', 
                       alpha=0.7, zorder=5, label=f'原版本 (n={len(orig_peaks_display)})')
    
    if len(nk2_peaks_display) > 0:
        nk2_times = nk2_peaks_display / sampling_rate
        nk2_amplitudes = ecg_display[nk2_peaks_display]
        axes[2].scatter(nk2_times, nk2_amplitudes, c='blue', s=40, marker='s', 
                       alpha=0.7, zorder=6, label=f'NeuroKit2 (n={len(nk2_peaks_display)})')
    
    axes[2].set_title('两种方法对比', fontsize=12, fontweight='bold')
    axes[2].set_xlabel('时间 (秒)')
    axes[2].set_ylabel('幅度 (μV)')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图像
    save_path = save_dir / "r_wave_detection_comparison.png"
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ 对比图已保存: {save_path}")
    
    return fig

def main():
    """主函数"""
    print("🚀 R波检测方法对比分析")
    print("="*50)
    
    # 1. 加载测试数据
    test_data = load_test_data()
    
    # 2. 使用原版本检测
    original_result = detect_with_original_method(test_data)
    
    # 3. 使用NeuroKit2检测
    neurokit2_result = detect_with_neurokit2_method(test_data)
    
    # 4. 对比结果
    comparison = compare_detection_results(original_result, neurokit2_result, test_data)
    
    # 5. 生成可视化
    save_dir = Path("D:/ecgeeg/30-数据分析/5-HBA/result/r_wave_comparison")
    save_dir.mkdir(parents=True, exist_ok=True)
    
    visualize_comparison(original_result, neurokit2_result, test_data, save_dir)
    
    # 6. 生成总结报告
    print(f"\n📋 对比分析总结")
    print("="*40)
    print(f"原版本:")
    print(f"  - R波数量: {comparison['original_count']}")
    print(f"  - 质量评分: {original_result['quality_score']:.3f}")
    print(f"  - 检测方法: {original_result['method']}")
    
    print(f"\nNeuroKit2版本:")
    print(f"  - R波数量: {comparison['neurokit2_count']}")
    print(f"  - 质量评分: {neurokit2_result['quality_score']:.3f}")
    print(f"  - 检测方法: {neurokit2_result['method']}")
    
    print(f"\n对比结果:")
    print(f"  - 数量差异: {comparison['neurokit2_count'] - comparison['original_count']}")
    
    if comparison['position_differences']:
        mean_diff = np.mean(comparison['position_differences'])
        print(f"  - 平均位置差异: {mean_diff:.1f}样本点 ({mean_diff/test_data['sampling_rate']*1000:.1f}ms)")
    
    # 推荐
    if neurokit2_result['quality_score'] > original_result['quality_score']:
        print(f"\n🏆 推荐: NeuroKit2方法 (质量更高)")
    else:
        print(f"\n🏆 推荐: 原版本方法 (质量更高)")
    
    print(f"\n📁 结果保存在: {save_dir}")

if __name__ == "__main__":
    main()
