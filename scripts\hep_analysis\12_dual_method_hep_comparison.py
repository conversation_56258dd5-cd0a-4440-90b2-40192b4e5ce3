#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双方法HEP提取对比分析器 v1.0
================================

对比两种HEP提取方法的质量和结果：
1. 方法A：基于ECG通道的R波检测 + EEG的HEP提取
2. 方法B：基于EEG心电伪迹的R波检测 + EEG的HEP提取

目标：
- 减少预处理对波形的影响
- 对比两种方法的HEP质量
- 验证与文献结果的一致性
- 为7个实验条件提供最优方法选择

作者：研究团队
日期：2025年6月
版本：1.0 - 双方法对比版本
"""

import os
import numpy as np
import mne
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
import pandas as pd
import random
from scipy import signal
from scipy.stats import zscore
import re
import pickle
import json
from matplotlib import font_manager
warnings.filterwarnings('ignore')

# 设置中文字体
font_path = r"C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKaiMono-Regular.ttf"
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = ['LXGW WenKai Mono']
else:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class DualMethodHEPComparator:
    """双方法HEP提取对比分析器"""

    def __init__(self, data_dir, validation_excel_path):
        """
        初始化双方法HEP对比分析器

        Parameters:
        -----------
        data_dir : str
            数据目录路径
        validation_excel_path : str
            电压验证结果Excel文件路径
        """
        self.data_dir = Path(data_dir)
        self.validation_excel_path = Path(validation_excel_path)

        # HEP分析参数（最小预处理）
        self.hep_params = {
            'time_window': (-0.2, 0.65),  # 缩短时间窗口，减少边界效应
            'baseline_window': (-0.1, 0),  # 缩短基线窗口
            'min_heartbeats': 50,  # 降低最小心跳数要求
            'rr_interval_range': (0.4, 1.5),  # 放宽R-R间隔范围
            'sampling_rate': 500,  # 采样率
            'eeg_filter': {
                'l_freq': 0.5,   # 提高高通滤波频率，减少低频漂移
                'h_freq': 40.0   # 提高低通滤波频率，保留更多信号
            }
        }
        
        # 可视化参数
        self.viz_params = {
            'time_window': (-0.2, 0.65),  # 可视化时间窗口
            'figure_dpi': 300  # 图像分辨率
        }
        
        # 脑区电极分组
        self.electrode_groups = {
            '前额叶': ['Fp1', 'Fp2', 'AF3', 'AF4', 'F7', 'F8'],
            '额叶': ['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'Fz'],
            '中央': ['FC1', 'FC2', 'FC3', 'FC4', 'C1', 'C2', 'C3', 'C4', 'Cz'],
            '顶叶': ['CP1', 'CP2', 'CP3', 'CP4', 'P1', 'P2', 'P3', 'P4', 'Pz'],
            '后部': ['PO3', 'PO4', 'O1', 'O2', 'Oz']
        }
        
        # 七个实验条件定义
        self.conditions = {
            'prac': '练习状态',
            'rest1': '静息状态1',
            'rest2': '静息状态2', 
            'rest3': '静息状态3',
            'test1': '测试状态1',
            'test2': '测试状态2',
            'test3': '测试状态3'
        }
        
        # 条件颜色配置
        self.condition_colors = {
            'prac': '#8B4513',    # 棕色
            'rest1': '#1f77b4',   # 蓝色
            'rest2': '#2ca02c',   # 绿色
            'rest3': '#17becf',   # 青色
            'test1': '#ff7f0e',   # 橙色
            'test2': '#d62728',   # 红色
            'test3': '#9467bd'    # 紫色
        }
        
        # 结果存储
        self.comparison_results = {
            'method_a_results': {},  # ECG方法结果
            'method_b_results': {},  # EEG方法结果
            'quality_comparison': {},  # 质量对比
            'selected_files': {}     # 选择的文件
        }

        # 结果保存路径
        self.results_save_dir = Path("../../result/hep_dual_method_comparison")
        self.results_save_dir.mkdir(parents=True, exist_ok=True)
        
        print("="*80)
        print("双方法HEP提取对比分析器")
        print("="*80)
        print(f"数据目录: {self.data_dir}")
        print(f"验证结果文件: {self.validation_excel_path}")
        print(f"实验条件: {list(self.conditions.keys())}")
        print(f"HEP时间窗口: {self.hep_params['time_window'][0]*1000:.0f}ms 到 {self.hep_params['time_window'][1]*1000:.0f}ms")
        print(f"EEG滤波: {self.hep_params['eeg_filter']['l_freq']}-{self.hep_params['eeg_filter']['h_freq']}Hz")

    def extract_condition_from_filename(self, filename):
        """从文件名中提取实验条件"""
        pattern = r'(\d+)_(\d+)_.*_(prac|test|rest)_.*\.fif'
        match = re.match(pattern, filename)

        if match:
            session_id = match.group(2)
            base_condition = match.group(3)

            if base_condition == 'prac':
                return 'prac'
            elif base_condition == 'test':
                return f'test{int(session_id)}'
            elif base_condition == 'rest':
                return f'rest{int(session_id)}'

        return None

    def select_files_for_comparison(self, n_files_per_condition=10):
        """
        为对比分析选择文件
        
        Parameters:
        -----------
        n_files_per_condition : int
            每个条件选择的文件数量（较少数量用于详细对比）
        """
        print("\n" + "="*60)
        print("为双方法对比选择文件")
        print("="*60)

        try:
            # 读取电压验证结果
            validation_df = pd.read_excel(self.validation_excel_path, sheet_name='验证结果')
            print(f"读取到 {len(validation_df)} 个文件的验证结果")

            # 筛选高质量文件
            high_quality_files = validation_df[validation_df['都在范围内'] == True]
            print(f"高质量文件数量: {len(high_quality_files)}")

            # 按条件分组文件
            condition_files = {condition: [] for condition in self.conditions.keys()}

            for _, row in high_quality_files.iterrows():
                filename = row['文件名']
                condition = self.extract_condition_from_filename(filename)

                if condition and condition in self.conditions:
                    file_path = self.data_dir / filename
                    if file_path.exists():
                        condition_files[condition].append({
                            'file_name': filename,
                            'file_path': file_path,
                            'eeg_amplitude': row['EEG幅度(μV)'],
                            'ecg_amplitude': row['ECG幅度(μV)']
                        })
            
            # 为每个条件选择文件
            for condition in self.conditions.keys():
                available_files = condition_files[condition]
                print(f"\n条件 '{condition}' ({self.conditions[condition]}):")
                print(f"  可用文件数: {len(available_files)}")

                if len(available_files) == 0:
                    print(f"  ⚠️ 无可用文件")
                    self.comparison_results['selected_files'][condition] = []
                    continue

                # 随机选择文件用于对比
                n_select = min(n_files_per_condition, len(available_files))
                selected_files = random.sample(available_files, n_select)

                self.comparison_results['selected_files'][condition] = selected_files

                print(f"  选择文件数: {len(selected_files)}")
                for i, file_info in enumerate(selected_files[:3]):
                    print(f"    {i+1}. {file_info['file_name']}")
                if len(selected_files) > 3:
                    print(f"    ... 还有 {len(selected_files)-3} 个文件")

            return True

        except Exception as e:
            print(f"❌ 文件选择失败: {str(e)}")
            return False

    def load_and_minimal_preprocess(self, file_path):
        """
        加载数据并进行最小预处理

        Parameters:
        -----------
        file_path : Path
            文件路径

        Returns:
        --------
        raw : mne.Raw
            预处理后的数据
        """
        try:
            # 加载数据
            raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
            print(f"    📥 原始数据: {raw.info['nchan']}通道, {raw.times[-1]:.1f}秒")

            # 最小预处理：仅进行必要的滤波
            raw_filtered = raw.copy()
            raw_filtered.filter(
                l_freq=self.hep_params['eeg_filter']['l_freq'],
                h_freq=self.hep_params['eeg_filter']['h_freq'],
                fir_design='firwin',
                verbose=False
            )
            print(f"    🔧 最小滤波: {self.hep_params['eeg_filter']['l_freq']}-{self.hep_params['eeg_filter']['h_freq']}Hz")

            return raw_filtered

        except Exception as e:
            print(f"❌ 加载失败 {file_path}: {e}")
            return None

    def method_a_ecg_based_r_detection(self, raw):
        """
        方法A：基于ECG通道的R波检测

        Parameters:
        -----------
        raw : mne.Raw
            预处理后的数据

        Returns:
        --------
        r_peaks : array
            R波位置
        detection_info : dict
            检测信息
        """
        print("    🫀 方法A：ECG通道R波检测...")

        try:
            # 获取ECG通道（后58通道）
            ecg_data = raw.get_data()[61:, :]  # 后58通道是ECG

            # 选择ECG11通道（距离心脏最近）
            ecg11_idx = 10  # ECG11是第11个ECG通道（索引10）
            ecg_signal = ecg_data[ecg11_idx, :]
            sampling_rate = raw.info['sfreq']

            # ECG专用滤波（5-15Hz）增强QRS复合波
            sos = signal.butter(4, [5, 15], btype='band', fs=sampling_rate, output='sos')
            ecg_filtered = signal.sosfilt(sos, ecg_signal)

            # 计算信号的绝对值和平方
            ecg_squared = ecg_filtered ** 2

            # 移动平均平滑
            window_size = int(0.08 * sampling_rate)  # 80ms窗口
            ecg_smoothed = np.convolve(ecg_squared, np.ones(window_size)/window_size, mode='same')

            # 自适应阈值检测
            threshold = np.mean(ecg_smoothed) + 2 * np.std(ecg_smoothed)
            rough_peaks, _ = signal.find_peaks(
                ecg_smoothed,
                height=threshold,
                distance=int(0.3 * sampling_rate)  # 最小间隔300ms
            )

            # 精确定位真正的R波峰值
            refined_peaks = []
            search_window = int(0.05 * sampling_rate)  # ±50ms搜索窗口

            for rough_peak in rough_peaks:
                start_idx = max(0, rough_peak - search_window)
                end_idx = min(len(ecg_signal), rough_peak + search_window)
                search_segment = ecg_signal[start_idx:end_idx]

                # 找到绝对值最大的位置
                if np.max(search_segment) > abs(np.min(search_segment)):
                    local_peak = np.argmax(search_segment)
                else:
                    local_peak = np.argmin(search_segment)

                true_r_peak = start_idx + local_peak
                refined_peaks.append(true_r_peak)

            r_peaks = np.array(refined_peaks)

            # 计算检测质量
            if len(r_peaks) > 1:
                rr_intervals = np.diff(r_peaks) / sampling_rate
                heart_rate = 60 / np.mean(rr_intervals)
                rr_std = np.std(rr_intervals)
            else:
                heart_rate = 0
                rr_std = 0

            detection_info = {
                'method': 'ECG_based',
                'channel': 'ECG11',
                'n_peaks': len(r_peaks),
                'heart_rate': heart_rate,
                'rr_std': rr_std,
                'quality_score': len(r_peaks) / 100 if len(r_peaks) > 0 else 0
            }

            print(f"      ✅ ECG方法: {len(r_peaks)}个R波, 心率{heart_rate:.1f}bpm")
            return r_peaks, detection_info

        except Exception as e:
            print(f"      ❌ ECG方法失败: {e}")
            return None, None

    def method_b_eeg_based_r_detection(self, raw):
        """
        方法B：基于EEG心电伪迹的R波检测

        Parameters:
        -----------
        raw : mne.Raw
            预处理后的数据

        Returns:
        --------
        r_peaks : array
            R波位置
        detection_info : dict
            检测信息
        """
        print("    🧠 方法B：EEG心电伪迹R波检测...")

        try:
            # 获取EEG数据（前61通道）
            eeg_data = raw.get_data()[:61, :]
            sampling_rate = raw.info['sfreq']

            # 心电敏感电极（按检测优先级排序）
            cardiac_channels = ['Fp1', 'Fp2', 'F7', 'F8', 'Fz', 'Cz']
            available_channels = [ch for ch in cardiac_channels if ch in raw.ch_names[:61]]

            best_peaks = None
            best_info = None
            best_quality = 0

            for channel in available_channels[:3]:  # 测试前3个最优电极
                try:
                    ch_idx = raw.ch_names.index(channel)
                    signal_data = eeg_data[ch_idx, :]

                    # 心跳检测专用滤波
                    sos = signal.butter(4, [1, 20], btype='band', fs=sampling_rate, output='sos')
                    filtered_data = signal.sosfilt(sos, signal_data)

                    # 计算绝对值和梯度
                    abs_signal = np.abs(filtered_data)
                    gradient = np.abs(np.gradient(filtered_data))

                    # 组合信号
                    combined_signal = abs_signal + 0.5 * gradient

                    # 自适应阈值
                    rolling_mean = pd.Series(combined_signal).rolling(window=int(5*sampling_rate), center=True).mean()
                    rolling_std = pd.Series(combined_signal).rolling(window=int(5*sampling_rate), center=True).std()

                    threshold = rolling_mean + 2 * rolling_std
                    threshold = threshold.fillna(np.mean(combined_signal) + 2*np.std(combined_signal))

                    # 检测峰值
                    peaks = []
                    for i in range(1, len(combined_signal)-1):
                        if (combined_signal[i] > combined_signal[i-1] and
                            combined_signal[i] > combined_signal[i+1] and
                            combined_signal[i] > threshold.iloc[i]):

                            # 检查最小间隔
                            if not peaks or (i - peaks[-1]) > 0.4 * sampling_rate:
                                peaks.append(i)

                    peaks = np.array(peaks)

                    # 计算质量评分
                    if len(peaks) > 1:
                        rr_intervals = np.diff(peaks) / sampling_rate
                        heart_rate = 60 / np.mean(rr_intervals)
                        rr_std = np.std(rr_intervals)

                        # 心率合理性检查
                        if 40 <= heart_rate <= 120:
                            quality = len(peaks) / 100
                        else:
                            quality = 0
                    else:
                        heart_rate = 0
                        rr_std = 0
                        quality = 0

                    if quality > best_quality:
                        best_quality = quality
                        best_peaks = peaks
                        best_info = {
                            'method': 'EEG_based',
                            'channel': channel,
                            'n_peaks': len(peaks),
                            'heart_rate': heart_rate,
                            'rr_std': rr_std,
                            'quality_score': quality
                        }

                        print(f"      🔍 {channel}: {len(peaks)}个R波, 心率{heart_rate:.1f}bpm, 质量{quality:.3f}")

                except Exception as e:
                    print(f"      ⚠️ 电极 {channel} 处理失败: {e}")
                    continue

            if best_peaks is not None:
                print(f"      ✅ EEG方法最佳结果: {best_info['channel']}, {len(best_peaks)}个R波")
                return best_peaks, best_info
            else:
                print(f"      ❌ EEG方法失败")
                return None, None

        except Exception as e:
            print(f"      ❌ EEG方法失败: {e}")
            return None, None

    def extract_hep_epochs(self, raw, r_peaks, method_name):
        """
        提取HEP时期数据

        Parameters:
        -----------
        raw : mne.Raw
            预处理后的数据
        r_peaks : array
            R波位置
        method_name : str
            方法名称

        Returns:
        --------
        hep_epochs : array
            HEP时期数据 (epochs x channels x samples)
        times : array
            时间轴
        """
        try:
            # 获取EEG数据（前61通道）
            eeg_data = raw.get_data()[:61, :]
            sampling_rate = raw.info['sfreq']

            # 计算时间窗口对应的样本点
            pre_samples = int(abs(self.hep_params['time_window'][0]) * sampling_rate)
            post_samples = int(self.hep_params['time_window'][1] * sampling_rate)
            total_samples = pre_samples + post_samples

            # 创建时间轴
            times = np.linspace(self.hep_params['time_window'][0],
                               self.hep_params['time_window'][1],
                               total_samples)

            # 提取每个心跳周期的EEG数据
            valid_epochs = []

            for r_peak in r_peaks:
                start_idx = r_peak - pre_samples
                end_idx = r_peak + post_samples

                # 确保索引在有效范围内
                if start_idx >= 0 and end_idx < eeg_data.shape[1]:
                    epoch = eeg_data[:, start_idx:end_idx]

                    # 基线校正
                    baseline_start_time = self.hep_params['baseline_window'][0] - self.hep_params['time_window'][0]
                    baseline_end_time = self.hep_params['baseline_window'][1] - self.hep_params['time_window'][0]

                    baseline_start = int(baseline_start_time * sampling_rate)
                    baseline_end = int(baseline_end_time * sampling_rate)

                    if baseline_start >= 0 and baseline_end <= epoch.shape[1] and baseline_start < baseline_end:
                        baseline_mean = np.mean(epoch[:, baseline_start:baseline_end], axis=1, keepdims=True)
                        epoch_corrected = epoch - baseline_mean
                        valid_epochs.append(epoch_corrected)

            if len(valid_epochs) > 0:
                hep_epochs = np.array(valid_epochs)
                print(f"      📊 {method_name}: 提取了{len(valid_epochs)}个有效epochs")
                return hep_epochs, times
            else:
                print(f"      ❌ {method_name}: 未提取到有效epochs")
                return None, times

        except Exception as e:
            print(f"      ❌ {method_name} epochs提取失败: {e}")
            return None, None

    def calculate_hep_quality_metrics(self, hep_epochs, times, method_name):
        """
        计算HEP质量指标

        Parameters:
        -----------
        hep_epochs : array
            HEP时期数据
        times : array
            时间轴
        method_name : str
            方法名称

        Returns:
        --------
        quality_metrics : dict
            质量指标
        """
        try:
            # 计算平均HEP
            hep_average = np.mean(hep_epochs, axis=0)

            # 基线窗口索引
            baseline_mask = (times >= self.hep_params['baseline_window'][0]) & \
                           (times <= self.hep_params['baseline_window'][1])

            # HEP成分窗口（R波后200-600ms）
            hep_mask = (times >= 0.2) & (times <= 0.6)

            # 计算信噪比
            if np.sum(hep_mask) > 0 and np.sum(baseline_mask) > 0:
                signal_power = np.mean(np.var(hep_epochs[:, :, hep_mask], axis=2))
                noise_power = np.mean(np.var(hep_epochs[:, :, baseline_mask], axis=2))
                snr = 10 * np.log10(signal_power / noise_power) if noise_power > 0 else 0
            else:
                snr = 0

            # 基线稳定性
            if np.sum(baseline_mask) > 0:
                baseline_std = np.mean(np.std(hep_epochs[:, :, baseline_mask], axis=2))
            else:
                baseline_std = 0

            # 伪迹检测（基于z-score）
            epoch_amplitudes = np.max(np.abs(hep_epochs), axis=(1, 2))
            z_scores = np.abs(zscore(epoch_amplitudes))
            artifact_epochs = np.sum(z_scores > 3)
            artifact_rate = artifact_epochs / len(hep_epochs)

            # 全局场功率（GFP）
            gfp = np.std(hep_average, axis=0)
            max_gfp = np.max(gfp)

            # HEP窗口内的平均幅度
            if np.sum(hep_mask) > 0:
                hep_amplitude = np.mean(np.abs(hep_average[:, hep_mask]))
            else:
                hep_amplitude = 0

            quality_metrics = {
                'method': method_name,
                'n_epochs': len(hep_epochs),
                'snr': snr,
                'baseline_std': baseline_std,
                'artifact_rate': artifact_rate,
                'artifact_epochs': artifact_epochs,
                'max_gfp': max_gfp,
                'hep_amplitude': hep_amplitude,
                'hep_average': hep_average,
                'times': times
            }

            print(f"      📈 {method_name}质量: SNR={snr:.1f}dB, 伪迹率={artifact_rate:.2f}, GFP峰值={max_gfp:.2e}")
            return quality_metrics

        except Exception as e:
            print(f"      ❌ {method_name}质量计算失败: {e}")
            return None

    def analyze_single_file_dual_method(self, file_info, condition):
        """
        使用双方法分析单个文件

        Parameters:
        -----------
        file_info : dict
            文件信息
        condition : str
            实验条件

        Returns:
        --------
        result : dict
            双方法分析结果
        """
        print(f"\n  📁 分析文件: {file_info['file_name']}")

        try:
            # 1. 加载和预处理
            raw = self.load_and_minimal_preprocess(file_info['file_path'])
            if raw is None:
                return None

            # 2. 方法A：ECG方法
            r_peaks_a, detection_info_a = self.method_a_ecg_based_r_detection(raw)

            # 3. 方法B：EEG方法
            r_peaks_b, detection_info_b = self.method_b_eeg_based_r_detection(raw)

            result = {
                'file_name': file_info['file_name'],
                'condition': condition,
                'method_a': None,
                'method_b': None
            }

            # 4. 处理方法A结果
            if r_peaks_a is not None and len(r_peaks_a) >= self.hep_params['min_heartbeats']:
                hep_epochs_a, times_a = self.extract_hep_epochs(raw, r_peaks_a, "方法A")
                if hep_epochs_a is not None:
                    quality_a = self.calculate_hep_quality_metrics(hep_epochs_a, times_a, "方法A")
                    if quality_a is not None:
                        result['method_a'] = {
                            'detection_info': detection_info_a,
                            'quality_metrics': quality_a,
                            'success': True
                        }

            # 5. 处理方法B结果
            if r_peaks_b is not None and len(r_peaks_b) >= self.hep_params['min_heartbeats']:
                hep_epochs_b, times_b = self.extract_hep_epochs(raw, r_peaks_b, "方法B")
                if hep_epochs_b is not None:
                    quality_b = self.calculate_hep_quality_metrics(hep_epochs_b, times_b, "方法B")
                    if quality_b is not None:
                        result['method_b'] = {
                            'detection_info': detection_info_b,
                            'quality_metrics': quality_b,
                            'success': True
                        }

            # 6. 设置默认失败结果
            if result['method_a'] is None:
                result['method_a'] = {'success': False, 'reason': 'R波检测失败或数量不足'}
            if result['method_b'] is None:
                result['method_b'] = {'success': False, 'reason': 'R波检测失败或数量不足'}

            return result

        except Exception as e:
            print(f"    ❌ 文件分析失败: {e}")
            return None

    def analyze_condition_dual_method(self, condition):
        """
        使用双方法分析单个实验条件

        Parameters:
        -----------
        condition : str
            实验条件
        """
        print(f"\n{'='*60}")
        print(f"双方法分析条件: {condition} ({self.conditions[condition]})")
        print("="*60)

        selected_files = self.comparison_results['selected_files'][condition]
        if not selected_files:
            print(f"⚠️ 条件 {condition} 无可用文件")
            return

        condition_results = []

        for file_info in selected_files:
            result = self.analyze_single_file_dual_method(file_info, condition)
            if result is not None:
                condition_results.append(result)

        # 统计结果
        method_a_success = sum(1 for r in condition_results if r['method_a']['success'])
        method_b_success = sum(1 for r in condition_results if r['method_b']['success'])

        print(f"\n📊 条件 {condition} 分析结果:")
        print(f"  总文件数: {len(selected_files)}")
        print(f"  方法A成功: {method_a_success}/{len(condition_results)}")
        print(f"  方法B成功: {method_b_success}/{len(condition_results)}")

        # 存储结果
        self.comparison_results['method_a_results'][condition] = condition_results
        self.comparison_results['method_b_results'][condition] = condition_results

        # 计算质量对比
        self._calculate_quality_comparison(condition, condition_results)

    def _calculate_quality_comparison(self, condition, condition_results):
        """计算质量对比指标"""
        method_a_metrics = []
        method_b_metrics = []

        for result in condition_results:
            if result['method_a']['success']:
                method_a_metrics.append(result['method_a']['quality_metrics'])
            if result['method_b']['success']:
                method_b_metrics.append(result['method_b']['quality_metrics'])

        comparison = {
            'condition': condition,
            'method_a_count': len(method_a_metrics),
            'method_b_count': len(method_b_metrics),
            'method_a_avg_snr': np.mean([m['snr'] for m in method_a_metrics]) if method_a_metrics else 0,
            'method_b_avg_snr': np.mean([m['snr'] for m in method_b_metrics]) if method_b_metrics else 0,
            'method_a_avg_gfp': np.mean([m['max_gfp'] for m in method_a_metrics]) if method_a_metrics else 0,
            'method_b_avg_gfp': np.mean([m['max_gfp'] for m in method_b_metrics]) if method_b_metrics else 0,
            'method_a_avg_epochs': np.mean([m['n_epochs'] for m in method_a_metrics]) if method_a_metrics else 0,
            'method_b_avg_epochs': np.mean([m['n_epochs'] for m in method_b_metrics]) if method_b_metrics else 0
        }

        self.comparison_results['quality_comparison'][condition] = comparison

        print(f"  质量对比:")
        print(f"    方法A平均SNR: {comparison['method_a_avg_snr']:.1f}dB")
        print(f"    方法B平均SNR: {comparison['method_b_avg_snr']:.1f}dB")
        print(f"    方法A平均epochs: {comparison['method_a_avg_epochs']:.1f}")
        print(f"    方法B平均epochs: {comparison['method_b_avg_epochs']:.1f}")

    def create_dual_method_comparison_visualization(self, condition):
        """
        创建双方法对比可视化

        Parameters:
        -----------
        condition : str
            实验条件
        """
        if condition not in self.comparison_results['method_a_results']:
            print(f"⚠️ 条件 {condition} 无可用数据进行可视化")
            return None

        condition_results = self.comparison_results['method_a_results'][condition]

        # 收集成功的结果
        method_a_heps = []
        method_b_heps = []
        times = None

        for result in condition_results:
            if result['method_a']['success']:
                method_a_heps.append(result['method_a']['quality_metrics']['hep_average'])
                if times is None:
                    times = result['method_a']['quality_metrics']['times']
            if result['method_b']['success']:
                method_b_heps.append(result['method_b']['quality_metrics']['hep_average'])
                if times is None:
                    times = result['method_b']['quality_metrics']['times']

        if not method_a_heps and not method_b_heps:
            print(f"⚠️ 条件 {condition} 无有效HEP数据")
            return None

        # 创建图形
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'双方法HEP对比 - {self.conditions[condition]}', fontsize=16, fontweight='bold')

        # 选择关键电极进行可视化
        key_electrodes = ['Fp1', 'Fz', 'Cz', 'Pz', 'C3', 'C4']

        for i, electrode in enumerate(key_electrodes):
            if i >= 6:  # 只显示6个电极
                break

            ax = axes[i // 3, i % 3]

            # 假设电极索引（需要根据实际通道名称调整）
            electrode_idx = i  # 简化处理

            # 绘制方法A结果
            if method_a_heps:
                method_a_data = np.array([hep[electrode_idx, :] for hep in method_a_heps])
                method_a_mean = np.mean(method_a_data, axis=0) * 1e6  # 转换为μV
                method_a_sem = np.std(method_a_data, axis=0) / np.sqrt(len(method_a_data)) * 1e6

                times_ms = times * 1000  # 转换为毫秒
                ax.plot(times_ms, method_a_mean, color='blue', linewidth=2, label=f'方法A (ECG, n={len(method_a_heps)})')
                ax.fill_between(times_ms, method_a_mean - method_a_sem, method_a_mean + method_a_sem,
                               color='blue', alpha=0.3)

            # 绘制方法B结果
            if method_b_heps:
                method_b_data = np.array([hep[electrode_idx, :] for hep in method_b_heps])
                method_b_mean = np.mean(method_b_data, axis=0) * 1e6  # 转换为μV
                method_b_sem = np.std(method_b_data, axis=0) / np.sqrt(len(method_b_data)) * 1e6

                times_ms = times * 1000  # 转换为毫秒
                ax.plot(times_ms, method_b_mean, color='red', linewidth=2, label=f'方法B (EEG, n={len(method_b_heps)})')
                ax.fill_between(times_ms, method_b_mean - method_b_sem, method_b_mean + method_b_sem,
                               color='red', alpha=0.3)

            # 标记R波时间点
            ax.axvline(x=0, color='black', linestyle='--', alpha=0.7, label='R波')

            # 标记HEP成分窗口
            ax.axvspan(200, 600, alpha=0.1, color='green', label='HEP窗口')

            # 设置标题和标签
            ax.set_title(f'{electrode}', fontsize=12, fontweight='bold')
            ax.set_xlabel('时间 (ms)', fontsize=10)
            ax.set_ylabel('幅度 (μV)', fontsize=10)
            ax.grid(True, alpha=0.3)

            # 只在第一个子图显示图例
            if i == 0:
                ax.legend(loc='upper right', fontsize=8)

        # 隐藏多余的子图
        for i in range(len(key_electrodes), 6):
            axes[i // 3, i % 3].set_visible(False)

        plt.tight_layout()

        # 保存图像
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        plot_path = self.results_save_dir / f"dual_method_comparison_{condition}_{timestamp}.png"
        plt.savefig(plot_path, dpi=self.viz_params['figure_dpi'], bbox_inches='tight')
        plt.close()

        print(f"✅ 条件 {condition} 双方法对比可视化已保存: {plot_path}")
        return plot_path

    def generate_comparison_report(self, visualization_paths):
        """生成双方法对比分析报告"""
        print("\n" + "="*60)
        print("生成双方法对比分析报告")
        print("="*60)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = self.results_save_dir / f"dual_method_hep_comparison_report_{timestamp}.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# HEP双方法提取对比分析报告\n\n")
            f.write(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("## 📊 分析概览\n\n")

            f.write("### 对比方法\n")
            f.write("- **方法A**: 基于ECG通道的R波检测 + EEG的HEP提取\n")
            f.write("- **方法B**: 基于EEG心电伪迹的R波检测 + EEG的HEP提取\n\n")

            f.write("### 分析参数\n")
            f.write(f"- **时间窗口**: {self.hep_params['time_window'][0]*1000:.0f}ms 到 {self.hep_params['time_window'][1]*1000:.0f}ms\n")
            f.write(f"- **基线校正**: {self.hep_params['baseline_window'][0]*1000:.0f}ms 到 {self.hep_params['baseline_window'][1]*1000:.0f}ms\n")
            f.write(f"- **最小心跳数**: {self.hep_params['min_heartbeats']}\n")
            f.write(f"- **EEG滤波**: {self.hep_params['eeg_filter']['l_freq']}-{self.hep_params['eeg_filter']['h_freq']}Hz\n\n")

            f.write("## 📈 对比结果\n\n")

            # 统计每个条件的结果
            for condition in self.conditions.keys():
                f.write(f"### {condition} - {self.conditions[condition]}\n\n")

                if condition in self.comparison_results['quality_comparison']:
                    comparison = self.comparison_results['quality_comparison'][condition]

                    f.write(f"- **方法A成功数**: {comparison['method_a_count']}\n")
                    f.write(f"- **方法B成功数**: {comparison['method_b_count']}\n")
                    f.write(f"- **方法A平均SNR**: {comparison['method_a_avg_snr']:.1f} dB\n")
                    f.write(f"- **方法B平均SNR**: {comparison['method_b_avg_snr']:.1f} dB\n")
                    f.write(f"- **方法A平均epochs**: {comparison['method_a_avg_epochs']:.1f}\n")
                    f.write(f"- **方法B平均epochs**: {comparison['method_b_avg_epochs']:.1f}\n")

                    # 推荐最佳方法
                    if comparison['method_a_avg_snr'] > comparison['method_b_avg_snr']:
                        f.write(f"- **推荐方法**: 方法A (ECG) - SNR更高\n")
                    elif comparison['method_b_avg_snr'] > comparison['method_a_avg_snr']:
                        f.write(f"- **推荐方法**: 方法B (EEG) - SNR更高\n")
                    else:
                        f.write(f"- **推荐方法**: 两种方法质量相近\n")

                    f.write("\n")
                else:
                    f.write("- **状态**: 无可用数据\n\n")

            f.write("## 🎨 生成的可视化文件\n\n")
            for i, path in enumerate(visualization_paths, 1):
                f.write(f"{i}. {path.name}\n")
            f.write("\n")

            f.write("## 💡 分析结论\n\n")
            f.write("### 方法对比总结\n")

            # 计算总体统计
            total_a_success = sum(comp['method_a_count'] for comp in self.comparison_results['quality_comparison'].values())
            total_b_success = sum(comp['method_b_count'] for comp in self.comparison_results['quality_comparison'].values())
            avg_a_snr = np.mean([comp['method_a_avg_snr'] for comp in self.comparison_results['quality_comparison'].values() if comp['method_a_count'] > 0])
            avg_b_snr = np.mean([comp['method_b_avg_snr'] for comp in self.comparison_results['quality_comparison'].values() if comp['method_b_count'] > 0])

            f.write(f"- **方法A总成功数**: {total_a_success}\n")
            f.write(f"- **方法B总成功数**: {total_b_success}\n")
            f.write(f"- **方法A平均SNR**: {avg_a_snr:.1f} dB\n")
            f.write(f"- **方法B平均SNR**: {avg_b_snr:.1f} dB\n\n")

            f.write("### 建议\n")
            if avg_a_snr > avg_b_snr:
                f.write("1. **推荐使用方法A（ECG方法）**：基于ECG通道的R波检测具有更高的信噪比\n")
                f.write("2. ECG方法能够更准确地检测R波，减少伪迹影响\n")
                f.write("3. 适合有ECG通道记录的实验设计\n\n")
            else:
                f.write("1. **推荐使用方法B（EEG方法）**：基于EEG心电伪迹的检测具有更高的信噪比\n")
                f.write("2. EEG方法适合仅有EEG记录的实验\n")
                f.write("3. 减少了对额外ECG通道的依赖\n\n")

            f.write(f"---\n\n**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

        print(f"✅ 双方法对比分析报告已保存: {report_path}")
        return report_path

    def run_dual_method_comparison(self, n_files_per_condition=10):
        """
        运行完整的双方法对比分析

        Parameters:
        -----------
        n_files_per_condition : int
            每个条件选择的文件数量
        """
        print("开始双方法HEP提取对比分析...")

        # 第一步：选择文件
        if not self.select_files_for_comparison(n_files_per_condition):
            print("❌ 文件选择失败，终止分析")
            return False

        # 第二步：分析每个条件
        for condition in self.conditions.keys():
            self.analyze_condition_dual_method(condition)

        # 第三步：生成可视化
        visualization_paths = []
        for condition in self.conditions.keys():
            plot_path = self.create_dual_method_comparison_visualization(condition)
            if plot_path:
                visualization_paths.append(plot_path)

        # 第四步：生成分析报告
        self.generate_comparison_report(visualization_paths)

        # 第五步：保存结果
        self.save_comparison_results()

        print("\n" + "="*80)
        print("✅ 双方法HEP提取对比分析完成！")
        print("="*80)

        return True

    def save_comparison_results(self):
        """保存对比分析结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存完整结果
        results_file = self.results_save_dir / f"dual_method_comparison_results_{timestamp}.pkl"
        with open(results_file, 'wb') as f:
            pickle.dump(self.comparison_results, f)

        # 保存元数据
        metadata = {
            'timestamp': timestamp,
            'conditions': self.conditions,
            'hep_params': self.hep_params,
            'viz_params': self.viz_params,
            'electrode_groups': self.electrode_groups,
            'script_version': '12_dual_method_hep_comparison_v1.0'
        }
        metadata_file = self.results_save_dir / f"dual_method_comparison_metadata_{timestamp}.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

        print(f"✅ 对比分析结果已保存:")
        print(f"   数据文件: {results_file}")
        print(f"   元数据文件: {metadata_file}")


if __name__ == "__main__":
    # 配置参数
    data_directory = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"
    validation_excel = r"D:\ecgeeg\30-数据分析\5-HBA\result\unit_conversion\final_voltage_validation_data_20250602_225214.xlsx"

    print("双方法HEP提取对比分析")
    print("="*50)
    print("方法A：基于ECG通道的R波检测")
    print("方法B：基于EEG心电伪迹的R波检测")
    print("="*50)

    # 创建双方法对比分析器并运行
    comparator = DualMethodHEPComparator(data_directory, validation_excel)

    # 运行对比分析（每个条件选择10个文件进行详细对比）
    comparator.run_dual_method_comparison(n_files_per_condition=10)
