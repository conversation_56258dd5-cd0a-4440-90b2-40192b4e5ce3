#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的七条件NeuroKit2 HEP分析器

使用专业NeuroKit2方法分析所有七个实验条件：
- rest_1, rest_2, rest_3
- test_1, test_2, test_3  
- prac

特点：
1. 完全基于NeuroKit2专业ECG处理
2. 标准化的R波检测和HEP提取
3. 专业的质量评估和报告
4. 批量处理所有条件和被试

作者: HEP Analysis Team
日期: 2024-12-19
版本: v11 - 完整七条件NeuroKit2版本
"""

import numpy as np
import pandas as pd
import mne
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入专业NeuroKit2分析器
import sys
import importlib.util
spec = importlib.util.spec_from_file_location("professional_analyzer", "10_professional_neurokit2_hep_analyzer.py")
professional_analyzer = importlib.util.module_from_spec(spec)
spec.loader.exec_module(professional_analyzer)
ProfessionalNeuroKit2HEPAnalyzer = professional_analyzer.ProfessionalNeuroKit2HEPAnalyzer

# 导入NeuroKit2
try:
    import neurokit2 as nk
    NEUROKIT_AVAILABLE = True
except ImportError:
    NEUROKIT_AVAILABLE = False
    raise ImportError("请先安装NeuroKit2")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class CompleteNeuroKit2SevenConditionsAnalyzer:
    """完整的七条件NeuroKit2 HEP分析器"""
    
    def __init__(self, data_dir, output_dir):
        """
        初始化完整分析器
        
        Parameters:
        -----------
        data_dir : str
            数据目录路径
        output_dir : str
            输出目录路径
        """
        self.data_dir = Path(data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化专业分析器
        self.analyzer = ProfessionalNeuroKit2HEPAnalyzer(data_dir, enable_quality_control=False)
        
        # 分析结果存储
        self.results = {
            'processed_files': {},
            'summary_stats': {},
            'failed_files': [],
            'quality_report': {}
        }
        
        print(f"🚀 完整七条件NeuroKit2 HEP分析器初始化完成")
        print(f"📁 数据目录: {self.data_dir}")
        print(f"📁 输出目录: {self.output_dir}")

    def find_files_for_all_conditions(self):
        """查找所有条件的文件"""
        print(f"\n🔍 查找所有条件的文件...")
        
        all_files = {}
        
        for condition, pattern in self.analyzer.condition_patterns.items():
            files = list(self.data_dir.rglob(pattern))
            all_files[condition] = files
            print(f"   {condition}: {len(files)} 个文件")
        
        # 统计总数
        total_files = sum(len(files) for files in all_files.values())
        print(f"📊 总计: {total_files} 个文件")
        
        return all_files

    def analyze_single_condition(self, condition, files):
        """分析单个条件的所有文件"""
        print(f"\n🔍 分析条件: {condition} ({self.analyzer.conditions[condition]})")
        print(f"📁 文件数量: {len(files)}")
        
        condition_results = []
        failed_files = []
        
        for i, file_path in enumerate(files, 1):
            print(f"\n  📁 [{i}/{len(files)}] {file_path.name}")
            
            # 构建文件信息
            file_info = {
                'file_path': str(file_path),
                'file_name': file_path.name,
                'subject_id': file_path.stem.split('_')[0],
                'condition': condition
            }
            
            try:
                # 使用专业NeuroKit2方法分析
                result = self.analyzer.analyze_single_file_professional(file_info)
                
                if result['success']:
                    condition_results.append(result)
                    print(f"    ✅ 成功: {result['r_peaks_count']}个R波, "
                          f"质量: {result['quality_metrics']['quality_level']}")
                else:
                    failed_files.append({
                        'file_info': file_info,
                        'error': result['error']
                    })
                    print(f"    ❌ 失败: {result['error']}")
                    
            except Exception as e:
                failed_files.append({
                    'file_info': file_info,
                    'error': str(e)
                })
                print(f"    ❌ 异常: {e}")
        
        print(f"\n📊 条件 {condition} 分析完成:")
        print(f"   ✅ 成功: {len(condition_results)} 个文件")
        print(f"   ❌ 失败: {len(failed_files)} 个文件")
        
        return condition_results, failed_files

    def save_condition_results(self, condition, results):
        """保存条件分析结果 - 与07脚本格式完全一致"""
        if not results:
            print(f"⚠️ 条件 {condition} 没有成功的结果可保存")
            return

        print(f"💾 保存条件 {condition} 的结果...")

        # 使用与07脚本完全相同的保存目录结构
        base_save_dir = Path("D:/ecgeeg/30-数据分析/5-HBA/result/hep_fif_data")
        condition_dir = base_save_dir / condition
        condition_dir.mkdir(parents=True, exist_ok=True)

        # 保存每个文件的HEP数据为MNE .fif格式
        for result in results:
            subject_id = result['file_info']['subject_id']

            # 创建与07脚本完全相同的MNE格式
            info = mne.create_info(
                ch_names=result['channel_names'],
                sfreq=result['sampling_rate'],
                ch_types=['eeg'] * len(result['channel_names'])  # 只保存EEG数据
            )

            # 创建EpochsArray对象
            epochs = mne.EpochsArray(
                result['hep_epochs'],
                info,
                tmin=result['times'][0],
                events=np.array([[i, 0, 1] for i in range(len(result['hep_epochs']))]),
                event_id={'hep': 1}
            )

            # 添加元数据
            epochs.metadata = pd.DataFrame({
                'subject_id': [subject_id] * len(result['hep_epochs']),
                'condition': [condition] * len(result['hep_epochs'])
            })

            # 保存为fif文件 - 使用与07脚本相同的命名格式
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            fif_file = condition_dir / f"sub-{subject_id}_task-hep_cond-{condition}_{timestamp}-epo.fif"
            epochs.save(fif_file, overwrite=True)

            print(f"    💾 {subject_id}: HEP数据已保存为MNE格式")

            # 清理内存
            del epochs

    def generate_overall_report(self):
        """生成总体分析报告"""
        print(f"\n📋 生成总体分析报告...")
        
        # 收集所有结果的统计信息
        all_stats = []
        
        for condition, results in self.results['processed_files'].items():
            if results:
                for result in results:
                    all_stats.append({
                        'condition': condition,
                        'subject_id': result['file_info']['subject_id'],
                        'r_peaks_count': result['r_peaks_count'],
                        'hep_epochs_count': result['hep_epochs'].shape[0],
                        'quality_score': result['quality_metrics']['quality_score'],
                        'quality_level': result['quality_metrics']['quality_level'],
                        'heart_rate_mean': result['quality_metrics']['heart_rate_mean'],
                        'heart_rate_std': result['quality_metrics']['heart_rate_std']
                    })
        
        if not all_stats:
            print("⚠️ 没有成功的分析结果可生成报告")
            return
        
        # 创建总体统计DataFrame
        stats_df = pd.DataFrame(all_stats)
        
        # 保存详细统计
        detailed_report_file = self.output_dir / "detailed_analysis_report.csv"
        stats_df.to_csv(detailed_report_file, index=False, encoding='utf-8-sig')
        
        # 生成汇总统计
        summary_stats = {}
        
        for condition in self.analyzer.conditions.keys():
            condition_data = stats_df[stats_df['condition'] == condition]
            if len(condition_data) > 0:
                summary_stats[condition] = {
                    'count': len(condition_data),
                    'mean_r_peaks': condition_data['r_peaks_count'].mean(),
                    'mean_hep_epochs': condition_data['hep_epochs_count'].mean(),
                    'mean_quality_score': condition_data['quality_score'].mean(),
                    'mean_heart_rate': condition_data['heart_rate_mean'].mean(),
                    'excellent_quality_ratio': (condition_data['quality_level'] == 'excellent').mean()
                }
        
        # 保存汇总统计
        summary_file = self.output_dir / "summary_statistics.csv"
        summary_df = pd.DataFrame(summary_stats).T
        summary_df.to_csv(summary_file, encoding='utf-8-sig')
        
        # 打印报告
        print(f"\n📊 总体分析报告")
        print("="*60)
        
        for condition, stats in summary_stats.items():
            print(f"\n{condition} ({self.analyzer.conditions[condition]}):")
            print(f"   文件数量: {stats['count']}")
            print(f"   平均R波数: {stats['mean_r_peaks']:.1f}")
            print(f"   平均HEP epochs: {stats['mean_hep_epochs']:.1f}")
            print(f"   平均质量评分: {stats['mean_quality_score']:.3f}")
            print(f"   平均心率: {stats['mean_heart_rate']:.1f} BPM")
            print(f"   优秀质量比例: {stats['excellent_quality_ratio']:.1%}")
        
        # 失败文件报告
        if self.results['failed_files']:
            print(f"\n❌ 失败文件报告:")
            for failed in self.results['failed_files']:
                print(f"   {failed['file_info']['file_name']}: {failed['error']}")
        
        print(f"\n📁 详细报告已保存:")
        print(f"   📊 详细统计: {detailed_report_file}")
        print(f"   📋 汇总统计: {summary_file}")

    def run_complete_analysis(self):
        """运行完整的七条件分析"""
        print(f"🚀 开始完整的七条件NeuroKit2 HEP分析")
        print("="*70)
        
        start_time = datetime.now()
        
        # 1. 查找所有文件
        all_files = self.find_files_for_all_conditions()
        
        # 2. 分析每个条件
        for condition, files in all_files.items():
            if files:
                condition_results, failed_files = self.analyze_single_condition(condition, files)
                
                # 保存结果
                self.results['processed_files'][condition] = condition_results
                self.results['failed_files'].extend(failed_files)
                
                # 保存条件结果
                self.save_condition_results(condition, condition_results)
            else:
                print(f"⚠️ 条件 {condition} 没有找到文件")
        
        # 3. 生成总体报告
        self.generate_overall_report()
        
        # 4. 完成总结
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n🎉 完整分析完成!")
        print(f"⏱️ 总耗时: {duration}")
        print(f"📁 结果保存在: {self.output_dir}")
        
        # 统计总结
        total_success = sum(len(results) for results in self.results['processed_files'].values())
        total_failed = len(self.results['failed_files'])
        
        print(f"\n📊 最终统计:")
        print(f"   ✅ 成功分析: {total_success} 个文件")
        print(f"   ❌ 分析失败: {total_failed} 个文件")
        print(f"   📈 成功率: {total_success/(total_success+total_failed)*100:.1f}%")

def main():
    """主函数"""
    print("🚀 完整七条件NeuroKit2 HEP分析")
    print("="*50)
    
    # 设置路径
    data_dir = "D:/ecgeeg/18-eegecg手动预处理5-重参考2-双侧乳突"
    output_dir = "D:/ecgeeg/30-数据分析/5-HBA/result/complete_neurokit2_seven_conditions"
    
    # 创建分析器
    analyzer = CompleteNeuroKit2SevenConditionsAnalyzer(data_dir, output_dir)
    
    # 运行完整分析
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
