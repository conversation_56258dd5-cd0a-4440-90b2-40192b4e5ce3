#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用NeuroKit2修复R波对齐问题的HEP分析器

主要修复：
1. 使用NeuroKit2专业R波检测，解决极性判断错误
2. 确保0ms位置正确对齐到真实R波峰值
3. 支持多种检测算法自动选择最佳结果
4. 保持与原有分析流程的兼容性

作者: HEP Analysis Team  
日期: 2024-12-19
版本: v09 - NeuroKit2修复版
"""

import numpy as np
import pandas as pd
import mne
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime
import random
import warnings
warnings.filterwarnings('ignore')

# 导入NeuroKit2
try:
    import neurokit2 as nk
    NEUROKIT_AVAILABLE = True
except ImportError:
    NEUROKIT_AVAILABLE = False
    print("❌ NeuroKit2 未安装，请运行: pip install neurokit2")

class NeuroKit2FixedHEPAnalyzer:
    """使用NeuroKit2修复R波对齐的HEP分析器"""
    
    def __init__(self, data_dir, enable_quality_control=False):
        """
        初始化分析器
        
        Parameters:
        -----------
        data_dir : str
            数据目录路径
        enable_quality_control : bool
            是否启用质量控制
        """
        if not NEUROKIT_AVAILABLE:
            raise ImportError("请先安装NeuroKit2: pip install neurokit2")
            
        self.data_dir = Path(data_dir)
        self.enable_quality_control = enable_quality_control
        
        # HEP分析参数
        self.hep_params = {
            'time_window': (-0.2, 0.8),  # HEP时间窗口
            'baseline_window': (-0.2, -0.05),  # 基线校正窗口  
            'min_heartbeats': 30,  # 最少心跳数
            'rr_interval_range': (0.4, 2.0),  # 有效R-R间隔范围
        }
        
        # NeuroKit2检测参数 - 基于测试结果优化
        self.nk_params = {
            'preferred_methods': ['christov2004', 'neurokit', 'hamilton2002', 'pantompkins1985'],
            'correct_artifacts': True,
            'show_info': False  # 减少输出信息
        }
        
        # 实验条件定义
        self.conditions = {
            'rest_1': '静息态1',
            'rest_2': '静息态2', 
            'rest_3': '静息态3',
            'test_1': '测试态1',
            'test_2': '测试态2',
            'test_3': '测试态3',
            'prac': '练习'
        }
        
        # 文件匹配模式
        self.condition_patterns = {
            'rest_1': '*_01_*_rest_*.fif',
            'rest_2': '*_02_*_rest_*.fif',
            'rest_3': '*_03_*_rest_*.fif',
            'test_1': '*_01_*_test_*.fif',
            'test_2': '*_02_*_test_*.fif',
            'test_3': '*_03_*_test_*.fif',
            'prac': '*_prac_*.fif'
        }
        
        # 分析结果存储
        self.analysis_results = {
            'processed_files': {},
            'summary_stats': {},
            'failed_files': []
        }
        
        print(f"🔧 NeuroKit2修复版HEP分析器初始化完成")
        print(f"📁 数据目录: {self.data_dir}")
        print(f"🎛️ 质量控制: {'启用' if self.enable_quality_control else '禁用'}")
        print(f"🏆 推荐检测方法: {self.nk_params['preferred_methods'][0]}")

    def detect_r_waves_neurokit2(self, ecg_data, sampling_rate):
        """
        使用NeuroKit2进行专业R波检测 - 解决极性对齐问题
        
        Parameters:
        -----------
        ecg_data : array
            ECG信号数据 (channels x samples)
        sampling_rate : float
            采样率
            
        Returns:
        --------
        r_peaks : array
            R波位置（样本点）
        quality_info : dict
            检测质量信息
        """
        print(f"    🔍 使用NeuroKit2检测R波...")
        
        # 使用固定ECG11通道（索引10）
        fixed_channel = 10
        if fixed_channel >= ecg_data.shape[0]:
            raise ValueError(f"ECG通道{fixed_channel+1}不存在，数据只有{ecg_data.shape[0]}个通道")
            
        ecg_signal = ecg_data[fixed_channel, :]
        print(f"    📊 ECG11信号长度: {len(ecg_signal)} 样本点")
        
        # 尝试多种NeuroKit2方法，选择最佳结果
        best_result = None
        best_quality = 0
        best_method = None
        
        for method in self.nk_params['preferred_methods']:
            try:
                print(f"      🧪 尝试方法: {method}")
                
                # 使用NeuroKit2处理ECG信号
                signals, info = nk.ecg_process(
                    ecg_signal, 
                    sampling_rate=sampling_rate,
                    method=method
                )
                
                # 获取R波位置
                r_peaks = info["ECG_R_Peaks"]
                
                if len(r_peaks) == 0:
                    print(f"        ⚠️ {method}: 未检测到R波")
                    continue
                
                # 计算质量指标
                quality_score = self._evaluate_neurokit2_quality(
                    ecg_signal, r_peaks, sampling_rate
                )
                
                print(f"        📈 {method}: {len(r_peaks)}个R波, 质量={quality_score:.3f}")
                
                if quality_score > best_quality:
                    best_quality = quality_score
                    best_result = {
                        'r_peaks': r_peaks,
                        'signals': signals,
                        'info': info,
                        'method': method
                    }
                    best_method = method
                    
            except Exception as e:
                print(f"        ❌ {method} 失败: {e}")
                continue
        
        if best_result is None:
            print(f"    ❌ 所有NeuroKit2方法都失败")
            return np.array([]), {'quality': 0, 'method': 'failed', 'error': 'all_methods_failed'}
        
        print(f"    ✅ 最佳方法: {best_method}")
        print(f"    📊 检测结果: {len(best_result['r_peaks'])}个R波, 质量评分: {best_quality:.3f}")
        
        # 验证R波对齐（确保0ms位置正确）
        validated_peaks = self._validate_r_wave_alignment(
            ecg_signal, best_result['r_peaks'], sampling_rate
        )
        
        # 返回质量信息
        quality_info = {
            'quality': best_quality,
            'method': best_method,
            'total_peaks': len(validated_peaks),
            'original_peaks': len(best_result['r_peaks']),
            'alignment_validated': True
        }
        
        return validated_peaks, quality_info

    def _evaluate_neurokit2_quality(self, ecg_signal, r_peaks, sampling_rate):
        """
        评估NeuroKit2检测质量
        
        Parameters:
        -----------
        ecg_signal : array
            原始ECG信号
        r_peaks : array
            检测到的R波位置
        sampling_rate : float
            采样率
            
        Returns:
        --------
        quality_score : float
            质量评分 (0-1)
        """
        if len(r_peaks) < 2:
            return 0.0
        
        # 1. R-R间隔一致性（心率变异性）
        rr_intervals = np.diff(r_peaks) / sampling_rate
        if len(rr_intervals) == 0:
            return 0.0
            
        rr_cv = np.std(rr_intervals) / np.mean(rr_intervals) if np.mean(rr_intervals) > 0 else 1.0
        rr_quality = max(0, 1 - rr_cv * 2)  # 变异系数越小质量越高
        
        # 2. 心率合理性检查
        mean_hr = 60 / np.mean(rr_intervals) if np.mean(rr_intervals) > 0 else 0
        if 50 <= mean_hr <= 120:
            hr_quality = 1.0
        elif 40 <= mean_hr <= 140:
            hr_quality = 0.7
        else:
            hr_quality = 0.3
        
        # 3. R波幅度一致性
        r_amplitudes = []
        for peak in r_peaks:
            if 0 <= peak < len(ecg_signal):
                r_amplitudes.append(abs(ecg_signal[peak]))
        
        if len(r_amplitudes) > 0:
            amp_cv = np.std(r_amplitudes) / np.mean(r_amplitudes)
            amp_quality = max(0, 1 - amp_cv)
        else:
            amp_quality = 0
        
        # 4. 数量充足性
        count_quality = min(1.0, len(r_peaks) / 50)  # 50个以上为满分
        
        # 5. R-R间隔分布合理性
        valid_rr_ratio = np.sum((rr_intervals >= 0.4) & (rr_intervals <= 2.0)) / len(rr_intervals)
        rr_dist_quality = valid_rr_ratio
        
        # 综合质量评分
        quality_score = (rr_quality * 0.3 + hr_quality * 0.2 + 
                        amp_quality * 0.2 + count_quality * 0.15 + 
                        rr_dist_quality * 0.15)
        
        return quality_score

    def _validate_r_wave_alignment(self, ecg_signal, r_peaks, sampling_rate):
        """
        验证R波对齐的准确性 - 确保0ms位置对齐到真实R波峰值
        
        Parameters:
        -----------
        ecg_signal : array
            ECG信号
        r_peaks : array
            检测到的R波位置
        sampling_rate : float
            采样率
            
        Returns:
        --------
        validated_peaks : array
            验证后的R波位置
        """
        print(f"      🔍 验证R波对齐准确性...")
        
        validated_peaks = []
        search_window = int(0.05 * sampling_rate)  # ±50ms搜索窗口
        
        # 改进的R波极性判断 - 基于整体信号统计
        # 分析整个信号的极性特征
        signal_max = np.max(ecg_signal)
        signal_min = np.min(ecg_signal)
        signal_abs_max = max(abs(signal_max), abs(signal_min))

        # 基于信号统计判断主要极性
        if abs(signal_min) > abs(signal_max):
            is_negative_r_wave = True
            polarity_confidence = abs(signal_min) / signal_abs_max
            print(f"      📊 R波极性: 负向 (置信度: {polarity_confidence:.3f})")
        else:
            is_negative_r_wave = False
            polarity_confidence = abs(signal_max) / signal_abs_max
            print(f"      📊 R波极性: 正向 (置信度: {polarity_confidence:.3f})")

        # 验证NeuroKit2检测的峰值是否符合预期极性
        sample_peaks = r_peaks[:min(10, len(r_peaks))]
        nk_polarity_scores = []

        for peak in sample_peaks:
            if 0 <= peak < len(ecg_signal):
                nk_polarity_scores.append(ecg_signal[peak])

        if len(nk_polarity_scores) > 0:
            nk_mean_polarity = np.mean(nk_polarity_scores)
            nk_is_negative = nk_mean_polarity < 0

            print(f"      📊 NeuroKit2检测极性: {'负向' if nk_is_negative else '正向'} (平均值: {nk_mean_polarity:.2f})")

            # 如果NeuroKit2的检测极性与信号统计不符，需要重新对齐
            if is_negative_r_wave != nk_is_negative:
                print(f"      ⚠️ 检测极性不符，需要重新对齐到正确的极性")
                # 使用信号统计的极性判断为准
            else:
                print(f"      ✅ 检测极性与信号统计一致")
        
        # 精确对齐每个R波 - 强制使用正确的极性
        alignment_corrections = []

        for peak in r_peaks:
            start_idx = max(0, peak - search_window)
            end_idx = min(len(ecg_signal), peak + search_window)

            if start_idx >= end_idx:
                continue

            search_segment = ecg_signal[start_idx:end_idx]

            # 强制根据信号统计确定的极性寻找真实峰值
            if is_negative_r_wave:
                # 负向R波：寻找最小值（最负的点）
                local_peak_idx = np.argmin(search_segment)
            else:
                # 正向R波：寻找最大值
                local_peak_idx = np.argmax(search_segment)

            true_peak = start_idx + local_peak_idx
            correction = true_peak - peak
            alignment_corrections.append(correction)
            validated_peaks.append(true_peak)
        
        validated_peaks = np.array(validated_peaks)
        
        # 统计对齐修正情况
        if len(alignment_corrections) > 0:
            mean_correction = np.mean(alignment_corrections)
            max_correction = np.max(np.abs(alignment_corrections))
            print(f"      📊 对齐修正: 平均{mean_correction:.1f}样本点, 最大{max_correction:.1f}样本点")
            
            if max_correction > search_window * 0.5:
                print(f"      ⚠️ 检测到较大的对齐偏差，建议检查信号质量")
        
        print(f"      ✅ R波对齐验证完成: {len(validated_peaks)}个有效R波")
        return validated_peaks

    def extract_hep_epochs(self, eeg_data, r_peaks, sampling_rate, ch_names=None):
        """
        提取HEP时期数据 - 使用验证后的R波位置
        
        Parameters:
        -----------
        eeg_data : array
            EEG数据 (channels x samples)
        r_peaks : array
            验证后的R波位置
        sampling_rate : float
            采样率
        ch_names : list, optional
            通道名称列表
            
        Returns:
        --------
        hep_epochs : array
            HEP时期数据 (epochs x channels x samples)
        times : array
            时间轴
        ch_names : list
            通道名称列表
        """
        print(f"    🔍 提取HEP epochs (使用NeuroKit2验证的R波位置)...")
        print(f"      EEG数据形状: {eeg_data.shape}")
        print(f"      验证后R波数量: {len(r_peaks)}")
        
        # 计算时间窗口对应的样本点
        pre_samples = int(abs(self.hep_params['time_window'][0]) * sampling_rate)
        post_samples = int(self.hep_params['time_window'][1] * sampling_rate)
        total_samples = pre_samples + post_samples
        
        # 创建时间轴
        times = np.linspace(self.hep_params['time_window'][0],
                           self.hep_params['time_window'][1],
                           total_samples)
        
        # 如果没有提供通道名称，创建默认的通道名称
        if ch_names is None:
            ch_names = [f'EEG{i+1:02d}' for i in range(eeg_data.shape[0])]
        
        # 提取每个心跳周期的EEG数据
        valid_epochs = []
        invalid_count = 0
        
        for i, r_peak in enumerate(r_peaks):
            start_idx = r_peak - pre_samples
            end_idx = r_peak + post_samples
            
            # 检查边界条件
            if start_idx < 0 or end_idx >= eeg_data.shape[1]:
                invalid_count += 1
                continue
            
            # 提取epoch
            epoch = eeg_data[:, start_idx:end_idx]
            
            # 基线校正
            baseline_start_time = self.hep_params['baseline_window'][0] - self.hep_params['time_window'][0]
            baseline_end_time = self.hep_params['baseline_window'][1] - self.hep_params['time_window'][0]
            
            baseline_start = int(baseline_start_time * sampling_rate)
            baseline_end = int(baseline_end_time * sampling_rate)
            
            if (baseline_start >= 0 and baseline_end <= epoch.shape[1] and 
                baseline_start < baseline_end):
                baseline_mean = np.mean(epoch[:, baseline_start:baseline_end], axis=1, keepdims=True)
                epoch_corrected = epoch - baseline_mean
                valid_epochs.append(epoch_corrected)
            else:
                invalid_count += 1
        
        print(f"      ✅ 成功提取: {len(valid_epochs)}个有效epochs")
        print(f"      ⚠️ 边界无效: {invalid_count}个epochs")
        
        if len(valid_epochs) > 0:
            hep_epochs = np.array(valid_epochs)
            print(f"      📊 最终HEP数据形状: {hep_epochs.shape}")
            return hep_epochs, times, ch_names
        else:
            raise ValueError("未提取到任何有效的HEP epochs")

    def analyze_single_file(self, file_info):
        """
        使用NeuroKit2分析单个文件
        
        Parameters:
        -----------
        file_info : dict
            文件信息字典
            
        Returns:
        --------
        result : dict
            分析结果
        """
        print(f"    📁 文件: {file_info['file_name']}")
        
        # 读取数据
        raw = mne.io.read_raw_fif(file_info['file_path'], preload=True, verbose=False)
        
        # 获取基本信息
        sampling_rate = raw.info['sfreq']
        ch_names = raw.ch_names
        data = raw.get_data() * 1000  # mV -> μV
        
        print(f"    📊 数据形状: {data.shape}, 采样率: {sampling_rate} Hz")
        
        # 分离EEG和ECG数据
        eeg_data = data[:61, :]  # 前61个通道是EEG
        ecg_data = data[61:, :]  # 后58个通道是ECG
        
        # 使用NeuroKit2检测R波
        r_peaks, quality_info = self.detect_r_waves_neurokit2(ecg_data, sampling_rate)
        
        if len(r_peaks) < self.hep_params['min_heartbeats']:
            return {
                'success': False,
                'error': f"R波数量不足: {len(r_peaks)} < {self.hep_params['min_heartbeats']}",
                'r_peaks_count': len(r_peaks),
                'quality_info': quality_info
            }
        
        # 提取HEP时期
        hep_epochs, times, ch_names_used = self.extract_hep_epochs(
            eeg_data, r_peaks, sampling_rate, ch_names[:61]
        )
        
        return {
            'success': True,
            'hep_epochs': hep_epochs,
            'times': times,
            'channel_names': ch_names_used,
            'file_info': file_info,
            'r_peaks_count': len(r_peaks),
            'quality_info': quality_info,
            'neurokit_method': quality_info['method'],
            'alignment_validated': quality_info.get('alignment_validated', False)
        }

if __name__ == "__main__":
    # 使用示例
    data_dir = "D:/ecgeeg/18-eegecg手动预处理5-重参考2-双侧乳突"
    
    # 创建NeuroKit2修复版分析器
    analyzer = NeuroKit2FixedHEPAnalyzer(data_dir, enable_quality_control=False)
    
    print("🚀 NeuroKit2修复版HEP分析器准备就绪")
    print("🎯 主要修复:")
    print("   ✅ 使用NeuroKit2专业R波检测")
    print("   ✅ 修复R波极性判断错误")
    print("   ✅ 确保0ms位置正确对齐")
    print("   ✅ 自动选择最佳检测算法")
