#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试专业NeuroKit2 HEP分析器

这个脚本将：
1. 使用专业NeuroKit2方法分析ECG和HEP
2. 测试多种NeuroKit2检测算法
3. 生成专业的HEP可视化
4. 验证R波对齐的准确性

作者: HEP Analysis Team
日期: 2024-12-19
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import mne
import warnings
warnings.filterwarnings('ignore')

# 导入专业NeuroKit2分析器
import sys
import importlib.util
spec = importlib.util.spec_from_file_location("professional_analyzer", "10_professional_neurokit2_hep_analyzer.py")
professional_analyzer = importlib.util.module_from_spec(spec)
spec.loader.exec_module(professional_analyzer)
ProfessionalNeuroKit2HEPAnalyzer = professional_analyzer.ProfessionalNeuroKit2HEPAnalyzer

# 导入NeuroKit2
try:
    import neurokit2 as nk
    NEUROKIT_AVAILABLE = True
except ImportError:
    NEUROKIT_AVAILABLE = False
    raise ImportError("请先安装NeuroKit2")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def test_multiple_neurokit2_methods():
    """测试多种NeuroKit2检测方法"""
    print("🧪 测试多种NeuroKit2专业检测方法")
    print("="*60)
    
    # 初始化分析器
    data_dir = "D:/ecgeeg/18-eegecg手动预处理5-重参考2-双侧乳突"
    analyzer = ProfessionalNeuroKit2HEPAnalyzer(data_dir, enable_quality_control=False)
    
    # 查找测试文件
    test_files = list(Path(data_dir).rglob("*_test_*.fif"))
    if not test_files:
        print("❌ 未找到测试文件")
        return None
    
    test_file = test_files[0]
    print(f"📁 测试文件: {test_file.name}")
    
    # 读取ECG数据
    raw = mne.io.read_raw_fif(test_file, preload=True, verbose=False)
    data = raw.get_data() * 1000
    sampling_rate = raw.info['sfreq']
    ecg_data = data[61:, :]
    ecg11_signal = ecg_data[10, :]
    
    # 测试多种NeuroKit2方法
    methods = ['neurokit', 'pantompkins1985', 'hamilton2002', 'christov2004', 'engzeemod2012']
    results = {}
    
    for method in methods:
        try:
            print(f"\n🔍 测试方法: {method}")
            
            # 使用NeuroKit2处理
            signals, info = nk.ecg_process(
                ecg11_signal,
                sampling_rate=sampling_rate,
                method=method
            )
            
            r_peaks = info["ECG_R_Peaks"]
            
            if len(r_peaks) > 1:
                # 计算心率统计
                rr_intervals = np.diff(r_peaks) / sampling_rate
                heart_rates = 60 / rr_intervals
                hr_mean = np.mean(heart_rates)
                hr_std = np.std(heart_rates)
                
                results[method] = {
                    'r_peaks': r_peaks,
                    'count': len(r_peaks),
                    'heart_rate_mean': hr_mean,
                    'heart_rate_std': hr_std,
                    'signals': signals,
                    'info': info,
                    'success': True
                }
                
                print(f"    ✅ 成功: {len(r_peaks)}个R波, 心率: {hr_mean:.1f}±{hr_std:.1f} BPM")
            else:
                print(f"    ❌ 失败: 检测到的R波数量不足")
                results[method] = {'success': False}
                
        except Exception as e:
            print(f"    ❌ 错误: {e}")
            results[method] = {'success': False, 'error': str(e)}
    
    return {
        'results': results,
        'ecg_signal': ecg11_signal,
        'sampling_rate': sampling_rate,
        'test_file': test_file
    }

def analyze_with_best_method(test_data):
    """使用最佳方法进行完整分析"""
    print(f"\n🎯 使用最佳方法进行完整HEP分析")
    print("="*50)
    
    # 选择最佳方法（基于R波数量和心率合理性）
    best_method = None
    best_score = 0
    
    for method, result in test_data['results'].items():
        if result.get('success', False):
            # 计算评分：R波数量 + 心率合理性
            count_score = min(1.0, result['count'] / 200)  # 200个R波为满分
            hr_score = 1.0 if 50 <= result['heart_rate_mean'] <= 120 else 0.5
            total_score = count_score * 0.6 + hr_score * 0.4
            
            if total_score > best_score:
                best_score = total_score
                best_method = method
    
    if best_method is None:
        print("❌ 未找到合适的检测方法")
        return None
    
    print(f"🏆 最佳方法: {best_method} (评分: {best_score:.3f})")
    
    # 使用最佳方法进行完整分析
    data_dir = "D:/ecgeeg/18-eegecg手动预处理5-重参考2-双侧乳突"
    analyzer = ProfessionalNeuroKit2HEPAnalyzer(data_dir, enable_quality_control=False)
    
    # 更新分析器的默认方法
    analyzer.nk_params['method'] = best_method
    
    # 构建文件信息
    file_info = {
        'file_path': str(test_data['test_file']),
        'file_name': test_data['test_file'].name,
        'subject_id': test_data['test_file'].stem.split('_')[0],
        'condition': 'test_1'
    }
    
    # 进行完整分析
    result = analyzer.analyze_single_file_professional(file_info)
    
    if not result['success']:
        print(f"❌ 分析失败: {result['error']}")
        return None
    
    print(f"✅ 分析成功!")
    print(f"📊 分析结果:")
    print(f"   - R波数量: {result['r_peaks_count']}")
    print(f"   - HEP epochs: {result['hep_epochs'].shape}")
    print(f"   - 质量评分: {result['quality_metrics']['quality_score']:.3f}")
    print(f"   - 质量等级: {result['quality_metrics']['quality_level']}")
    print(f"   - 平均心率: {result['quality_metrics']['heart_rate_mean']:.1f} BPM")
    
    return result

def create_professional_visualization(analysis_result, method_comparison, save_dir):
    """创建专业的可视化"""
    print(f"\n📊 生成专业可视化...")
    
    # 创建多子图
    fig = plt.figure(figsize=(16, 12))
    
    # 1. ECG信号和R波检测结果对比
    ax1 = plt.subplot(3, 2, (1, 2))
    
    ecg_signal = analysis_result['ecg_signal']
    sampling_rate = analysis_result['sampling_rate']
    time_axis = np.arange(len(ecg_signal)) / sampling_rate
    
    # 只显示前60秒
    display_duration = 60
    display_samples = int(display_duration * sampling_rate)
    
    if len(ecg_signal) > display_samples:
        ecg_display = ecg_signal[:display_samples]
        time_display = time_axis[:display_samples]
    else:
        ecg_display = ecg_signal
        time_display = time_axis
    
    # 绘制ECG信号
    ax1.plot(time_display, ecg_display, 'k-', linewidth=0.8, alpha=0.7, label='ECG11信号')
    
    # 标记R波位置
    r_peaks = analysis_result['r_peaks']
    r_peaks_display = r_peaks[r_peaks < display_samples]
    
    if len(r_peaks_display) > 0:
        r_times = r_peaks_display / sampling_rate
        r_amplitudes = ecg_display[r_peaks_display]
        ax1.scatter(r_times, r_amplitudes, c='red', s=50, marker='o', 
                   alpha=0.8, zorder=5, label=f'R波 (n={len(r_peaks_display)})')
        
        # 添加垂直线
        for r_time in r_times:
            ax1.axvline(r_time, color='red', alpha=0.3, linestyle='--', linewidth=1)
    
    ax1.set_title(f'NeuroKit2专业ECG分析 - 方法: {analysis_result["quality_metrics"]["quality_level"]}', 
                 fontsize=12, fontweight='bold')
    ax1.set_xlabel('时间 (秒)')
    ax1.set_ylabel('幅度 (μV)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 方法对比柱状图
    ax2 = plt.subplot(3, 2, 3)
    
    methods = []
    counts = []
    colors = []
    
    for method, result in method_comparison['results'].items():
        if result.get('success', False):
            methods.append(method)
            counts.append(result['count'])
            colors.append('green' if method == analysis_result['quality_metrics'].get('method', '') else 'blue')
    
    if methods:
        bars = ax2.bar(methods, counts, color=colors, alpha=0.7)
        ax2.set_title('不同方法检测的R波数量对比', fontweight='bold')
        ax2.set_ylabel('R波数量')
        ax2.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, count in zip(bars, counts):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                    str(count), ha='center', va='bottom')
    
    # 3. 心率分布
    ax3 = plt.subplot(3, 2, 4)
    
    if len(r_peaks) > 1:
        rr_intervals = np.diff(r_peaks) / sampling_rate
        heart_rates = 60 / rr_intervals
        
        ax3.hist(heart_rates, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax3.axvline(np.mean(heart_rates), color='red', linestyle='--', 
                   label=f'平均: {np.mean(heart_rates):.1f} BPM')
        ax3.set_title('心率分布', fontweight='bold')
        ax3.set_xlabel('心率 (BPM)')
        ax3.set_ylabel('频次')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
    
    # 4. HEP波形
    ax4 = plt.subplot(3, 2, (5, 6))
    
    hep_epochs = analysis_result['hep_epochs']
    times = analysis_result['times']
    
    # 选择前几个电极的平均HEP
    mean_hep = np.mean(hep_epochs, axis=0)
    representative_channels = slice(0, 5)  # 前5个电极
    
    for i in range(5):
        if i < mean_hep.shape[0]:
            ax4.plot(times * 1000, mean_hep[i, :], alpha=0.7, 
                    label=f'EEG{i+1:02d}', linewidth=1.5)
    
    # 标记重要时间点
    ax4.axvline(0, color='red', linestyle='--', linewidth=2, alpha=0.8, label='R波 (0ms)')
    ax4.axvspan(455, 595, alpha=0.15, color='green', label='HEP窗口 (455-595ms)')
    
    ax4.set_title(f'平均HEP波形 (n={len(hep_epochs)} epochs)', fontweight='bold')
    ax4.set_xlabel('时间 (ms)')
    ax4.set_ylabel('幅度 (μV)')
    ax4.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图像
    save_path = save_dir / "professional_neurokit2_analysis.png"
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ 专业分析图已保存: {save_path}")
    
    return fig

def main():
    """主函数"""
    print("🚀 专业NeuroKit2 HEP分析器测试")
    print("="*60)
    
    # 1. 测试多种NeuroKit2方法
    method_comparison = test_multiple_neurokit2_methods()
    if method_comparison is None:
        return
    
    # 2. 使用最佳方法进行完整分析
    analysis_result = analyze_with_best_method(method_comparison)
    if analysis_result is None:
        return
    
    # 3. 生成专业可视化
    save_dir = Path("D:/ecgeeg/30-数据分析/5-HBA/result/professional_neurokit2")
    save_dir.mkdir(parents=True, exist_ok=True)
    
    create_professional_visualization(analysis_result, method_comparison, save_dir)
    
    # 4. 生成总结报告
    print(f"\n📋 专业NeuroKit2分析报告")
    print("="*50)
    
    # 方法对比
    print(f"🔍 方法对比结果:")
    for method, result in method_comparison['results'].items():
        if result.get('success', False):
            print(f"   {method}: {result['count']}个R波, "
                  f"心率: {result['heart_rate_mean']:.1f}±{result['heart_rate_std']:.1f} BPM")
        else:
            print(f"   {method}: 失败")
    
    # 最终分析结果
    print(f"\n📊 最终分析结果:")
    print(f"   ✅ 使用方法: 专业NeuroKit2")
    print(f"   📊 R波数量: {analysis_result['r_peaks_count']}")
    print(f"   📊 HEP epochs: {analysis_result['hep_epochs'].shape[0]}")
    print(f"   📊 质量评分: {analysis_result['quality_metrics']['quality_score']:.3f}")
    print(f"   📊 质量等级: {analysis_result['quality_metrics']['quality_level']}")
    print(f"   📊 平均心率: {analysis_result['quality_metrics']['heart_rate_mean']:.1f} BPM")
    print(f"   📊 心率变异: {analysis_result['quality_metrics']['heart_rate_cv']:.3f}")
    
    print(f"\n🎉 专业分析完成!")
    print(f"📁 结果保存在: {save_dir}")
    print(f"\n💡 结论:")
    print(f"   ✅ NeuroKit2专业方法成功处理标准ECG导联")
    print(f"   ✅ R波检测质量: {analysis_result['quality_metrics']['quality_level']}")
    print(f"   ✅ 适合用于HEP分析")

if __name__ == "__main__":
    main()
