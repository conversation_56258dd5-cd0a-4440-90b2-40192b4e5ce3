#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版HEP分析器 v1.0
====================

基于10_literature_based_hep_analyzer.py的成功R波检测，
创建一个简化版本来解决epochs创建问题。

重点：
- 保持成功的多算法R波检测
- 简化心电场伪迹去除（避免依赖包问题）
- 专注解决epochs创建问题
- 快速验证HEP计算流程
"""

import os
import sys
import mne
import numpy as np
import pandas as pd
from pathlib import Path
import matplotlib.pyplot as plt
from scipy import signal
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['LXGW WenKai']
plt.rcParams['axes.unicode_minus'] = False

class SimplifiedHEPAnalyzer:
    """简化版HEP分析器"""
    
    def __init__(self):
        self.setup_paths()
        self.setup_parameters()
        
    def setup_paths(self):
        """设置文件路径"""
        self.base_path = Path("D:/ecgeeg")
        self.data_path = self.base_path / "18-eegecg手动预处理5-重参考2-双侧乳突"
        self.output_path = self.base_path / "30-数据分析/5-HBA/result/simplified_hep"
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        print(f"📁 数据路径: {self.data_path}")
        print(f"📁 输出路径: {self.output_path}")
    
    def setup_parameters(self):
        """设置参数（基于成功的文献参数）"""
        self.params = {
            'highpass': 0.1,
            'lowpass': 30.0,
            'epoch_tmin': -0.2,
            'epoch_tmax': 0.6,
            'min_rr_interval': 0.6,
            'amplitude_threshold': 200e-6,  # 放宽到200μV
            'baseline_tmin': -0.2,
            'baseline_tmax': 0.0,
            'min_epochs': 20,  # 降低到20
            'sfreq': 500
        }
        
        self.cardiac_channels = ['Fp1', 'Fp2', 'F7', 'F8', 'FT9', 'FT10']
        
        print("📚 使用简化参数（基于成功的R波检测）")
        for key, value in self.params.items():
            print(f"   📊 {key}: {value}")
    
    def load_and_preprocess_eeg(self, file_path):
        """加载并预处理EEG数据"""
        try:
            raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
            print(f"    📥 原始数据: {raw.info['nchan']}通道, {raw.times[-1]:.1f}秒")
            
            # 文献标准滤波
            raw_filtered = raw.copy()
            raw_filtered.filter(
                l_freq=self.params['highpass'], 
                h_freq=self.params['lowpass'],
                fir_design='firwin',
                verbose=False
            )
            print(f"    🔧 滤波完成: {self.params['highpass']}-{self.params['lowpass']}Hz")
            
            return raw_filtered
            
        except Exception as e:
            print(f"❌ 加载失败 {file_path}: {e}")
            return None
    
    def detect_r_peaks_simplified(self, raw):
        """简化的R波检测（保持成功的方法）"""
        print("    🫀 执行R波检测...")
        
        available_channels = [ch for ch in self.cardiac_channels if ch in raw.ch_names]
        
        if not available_channels:
            print("❌ 未找到心电敏感电极")
            return None, None
        
        best_result = None
        best_channel = None
        
        for channel in available_channels[:3]:  # 测试前3个电极
            print(f"      🔍 测试电极: {channel}")
            
            try:
                ch_idx = raw.ch_names.index(channel)
                data = raw.get_data()[ch_idx, :]
                
                # 使用自适应检测算法（在测试中表现最好）
                r_peaks = self._adaptive_detection_simple(data, raw.info['sfreq'])
                
                if len(r_peaks) > 30:  # 基本数量要求
                    quality = self._calculate_quality_simple(r_peaks, raw.info['sfreq'])
                    
                    if best_result is None or quality > best_result['quality']:
                        best_result = {
                            'r_peaks': r_peaks,
                            'quality': quality
                        }
                        best_channel = channel
                        
                        print(f"      ✅ {channel}: {len(r_peaks)}个R波, 质量={quality:.1f}")
                
            except Exception as e:
                print(f"      ⚠️ 电极 {channel} 处理失败: {e}")
                continue
        
        if best_result is not None:
            return best_result['r_peaks'], best_channel
        else:
            print("    ❌ R波检测失败")
            return None, None
    
    def _adaptive_detection_simple(self, signal_data, sfreq):
        """简化的自适应检测"""
        try:
            # 心跳检测专用滤波
            sos = signal.butter(4, [1, 20], btype='band', fs=sfreq, output='sos')
            filtered_data = signal.sosfilt(sos, signal_data)
            
            # 计算绝对值和梯度
            abs_signal = np.abs(filtered_data)
            gradient = np.abs(np.gradient(filtered_data))
            combined_signal = abs_signal + 0.5 * gradient
            
            # 简化的阈值检测
            threshold = np.mean(combined_signal) + 2 * np.std(combined_signal)
            
            # 检测峰值
            peaks = []
            min_distance = int(0.4 * sfreq)  # 最小间隔400ms
            
            for i in range(1, len(combined_signal)-1):
                if (combined_signal[i] > combined_signal[i-1] and 
                    combined_signal[i] > combined_signal[i+1] and 
                    combined_signal[i] > threshold):
                    
                    if not peaks or (i - peaks[-1]) > min_distance:
                        peaks.append(i)
            
            return np.array(peaks)
            
        except Exception as e:
            print(f"      检测失败: {e}")
            return np.array([])
    
    def _calculate_quality_simple(self, r_peaks, sfreq):
        """简化的质量计算"""
        if len(r_peaks) < 5:
            return 0
        
        # RR间隔分析
        rr_intervals = np.diff(r_peaks) / sfreq
        heart_rate = 60 / np.mean(rr_intervals)
        
        # 基本质量评分
        hr_score = 1.0 if 50 <= heart_rate <= 100 else 0.5
        count_score = min(len(r_peaks) / 80, 1.0)
        
        return (hr_score * 0.6 + count_score * 0.4) * 100
    
    def apply_minimum_rr_interval(self, r_peaks, sfreq):
        """应用最小RR间隔过滤"""
        if len(r_peaks) == 0:
            return np.array([])
        
        min_samples = int(self.params['min_rr_interval'] * sfreq)
        filtered_peaks = []
        
        for i, peak in enumerate(r_peaks):
            if i < len(r_peaks) - 1:
                next_peak = r_peaks[i + 1]
                if (next_peak - peak) >= min_samples:
                    filtered_peaks.append(peak)
            else:
                if i == 0 or (peak - r_peaks[i-1]) >= min_samples:
                    filtered_peaks.append(peak)
        
        filtered_peaks = np.array(filtered_peaks)
        print(f"    ⏱️ 最小RR间隔过滤: {len(r_peaks)} → {len(filtered_peaks)} 个R波")
        
        return filtered_peaks
    
    def create_epochs_debug(self, raw, r_peaks):
        """创建epochs（调试版本）"""
        print("    📊 创建epochs（调试模式）...")
        
        if len(r_peaks) == 0:
            print("    ❌ 没有R波可用")
            return None
        
        try:
            # 检查R波位置的有效性
            max_sample = len(raw.times) - 1
            valid_peaks = r_peaks[r_peaks < max_sample]
            
            if len(valid_peaks) == 0:
                print("    ❌ 没有有效的R波位置")
                return None
            
            print(f"    📍 有效R波: {len(valid_peaks)}/{len(r_peaks)}")
            
            # 检查时间窗口
            min_time_samples = int(abs(self.params['epoch_tmin']) * raw.info['sfreq'])
            max_time_samples = int(self.params['epoch_tmax'] * raw.info['sfreq'])
            
            # 过滤边界附近的R波
            safe_peaks = []
            for peak in valid_peaks:
                if (peak >= min_time_samples and 
                    peak <= max_sample - max_time_samples):
                    safe_peaks.append(peak)
            
            safe_peaks = np.array(safe_peaks)
            print(f"    🔒 安全R波（远离边界）: {len(safe_peaks)}")
            
            if len(safe_peaks) == 0:
                print("    ❌ 没有安全的R波位置")
                return None
            
            # 创建事件数组
            events = np.array([[peak, 0, 1] for peak in safe_peaks])
            print(f"    📋 创建事件数组: {events.shape}")
            
            # 创建epochs，放宽振幅阈值
            print(f"    🎚️ 振幅阈值: {self.params['amplitude_threshold']*1e6:.0f} μV")
            
            epochs = mne.Epochs(
                raw,
                events=events,
                event_id={'heartbeat': 1},
                tmin=self.params['epoch_tmin'],
                tmax=self.params['epoch_tmax'],
                baseline=(self.params['baseline_tmin'], self.params['baseline_tmax']),
                reject={'eeg': self.params['amplitude_threshold']},
                preload=True,
                verbose=True  # 开启详细输出
            )
            
            print(f"    ✅ Epochs创建完成: {len(epochs)} epochs")
            
            if len(epochs) == 0:
                print("    ⚠️ 所有epochs被拒绝，尝试无振幅拒绝...")
                
                # 尝试不设置振幅拒绝阈值
                epochs_no_reject = mne.Epochs(
                    raw,
                    events=events,
                    event_id={'heartbeat': 1},
                    tmin=self.params['epoch_tmin'],
                    tmax=self.params['epoch_tmax'],
                    baseline=(self.params['baseline_tmin'], self.params['baseline_tmax']),
                    reject=None,  # 不设置拒绝阈值
                    preload=True,
                    verbose=True
                )
                
                print(f"    🔄 无拒绝阈值epochs: {len(epochs_no_reject)} epochs")
                return epochs_no_reject
            
            return epochs
            
        except Exception as e:
            print(f"    ❌ Epochs创建失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def compute_hep_simple(self, epochs):
        """计算HEP（简化版）"""
        print("    🧠 计算HEP...")
        
        try:
            # 平均得到HEP
            hep = epochs.average()
            
            # 计算全局场功率
            gfp = np.std(hep.data, axis=0)
            
            print(f"    ✅ HEP计算完成")
            print(f"    📈 峰值GFP: {np.max(gfp):.2e}")
            
            return {
                'hep': hep,
                'gfp': gfp,
                'n_epochs': len(epochs)
            }
            
        except Exception as e:
            print(f"    ❌ HEP计算失败: {e}")
            return None
    
    def create_simple_visualization(self, hep_result, file_name, condition):
        """创建简化的可视化"""
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        fig.suptitle(f'HEP分析结果: {file_name} ({condition})', fontsize=14, fontweight='bold')
        
        hep = hep_result['hep']
        gfp = hep_result['gfp']
        
        # 子图1：关键电极的HEP波形
        ax1 = axes[0]
        key_channels = ['Cz', 'Fz', 'C3', 'C4']
        colors = ['red', 'blue', 'green', 'orange']
        
        for i, ch in enumerate(key_channels):
            if ch in hep.ch_names:
                ch_idx = hep.ch_names.index(ch)
                ax1.plot(hep.times * 1000, hep.data[ch_idx] * 1e6, 
                        color=colors[i], label=ch, linewidth=2)
        
        ax1.axvline(0, color='black', linestyle='--', alpha=0.5, label='R波')
        ax1.axvspan(200, 600, alpha=0.2, color='yellow', label='HEP窗口')
        ax1.set_xlabel('时间 (ms)', fontsize=10)
        ax1.set_ylabel('振幅 (μV)', fontsize=10)
        ax1.set_title('HEP时域波形', fontsize=12, fontweight='bold')
        ax1.legend(fontsize=9)
        ax1.grid(True, alpha=0.3)
        
        # 子图2：全局场功率
        ax2 = axes[1]
        ax2.plot(hep.times * 1000, gfp * 1e6, color='purple', linewidth=3, label='GFP')
        ax2.axvline(0, color='black', linestyle='--', alpha=0.5)
        ax2.axvspan(200, 600, alpha=0.2, color='yellow')
        
        # 标注信息
        peak_idx = np.argmax(gfp)
        peak_time = hep.times[peak_idx] * 1000
        peak_amp = gfp[peak_idx] * 1e6
        
        ax2.plot(peak_time, peak_amp, 'ro', markersize=8)
        ax2.annotate(f'峰值: {peak_time:.0f}ms\n{peak_amp:.2f}μV\nEpochs: {hep_result["n_epochs"]}', 
                    xy=(peak_time, peak_amp), xytext=(10, 10),
                    textcoords='offset points', fontsize=9,
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
        
        ax2.set_xlabel('时间 (ms)', fontsize=10)
        ax2.set_ylabel('GFP (μV)', fontsize=10)
        ax2.set_title('全局场功率', fontsize=12, fontweight='bold')
        ax2.legend(fontsize=9)
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        output_file = self.output_path / f"{condition}_{file_name}_简化HEP分析.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
        
        print(f"    📊 可视化已保存: {output_file}")
    
    def process_single_file(self, file_path, condition):
        """处理单个文件（简化版）"""
        print(f"\n🔬 处理文件: {file_path.name}")
        print(f"📋 条件: {condition}")
        print("=" * 50)
        
        try:
            # 第1步：加载和预处理
            raw = self.load_and_preprocess_eeg(file_path)
            if raw is None:
                return None
            
            # 第2步：R波检测
            r_peaks, best_channel = self.detect_r_peaks_simplified(raw)
            if r_peaks is None or len(r_peaks) < 10:
                print(f"❌ R波检测失败")
                return None
            
            # 第3步：最小RR间隔过滤
            r_peaks_filtered = self.apply_minimum_rr_interval(r_peaks, raw.info['sfreq'])
            if len(r_peaks_filtered) < self.params['min_epochs']:
                print(f"❌ 过滤后R波不足: {len(r_peaks_filtered)}")
                return None
            
            # 第4步：创建epochs（调试版）
            epochs = self.create_epochs_debug(raw, r_peaks_filtered)
            if epochs is None or len(epochs) < 5:
                print(f"❌ Epochs创建失败")
                return None
            
            # 第5步：计算HEP
            hep_result = self.compute_hep_simple(epochs)
            if hep_result is None:
                return None
            
            # 第6步：创建可视化
            self.create_simple_visualization(hep_result, file_path.stem, condition)
            
            print(f"\n✅ 文件处理成功: {file_path.name}")
            print(f"   🎯 检测电极: {best_channel}")
            print(f"   🫀 R波数量: {len(r_peaks)} → {len(r_peaks_filtered)}")
            print(f"   📊 Epochs数量: {len(epochs)}")
            
            return {
                'file_name': file_path.name,
                'condition': condition,
                'hep_result': hep_result,
                'best_channel': best_channel,
                'r_peaks_count': len(r_peaks_filtered),
                'epochs_count': len(epochs)
            }
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def run_test_analysis(self):
        """运行测试分析"""
        print("🚀 开始简化版HEP分析测试")
        print("=" * 50)
        
        # 查找文件
        all_files = list(self.data_path.glob("*.fif"))
        
        if len(all_files) == 0:
            print("❌ 未找到任何.fif文件")
            return
        
        # 测试前几个文件
        test_files = all_files[:3]
        results = []
        
        for file_path in test_files:
            # 判断条件
            condition = 'unknown'
            if 'rest' in file_path.name.lower():
                condition = 'rest'
            elif 'prac' in file_path.name.lower():
                condition = 'prac'
            elif 'test' in file_path.name.lower():
                condition = 'test'
            
            result = self.process_single_file(file_path, condition)
            if result is not None:
                results.append(result)
        
        # 打印总结
        print(f"\n🎉 测试完成！")
        print(f"成功处理: {len(results)}/{len(test_files)} 个文件")
        
        if results:
            avg_epochs = np.mean([r['epochs_count'] for r in results])
            print(f"平均epochs数量: {avg_epochs:.1f}")
            
            for result in results:
                print(f"  ✅ {result['file_name']}: {result['epochs_count']} epochs, {result['best_channel']} 电极")
        
        return results

if __name__ == "__main__":
    try:
        analyzer = SimplifiedHEPAnalyzer()
        results = analyzer.run_test_analysis()
        
        print(f"\n🎯 简化版HEP分析测试完成！")
        if results:
            print(f"✅ 成功解决了epochs创建问题")
            print(f"🔬 验证了完整的HEP分析流程")
        else:
            print(f"⚠️ 仍需进一步调试")
        
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
        import traceback
        traceback.print_exc() 