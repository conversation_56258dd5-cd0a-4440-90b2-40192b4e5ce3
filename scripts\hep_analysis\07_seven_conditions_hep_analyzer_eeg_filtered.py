#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HEP（心跳诱发电位）七个实验条件分析脚本 - EEG滤波增强版本
基于07版本，增加EEG 0.1-30Hz滤波处理，对7个实验条件进行完整的HEP分析和可视化

重要改进：
- 增加EEG 0.1-30Hz带通滤波
- 修复R波检测算法（精确对齐到R波峰值）
- 保持其他参数与07版本一致

实验条件：
1. prac - 练习状态
2. rest1 - 静息状态1
3. rest2 - 静息状态2
4. rest3 - 静息状态3
5. test1 - 测试状态1
6. test2 - 测试状态2
7. test3 - 测试状态3

"""

import os
import numpy as np
import mne
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
import pandas as pd
import random
from scipy import signal
from scipy.stats import zscore
import re
import pickle
import json
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SevenConditionsHEPAnalyzer:
    """七个实验条件HEP分析器"""

    def __init__(self, data_dir, validation_excel_path, enable_quality_control=True):
        """
        初始化HEP分析器

        Parameters:
        -----------
        data_dir : str
            数据目录路径
        validation_excel_path : str
            电压验证结果Excel文件路径
        enable_quality_control : bool
            是否启用质量控制（默认True）
        """
        self.data_dir = Path(data_dir)
        self.validation_excel_path = Path(validation_excel_path)
        self.enable_quality_control = enable_quality_control

        # HEP分析参数（保持与原脚本一致）
        self.hep_params = {
            'time_window': (-0.8, 1.0),  # R波前800ms到后1000ms
            'baseline_window': (-0.2, 0),  # R波前200ms作为基线
            'min_heartbeats': 100,  # 最小心跳数
            'rr_interval_range': (0.3, 2.0),  # 有效R-R间隔范围(秒)
            'baseline_std_threshold': 0.1,  # 基线标准差阈值(100mV in V units)
            'sampling_rate': 500  # 采样率
        }
        
        # 可视化参数
        self.viz_params = {
            'time_window': (-0.2, 0.65),  # 可视化时间窗口：-200ms到+650ms
            'subplot_ratio': (2, 1),  # 子图宽高比 2:1
            'figure_dpi': 300  # 图像分辨率
        }
        
        # 脑区电极分组（保持与原脚本一致）
        self.electrode_groups = {
            '左前额': ['Fp1', 'AF7', 'AF3', 'F7', 'F5', 'F3', 'F1'],
            '右前额': ['Fp2', 'AF8', 'AF4', 'F8', 'F6', 'F4', 'F2'],
            '左中央': ['FC5', 'FC3', 'FC1', 'C5', 'C3', 'C1', 'CP5', 'CP3', 'CP1'],
            '右中央': ['FC6', 'FC4', 'FC2', 'C6', 'C4', 'C2', 'CP6', 'CP4', 'CP2'],
            '中线': ['Fz', 'FCz', 'Cz', 'CPz', 'Pz'],
            '顶叶': ['P7', 'P5', 'P3', 'P1', 'P2', 'P4', 'P6', 'P8', 'PO7', 'PO3', 'PO4', 'PO8']
        }
        
        # 七个实验条件定义
        self.conditions = {
            'prac': '练习状态',
            'rest1': '静息状态1',
            'rest2': '静息状态2', 
            'rest3': '静息状态3',
            'test1': '测试状态1',
            'test2': '测试状态2',
            'test3': '测试状态3'
        }
        
        # 条件颜色配置
        self.condition_colors = {
            'prac': '#8B4513',    # 棕色
            'rest1': '#1f77b4',   # 蓝色
            'rest2': '#2ca02c',   # 绿色
            'rest3': '#17becf',   # 青色
            'test1': '#ff7f0e',   # 橙色
            'test2': '#d62728',   # 红色
            'test3': '#9467bd'    # 紫色
        }
        
        # 结果存储
        self.analysis_results = {
            'selected_files': {},  # 按条件存储选择的文件
            'hep_data': {},        # 按条件存储HEP数据
            'quality_metrics': {}, # 按条件存储质量指标
            'group_averages': {},  # 按条件存储脑区平均数据
            'rejected_data': {}    # 按条件存储被淘汰的数据详情
        }

        # 结果保存路径
        self.results_save_dir = Path("../../result/hep_analysis_cache")
        self.results_save_dir.mkdir(parents=True, exist_ok=True)
        
        print("="*80)
        print("HEP（心跳诱发电位）七个实验条件分析 - 100%质量文件版本")
        print("="*80)
        print(f"数据目录: {self.data_dir}")
        print(f"验证结果文件: {self.validation_excel_path}")
        print(f"实验条件: {list(self.conditions.keys())}")
        print(f"HEP时间窗口: {self.hep_params['time_window'][0]*1000:.0f}ms 到 {self.hep_params['time_window'][1]*1000:.0f}ms")
        print(f"可视化窗口: {self.viz_params['time_window'][0]*1000:.0f}ms 到 {self.viz_params['time_window'][1]*1000:.0f}ms")

    def extract_condition_from_filename(self, filename):
        """
        从文件名中提取实验条件

        Parameters:
        -----------
        filename : str
            文件名

        Returns:
        --------
        condition : str or None
            实验条件，如果无法识别则返回None
        """
        # 文件名格式示例: 01_01_reto2_combined_baseline_corrected_chanloc_bad_interp_prac_TP9TP10Ref.fif
        # 或: 01_02_reto2_combined_baseline_corrected_chanloc_bad_interp_test_TP9TP10Ref.fif

        # 使用正则表达式提取条件信息
        pattern = r'(\d+)_(\d+)_.*_(prac|test|rest)_.*\.fif'
        match = re.match(pattern, filename)

        if match:
            subject_id = match.group(1)
            session_id = match.group(2)
            base_condition = match.group(3)

            # 根据session_id和base_condition确定具体条件
            if base_condition == 'prac':
                return 'prac'
            elif base_condition == 'test':
                # session_id 01->test1, 02->test2, 03->test3
                return f'test{int(session_id)}'
            elif base_condition == 'rest':
                # session_id 01->rest1, 02->rest2, 03->rest3
                return f'rest{int(session_id)}'

        return None

    def save_analysis_results(self, timestamp=None):
        """保存分析结果到文件"""
        if timestamp is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存完整结果（EEG滤波版本）
        results_file = self.results_save_dir / f"hep_analysis_eeg_filtered_results_{timestamp}.pkl"
        with open(results_file, 'wb') as f:
            pickle.dump(self.analysis_results, f)

        # 保存元数据
        metadata = {
            'timestamp': timestamp,
            'conditions': self.conditions,
            'hep_params': self.hep_params,
            'viz_params': self.viz_params,
            'electrode_groups': self.electrode_groups,
            'script_version': '07_eeg_filtered_fixed_r_peak_alignment',
            'eeg_filter': '0.1-30Hz'
        }
        metadata_file = self.results_save_dir / f"hep_analysis_eeg_filtered_metadata_{timestamp}.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

        print(f"✅ 分析结果已保存:")
        print(f"   数据文件: {results_file}")
        print(f"   元数据文件: {metadata_file}")
        return timestamp

    def load_analysis_results(self, timestamp=None):
        """加载已保存的分析结果"""
        if timestamp is None:
            # 查找最新的EEG滤波结果文件
            result_files = list(self.results_save_dir.glob("hep_analysis_eeg_filtered_results_*.pkl"))
            if not result_files:
                print("❌ 未找到已保存的EEG滤波分析结果")
                return False

            # 按时间戳排序，选择最新的
            result_files.sort(key=lambda x: x.stem.split('_')[-1])
            latest_file = result_files[-1]
            timestamp = latest_file.stem.split('_')[-1]
        else:
            latest_file = self.results_save_dir / f"hep_analysis_eeg_filtered_results_{timestamp}.pkl"
            if not latest_file.exists():
                print(f"❌ 未找到时间戳为 {timestamp} 的EEG滤波分析结果")
                return False

        try:
            # 加载结果数据
            with open(latest_file, 'rb') as f:
                self.analysis_results = pickle.load(f)

            # 加载元数据
            metadata_file = self.results_save_dir / f"hep_analysis_eeg_filtered_metadata_{timestamp}.json"
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                print(f"✅ 成功加载分析结果 (时间戳: {timestamp})")
                print(f"   脚本版本: {metadata.get('script_version', 'unknown')}")
                return True
            else:
                print(f"⚠️ 加载了数据但未找到元数据文件")
                return True

        except Exception as e:
            print(f"❌ 加载分析结果失败: {str(e)}")
            return False

    def select_files_by_condition(self, n_files_per_condition=40):
        """
        按实验条件选择文件

        Parameters:
        -----------
        n_files_per_condition : int
            每个条件选择的文件数量
        """
        print("\n" + "="*60)
        print("按实验条件选择文件")
        print("="*60)

        try:
            # 读取电压验证结果
            validation_df = pd.read_excel(self.validation_excel_path, sheet_name='验证结果')
            print(f"读取到 {len(validation_df)} 个文件的验证结果")

            # 记录文件选择过程中的淘汰情况
            file_selection_stats = {
                'total_files': len(validation_df),
                'high_quality_files': 0,
                'low_quality_files': [],
                'missing_files': [],
                'unrecognized_conditions': []
            }

            if self.enable_quality_control:
                # 筛选高质量文件（EEG和ECG都在生理范围内）
                high_quality_files = validation_df[validation_df['都在范围内'] == True]
                low_quality_files = validation_df[validation_df['都在范围内'] == False]

                # 记录低质量文件
                for _, row in low_quality_files.iterrows():
                    file_selection_stats['low_quality_files'].append({
                        'file_name': row['文件名'],
                        'eeg_in_range': row['EEG生理范围'],
                        'ecg_in_range': row['ECG生理范围'],
                        'eeg_amplitude': row['EEG幅度(μV)'],
                        'ecg_amplitude': row['ECG幅度(μV)'],
                        'rejection_reason': 'voltage_out_of_range'
                    })
            else:
                # 不启用质量控制时，使用所有文件
                high_quality_files = validation_df
                print("⚠️ 质量控制已禁用，使用所有文件")

            file_selection_stats['high_quality_files'] = len(high_quality_files)
            print(f"高质量文件数量: {len(high_quality_files)}")
            if self.enable_quality_control:
                print(f"低质量文件数量: {len(file_selection_stats['low_quality_files'])}")

            # 按条件分组文件
            condition_files = {condition: [] for condition in self.conditions.keys()}

            for _, row in high_quality_files.iterrows():
                filename = row['文件名']
                condition = self.extract_condition_from_filename(filename)

                if condition and condition in self.conditions:
                    file_path = self.data_dir / filename
                    if file_path.exists():
                        condition_files[condition].append({
                            'file_name': filename,
                            'file_path': file_path,
                            'eeg_amplitude': row['EEG幅度(μV)'],
                            'ecg_amplitude': row['ECG幅度(μV)'],
                            'quality_score': int(row['EEG生理范围']) + int(row['ECG生理范围'])
                        })
                    else:
                        file_selection_stats['missing_files'].append(filename)
                else:
                    file_selection_stats['unrecognized_conditions'].append(filename)
            
            # 为每个条件选择文件
            for condition in self.conditions.keys():
                available_files = condition_files[condition]
                print(f"\n条件 '{condition}' ({self.conditions[condition]}):")
                print(f"  可用文件数: {len(available_files)}")

                if len(available_files) == 0:
                    print(f"  ⚠️ 无可用文件")
                    self.analysis_results['selected_files'][condition] = []
                    continue

                # 按质量分数排序
                available_files.sort(key=lambda x: x['quality_score'], reverse=True)

                # 选择文件（如果可用文件少于需求，则全部选择）
                n_select = min(n_files_per_condition, len(available_files))

                # 从前100%的高质量文件中随机选择
                top_files = available_files[:int(len(available_files) * 1)] if len(available_files) > 5 else available_files
                selected_files = random.sample(top_files, min(n_select, len(top_files)))

                self.analysis_results['selected_files'][condition] = selected_files

                print(f"  选择文件数: {len(selected_files)}")
                for i, file_info in enumerate(selected_files[:3]):  # 只显示前3个
                    print(f"    {i+1}. {file_info['file_name']}")
                if len(selected_files) > 3:
                    print(f"    ... 还有 {len(selected_files)-3} 个文件")

            # 保存文件选择统计信息
            self.analysis_results['file_selection_stats'] = file_selection_stats

            # 打印文件选择总结
            print(f"\n📊 文件选择总结:")
            print(f"  总文件数: {file_selection_stats['total_files']}")
            print(f"  高质量文件: {file_selection_stats['high_quality_files']}")
            if self.enable_quality_control:
                print(f"  低质量文件: {len(file_selection_stats['low_quality_files'])}")
            print(f"  缺失文件: {len(file_selection_stats['missing_files'])}")
            print(f"  无法识别条件: {len(file_selection_stats['unrecognized_conditions'])}")

            return True

        except Exception as e:
            print(f"❌ 文件选择失败: {str(e)}")
            return False

    def apply_eeg_filter(self, eeg_data, sampling_rate):
        """
        对EEG数据应用0.1-30Hz带通滤波

        Parameters:
        -----------
        eeg_data : array
            EEG数据 (channels x samples)
        sampling_rate : float
            采样率

        Returns:
        --------
        eeg_filtered : array
            滤波后的EEG数据
        """
        try:
            # 设计0.1-30Hz带通滤波器
            low_freq = 0.1
            high_freq = 30.0

            # 使用scipy.signal设计Butterworth滤波器
            nyquist = sampling_rate / 2
            low_norm = low_freq / nyquist
            high_norm = high_freq / nyquist

            # 设计4阶Butterworth带通滤波器
            sos = signal.butter(4, [low_norm, high_norm], btype='band', output='sos')

            # 对每个EEG通道应用滤波
            eeg_filtered = np.zeros_like(eeg_data)
            for ch in range(eeg_data.shape[0]):
                eeg_filtered[ch, :] = signal.sosfilt(sos, eeg_data[ch, :])

            return eeg_filtered

        except Exception as e:
            print(f"⚠️ EEG滤波失败: {str(e)}")
            print("  使用原始EEG数据")
            return eeg_data

    def detect_r_waves(self, ecg_data, sampling_rate):
        """
        检测ECG信号中的R波 - 修复版本，确保0ms对齐到真正的R波峰值

        Parameters:
        -----------
        ecg_data : array
            ECG信号数据
        sampling_rate : float
            采样率

        Returns:
        --------
        r_peaks : array
            R波峰值位置（样本点）
        """
        # 使用多个ECG通道的平均值
        if ecg_data.ndim > 1:
            ecg_signal = np.mean(ecg_data, axis=0)
        else:
            ecg_signal = ecg_data

        # 步骤1: 带通滤波 (5-15 Hz) 增强QRS复合波
        sos = signal.butter(4, [5, 15], btype='band', fs=sampling_rate, output='sos')
        ecg_filtered = signal.sosfilt(sos, ecg_signal)

        # 步骤2: 计算信号的绝对值和平方
        ecg_squared = ecg_filtered ** 2

        # 步骤3: 移动平均平滑
        window_size = int(0.08 * sampling_rate)  # 80ms窗口
        ecg_smoothed = np.convolve(ecg_squared, np.ones(window_size)/window_size, mode='same')

        # 步骤4: 自适应阈值检测粗略R波位置
        threshold = np.mean(ecg_smoothed) + 2 * np.std(ecg_smoothed)
        rough_peaks, _ = signal.find_peaks(
            ecg_smoothed,
            height=threshold,
            distance=int(0.3 * sampling_rate)  # 最小间隔300ms
        )

        # 步骤5: 精确定位真正的R波峰值
        # 在每个粗略峰值附近的原始ECG信号中找到真正的最大值
        refined_peaks = []
        search_window = int(0.05 * sampling_rate)  # ±50ms搜索窗口

        for rough_peak in rough_peaks:
            # 定义搜索范围
            start_idx = max(0, rough_peak - search_window)
            end_idx = min(len(ecg_signal), rough_peak + search_window)

            # 在原始ECG信号中找到最大值位置
            search_segment = ecg_signal[start_idx:end_idx]

            # 找到绝对值最大的位置（处理倒置的R波）
            if np.max(search_segment) > abs(np.min(search_segment)):
                # 正向R波
                local_peak = np.argmax(search_segment)
            else:
                # 倒置R波
                local_peak = np.argmin(search_segment)

            # 转换为全局索引
            true_r_peak = start_idx + local_peak
            refined_peaks.append(true_r_peak)

        return np.array(refined_peaks)

    def validate_r_peaks(self, r_peaks, sampling_rate):
        """
        验证R波检测质量（支持质量控制开关）

        Parameters:
        -----------
        r_peaks : array
            R波峰值位置
        sampling_rate : float
            采样率

        Returns:
        --------
        valid_peaks : array
            有效的R波峰值
        quality_metrics : dict
            质量指标
        """
        if len(r_peaks) < 2:
            return r_peaks, {'valid_peaks': len(r_peaks), 'rr_intervals': [], 'quality': 'poor'}

        # 计算R-R间隔
        rr_intervals = np.diff(r_peaks) / sampling_rate

        if self.enable_quality_control:
            # 启用质量控制：筛选有效的R-R间隔
            valid_rr_mask = (rr_intervals >= self.hep_params['rr_interval_range'][0]) & \
                           (rr_intervals <= self.hep_params['rr_interval_range'][1])

            # 移除异常间隔对应的R波
            valid_peaks = []
            rejected_peaks = []
            for i in range(len(r_peaks)):
                if i == 0:  # 第一个峰值
                    if len(valid_rr_mask) > 0 and valid_rr_mask[0]:
                        valid_peaks.append(r_peaks[i])
                    else:
                        rejected_peaks.append((r_peaks[i], 'first_peak_invalid_interval'))
                elif i == len(r_peaks) - 1:  # 最后一个峰值
                    if valid_rr_mask[i-1]:
                        valid_peaks.append(r_peaks[i])
                    else:
                        rejected_peaks.append((r_peaks[i], 'last_peak_invalid_interval'))
                else:  # 中间的峰值
                    if valid_rr_mask[i-1] and valid_rr_mask[i]:
                        valid_peaks.append(r_peaks[i])
                    else:
                        rejected_peaks.append((r_peaks[i], 'middle_peak_invalid_interval'))

            valid_peaks = np.array(valid_peaks)
        else:
            # 禁用质量控制：使用所有R波
            valid_peaks = r_peaks
            rejected_peaks = []

        valid_rr_intervals = np.diff(valid_peaks) / sampling_rate if len(valid_peaks) > 1 else []

        # 质量评估
        if self.enable_quality_control:
            if len(valid_peaks) >= self.hep_params['min_heartbeats']:
                quality = 'good'
            elif len(valid_peaks) >= 50:
                quality = 'fair'
            else:
                quality = 'poor'
        else:
            # 禁用质量控制时，根据总数评估
            if len(valid_peaks) >= 50:
                quality = 'acceptable_no_qc'
            else:
                quality = 'insufficient_data'

        quality_metrics = {
            'total_peaks': len(r_peaks),
            'valid_peaks': len(valid_peaks),
            'rejected_peaks': len(rejected_peaks),
            'rejection_rate': (len(r_peaks) - len(valid_peaks)) / len(r_peaks) if len(r_peaks) > 0 else 1,
            'mean_rr_interval': np.mean(valid_rr_intervals) if len(valid_rr_intervals) > 0 else 0,
            'rr_std': np.std(valid_rr_intervals) if len(valid_rr_intervals) > 0 else 0,
            'quality': quality,
            'quality_control_enabled': self.enable_quality_control
        }

        return valid_peaks, quality_metrics

    def extract_hep_epochs(self, eeg_data, r_peaks, sampling_rate):
        """
        提取HEP时期数据（与原脚本保持一致）

        Parameters:
        -----------
        eeg_data : array
            EEG数据 (channels x samples)
        r_peaks : array
            有效R波位置
        sampling_rate : float
            采样率

        Returns:
        --------
        hep_epochs : array
            HEP时期数据 (epochs x channels x samples)
        times : array
            时间轴
        """
        # 计算时间窗口对应的样本点
        pre_samples = int(abs(self.hep_params['time_window'][0]) * sampling_rate)
        post_samples = int(self.hep_params['time_window'][1] * sampling_rate)
        total_samples = pre_samples + post_samples

        # 创建时间轴
        times = np.linspace(self.hep_params['time_window'][0],
                           self.hep_params['time_window'][1],
                           total_samples)

        # 提取每个心跳周期的EEG数据
        valid_epochs = []

        for r_peak in r_peaks:
            start_idx = r_peak - pre_samples
            end_idx = r_peak + post_samples

            # 确保索引在有效范围内
            if start_idx >= 0 and end_idx < eeg_data.shape[1]:
                epoch = eeg_data[:, start_idx:end_idx]

                # 基线校正
                baseline_start_time = self.hep_params['baseline_window'][0] - self.hep_params['time_window'][0]
                baseline_end_time = self.hep_params['baseline_window'][1] - self.hep_params['time_window'][0]

                baseline_start = int(baseline_start_time * sampling_rate)
                baseline_end = int(baseline_end_time * sampling_rate)

                # 检查基线窗口是否有效
                if baseline_start < 0 or baseline_end > epoch.shape[1] or baseline_start >= baseline_end:
                    continue

                baseline_mean = np.mean(epoch[:, baseline_start:baseline_end], axis=1, keepdims=True)
                epoch_corrected = epoch - baseline_mean

                # 检查基线稳定性
                baseline_std = np.std(epoch[:, baseline_start:baseline_end], axis=1)
                if self.enable_quality_control:
                    # 启用质量控制：检查基线稳定性
                    if np.all(baseline_std < self.hep_params['baseline_std_threshold']):
                        valid_epochs.append(epoch_corrected)
                else:
                    # 禁用质量控制：接受所有epoch
                    valid_epochs.append(epoch_corrected)

        if len(valid_epochs) > 0:
            hep_epochs = np.array(valid_epochs)
            return hep_epochs, times
        else:
            return None, times

    def analyze_single_file(self, file_info, condition):
        """
        分析单个文件的HEP

        Parameters:
        -----------
        file_info : dict
            文件信息
        condition : str
            实验条件

        Returns:
        --------
        result : dict
            分析结果
        """
        try:
            # 读取数据
            raw = mne.io.read_raw_fif(file_info['file_path'], preload=True, verbose=False)
            data = raw.get_data()
            sampling_rate = raw.info['sfreq']

            # 分离EEG和ECG数据
            eeg_data = data[:61, :]  # 前61通道
            ecg_data = data[61:, :]  # 后58通道

            # 新增：EEG 0.1-30Hz带通滤波
            print(f"  应用EEG滤波 (0.1-30Hz)...")
            eeg_data_filtered = self.apply_eeg_filter(eeg_data, sampling_rate)

            # R波检测
            r_peaks = self.detect_r_waves(ecg_data, sampling_rate)
            valid_r_peaks, r_wave_quality = self.validate_r_peaks(r_peaks, sampling_rate)

            # 检查心跳数量是否足够（根据质量控制设置调整阈值）
            min_heartbeats_threshold = self.hep_params['min_heartbeats'] if self.enable_quality_control else 50
            if len(valid_r_peaks) < min_heartbeats_threshold:
                return {
                    'file_name': file_info['file_name'],
                    'condition': condition,
                    'success': False,
                    'reason': 'insufficient_heartbeats',
                    'valid_heartbeats': len(valid_r_peaks),
                    'total_heartbeats': len(r_peaks),
                    'r_wave_quality': r_wave_quality,
                    'quality_control_enabled': self.enable_quality_control
                }

            # HEP提取（使用滤波后的EEG数据）
            hep_epochs, times = self.extract_hep_epochs(eeg_data_filtered, valid_r_peaks, sampling_rate)

            if hep_epochs is None:
                return {
                    'file_name': file_info['file_name'],
                    'condition': condition,
                    'success': False,
                    'reason': 'hep_extraction_failed'
                }

            # 计算平均HEP
            hep_average = np.mean(hep_epochs, axis=0)

            # 质量指标计算
            quality_metrics = self._calculate_hep_quality_metrics(hep_epochs, times)

            result = {
                'file_name': file_info['file_name'],
                'condition': condition,
                'success': True,
                'hep_epochs': hep_epochs,
                'hep_average': hep_average,
                'times': times,
                'valid_heartbeats': len(valid_r_peaks),
                'quality_metrics': quality_metrics,
                'channel_names': raw.ch_names[:61]  # EEG通道名称
            }

            return result

        except Exception as e:
            return {
                'file_name': file_info['file_name'],
                'condition': condition,
                'success': False,
                'reason': 'analysis_error',
                'error': str(e)
            }

    def _calculate_hep_quality_metrics(self, hep_epochs, times):
        """计算HEP质量指标"""
        # 基线窗口索引
        baseline_mask = (times >= self.hep_params['baseline_window'][0]) & \
                       (times <= self.hep_params['baseline_window'][1])

        # HEP成分窗口（R波后150-300ms）
        hep_mask = (times >= 0.15) & (times <= 0.3)

        # 计算信噪比
        signal_power = np.mean(np.var(hep_epochs[:, :, hep_mask], axis=2))
        noise_power = np.mean(np.var(hep_epochs[:, :, baseline_mask], axis=2))
        snr = 10 * np.log10(signal_power / noise_power) if noise_power > 0 else 0

        # 基线稳定性
        baseline_std = np.mean(np.std(hep_epochs[:, :, baseline_mask], axis=2))
        baseline_stable = baseline_std < self.hep_params['baseline_std_threshold']

        # 伪迹检测（基于z-score）
        epoch_amplitudes = np.max(np.abs(hep_epochs), axis=(1, 2))
        z_scores = np.abs(zscore(epoch_amplitudes))
        artifact_epochs = np.sum(z_scores > 3)
        artifact_rate = artifact_epochs / len(hep_epochs)

        return {
            'snr': snr,
            'baseline_std': baseline_std,
            'baseline_stable': baseline_stable,
            'artifact_rate': artifact_rate,
            'artifact_epochs': artifact_epochs,
            'total_epochs': len(hep_epochs)
        }

    def analyze_condition(self, condition):
        """
        分析单个实验条件

        Parameters:
        -----------
        condition : str
            实验条件
        """
        print(f"\n分析条件: {condition} ({self.conditions[condition]})")
        print("-" * 50)

        selected_files = self.analysis_results['selected_files'][condition]
        if not selected_files:
            print(f"⚠️ 条件 {condition} 无可用文件")
            return

        successful_results = []
        failed_results = []
        rejection_details = {
            'insufficient_heartbeats': [],
            'hep_extraction_failed': [],
            'analysis_error': []
        }

        for i, file_info in enumerate(selected_files):
            print(f"  处理文件 {i+1}/{len(selected_files)}: {file_info['file_name']}")

            result = self.analyze_single_file(file_info, condition)

            if result['success']:
                successful_results.append(result)
                print(f"    ✅ 成功 - 心跳数: {result['valid_heartbeats']}")
            else:
                failed_results.append(result)
                rejection_reason = result['reason']
                rejection_details[rejection_reason].append(result)

                if rejection_reason == 'insufficient_heartbeats':
                    print(f"    ❌ 失败 - 心跳不足: {result.get('valid_heartbeats', 0)}/{result.get('total_heartbeats', 0)}")
                else:
                    print(f"    ❌ 失败 - {rejection_reason}")

        print(f"\n条件 {condition} 分析完成:")
        print(f"  成功: {len(successful_results)}/{len(selected_files)}")
        print(f"  失败: {len(failed_results)}/{len(selected_files)}")

        # 详细的失败统计
        if failed_results:
            print(f"  失败详情:")
            for reason, count in [(k, len(v)) for k, v in rejection_details.items() if v]:
                print(f"    {reason}: {count}")

        # 存储结果
        self.analysis_results['hep_data'][condition] = {
            'successful': successful_results,
            'failed': failed_results
        }

        # 存储淘汰数据详情
        self.analysis_results['rejected_data'][condition] = rejection_details

        # 计算脑区平均
        if successful_results:
            self._calculate_brain_region_averages(condition, successful_results)

    def _calculate_brain_region_averages(self, condition, successful_results):
        """计算脑区平均HEP"""
        if not successful_results:
            return

        # 获取通道名称和时间轴
        channel_names = successful_results[0]['channel_names']
        times = successful_results[0]['times']

        region_data = {}

        for region_name, electrode_list in self.electrode_groups.items():
            # 找到该脑区对应的通道索引
            region_indices = []
            for electrode in electrode_list:
                if electrode in channel_names:
                    region_indices.append(channel_names.index(electrode))

            if not region_indices:
                continue

            # 收集该脑区所有文件的HEP数据
            region_heps = []
            for result in successful_results:
                # 提取该脑区的HEP数据并平均
                region_hep = np.mean(result['hep_average'][region_indices, :], axis=0)
                region_heps.append(region_hep)

            if region_heps:
                # 计算平均和标准误
                region_heps = np.array(region_heps)
                mean_hep = np.mean(region_heps, axis=0)
                sem_hep = np.std(region_heps, axis=0) / np.sqrt(len(region_heps))

                region_data[region_name] = {
                    'mean': mean_hep,
                    'sem': sem_hep,
                    'times': times,
                    'n_files': len(region_heps),
                    'electrodes': [channel_names[i] for i in region_indices]
                }

        self.analysis_results['group_averages'][condition] = region_data

    def create_condition_visualization(self, condition):
        """
        为单个实验条件创建HEP波形可视化

        Parameters:
        -----------
        condition : str
            实验条件
        """
        if condition not in self.analysis_results['group_averages']:
            print(f"⚠️ 条件 {condition} 无可用数据进行可视化")
            return None

        region_data = self.analysis_results['group_averages'][condition]
        if not region_data:
            print(f"⚠️ 条件 {condition} 无脑区数据")
            return None

        # 创建图形 - 2行3列布局，每个子图2:1比例
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'HEP波形 - {self.conditions[condition]}', fontsize=16, fontweight='bold')

        axes = axes.flatten()

        # 获取可视化时间窗口的索引
        times = list(region_data.values())[0]['times']
        viz_mask = (times >= self.viz_params['time_window'][0]) & \
                   (times <= self.viz_params['time_window'][1])
        viz_times = times[viz_mask] * 1000  # 转换为毫秒

        # 计算统一的Y轴范围
        all_amplitudes = []
        for region_name, data in region_data.items():
            mean_hep = data['mean'][viz_mask] * 1e6  # 转换为μV
            all_amplitudes.extend(mean_hep)

        if all_amplitudes:
            y_min = np.min(all_amplitudes) * 1.1
            y_max = np.max(all_amplitudes) * 1.1
        else:
            y_min, y_max = -10, 10

        # 绘制每个脑区
        for i, (region_name, data) in enumerate(region_data.items()):
            if i >= len(axes):
                break

            ax = axes[i]

            # 提取可视化窗口的数据
            mean_hep = data['mean'][viz_mask] * 1e6  # 转换为μV
            sem_hep = data['sem'][viz_mask] * 1e6   # 转换为μV

            # 绘制HEP波形
            color = self.condition_colors[condition]
            ax.plot(viz_times, mean_hep, color=color, linewidth=2,
                   label=f'{self.conditions[condition]}')

            # 绘制标准误区域
            ax.fill_between(viz_times, mean_hep - sem_hep, mean_hep + sem_hep,
                           color=color, alpha=0.3)

            # 标记R波时间点
            ax.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='R波')

            # 标记HEP成分窗口
            ax.axvspan(200, 600, alpha=0.1, color='green', label='HEP窗口')

            # 设置标题和标签
            ax.set_title(f'{region_name} (n={data["n_files"]})', fontsize=12, fontweight='bold')
            ax.set_xlabel('时间 (ms)', fontsize=10)
            ax.set_ylabel('幅度 (μV)', fontsize=10)
            ax.grid(True, alpha=0.3)

            # 设置坐标轴范围
            ax.set_xlim(viz_times[0], viz_times[-1])
            ax.set_ylim(y_min, y_max)

            # 设置子图比例为2:1
            ax.set_aspect('auto')

            # 只在第一个子图显示图例
            if i == 0:
                ax.legend(loc='upper right', fontsize=8)

        # 隐藏多余的子图
        for i in range(len(region_data), len(axes)):
            axes[i].set_visible(False)

        plt.tight_layout()

        # 保存图像（EEG滤波版本）
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = Path("../../result/hep_seven_conditions_eeg_filtered")
        output_dir.mkdir(parents=True, exist_ok=True)

        plot_path = output_dir / f"hep_waveforms_{condition}_{timestamp}.png"
        plt.savefig(plot_path, dpi=self.viz_params['figure_dpi'], bbox_inches='tight')
        plt.close()

        print(f"✅ 条件 {condition} 可视化已保存: {plot_path}")
        return plot_path

    def run_complete_analysis(self, n_files_per_condition=40, force_recompute=False):
        """
        运行完整的七条件HEP分析

        Parameters:
        -----------
        n_files_per_condition : int
            每个条件选择的文件数量
        force_recompute : bool
            是否强制重新计算（忽略已保存的结果）
        """
        print("开始七个实验条件HEP分析...")

        # 检查是否有已保存的结果
        if not force_recompute and self.load_analysis_results():
            print("✅ 使用已保存的分析结果")
            skip_analysis = True
        else:
            print("🔄 开始新的分析计算")
            skip_analysis = False

        if not skip_analysis:
            # 第一步：选择文件
            if not self.select_files_by_condition(n_files_per_condition):
                print("❌ 文件选择失败，终止分析")
                return False

            # 第二步：分析每个条件
            for condition in self.conditions.keys():
                self.analyze_condition(condition)

            # 保存分析结果
            timestamp = self.save_analysis_results()
        else:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 第三步：生成可视化
        visualization_paths = []
        for condition in self.conditions.keys():
            plot_path = self.create_condition_visualization(condition)
            if plot_path:
                visualization_paths.append(plot_path)

        # 第四步：生成分析报告
        self.generate_analysis_report(visualization_paths)

        # 第五步：生成数据淘汰报告
        self.generate_rejection_report()

        # 第六步：打印总结
        self._print_analysis_summary()

        print("\n" + "="*80)
        print("✅ 七个实验条件HEP分析完成！")
        print("="*80)

        return True

    def run_visualization_only(self, timestamp=None):
        """
        仅运行可视化（基于已保存的分析结果）

        Parameters:
        -----------
        timestamp : str, optional
            指定要使用的结果时间戳，如果为None则使用最新的
        """
        print("开始基于已保存结果的可视化...")

        # 加载分析结果
        if not self.load_analysis_results(timestamp):
            print("❌ 无法加载分析结果，终止可视化")
            return False

        # 生成可视化
        visualization_paths = []
        for condition in self.conditions.keys():
            if condition in self.analysis_results['group_averages']:
                plot_path = self.create_condition_visualization(condition)
                if plot_path:
                    visualization_paths.append(plot_path)

        # 生成分析报告
        self.generate_analysis_report(visualization_paths)

        print("\n" + "="*80)
        print("✅ 可视化完成！")
        print("="*80)

        return True

    def generate_analysis_report(self, visualization_paths):
        """生成分析报告"""
        print("\n" + "="*60)
        print("生成分析报告")
        print("="*60)

        # 创建输出目录（EEG滤波版本）
        output_dir = Path("../../result/hep_seven_conditions_eeg_filtered")
        output_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = output_dir / f"seven_conditions_hep_analysis_report_{timestamp}.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# HEP（心跳诱发电位）七个实验条件分析报告 - 100%质量文件版本\n\n")
            f.write(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("**版本说明**: 本报告基于所有高质量文件的分析结果（100%质量阈值，最大样本量）\n\n")

            f.write("## 📊 分析概览\n\n")
            f.write("### 实验条件\n")
            for condition, name in self.conditions.items():
                f.write(f"- **{condition}**: {name}\n")
            f.write("\n")

            f.write("### 分析参数\n")
            f.write(f"- **时间窗口**: {self.hep_params['time_window'][0]*1000:.0f}ms 到 {self.hep_params['time_window'][1]*1000:.0f}ms\n")
            f.write(f"- **基线校正**: {self.hep_params['baseline_window'][0]*1000:.0f}ms 到 {self.hep_params['baseline_window'][1]*1000:.0f}ms\n")
            f.write(f"- **可视化窗口**: {self.viz_params['time_window'][0]*1000:.0f}ms 到 {self.viz_params['time_window'][1]*1000:.0f}ms\n")
            f.write(f"- **最小心跳数**: {self.hep_params['min_heartbeats']}\n\n")

            f.write("## 🧠 脑区电极分组\n\n")
            for region, electrodes in self.electrode_groups.items():
                f.write(f"- **{region}**: {', '.join(electrodes)}\n")
            f.write("\n")

            f.write("## 📈 分析结果\n\n")

            # 统计每个条件的结果
            for condition in self.conditions.keys():
                f.write(f"### {condition} - {self.conditions[condition]}\n\n")

                if condition in self.analysis_results['hep_data']:
                    data = self.analysis_results['hep_data'][condition]
                    successful = data['successful']
                    failed = data['failed']

                    f.write(f"- **分析文件数**: {len(successful) + len(failed)}\n")
                    f.write(f"- **成功分析**: {len(successful)}\n")
                    f.write(f"- **分析失败**: {len(failed)}\n")
                    f.write(f"- **成功率**: {len(successful)/(len(successful)+len(failed))*100:.1f}%\n")

                    if successful:
                        total_heartbeats = sum(r['valid_heartbeats'] for r in successful)
                        avg_heartbeats = total_heartbeats / len(successful)
                        f.write(f"- **平均心跳数**: {avg_heartbeats:.1f}\n")

                    if condition in self.analysis_results['group_averages']:
                        region_data = self.analysis_results['group_averages'][condition]
                        f.write(f"- **分析脑区数**: {len(region_data)}\n")

                    f.write("\n")
                else:
                    f.write("- **状态**: 无可用数据\n\n")

            f.write("## 🎨 生成的可视化文件\n\n")
            for i, path in enumerate(visualization_paths, 1):
                f.write(f"{i}. {path.name}\n")
            f.write("\n")

            f.write("## 💡 分析说明\n\n")
            f.write("1. **数据选择**: 从183个高质量文件中选择所有文件（100%质量阈值，最大样本量）\n")
            f.write("2. **质量阈值**: 100%质量阈值使用所有高质量文件，实现最大统计功效\n")
            f.write("3. **质量控制**: 严格的R波检测和基线稳定性检查\n")
            f.write("4. **可视化标准**: 2:1宽高比，统一时间轴和幅度范围\n")
            f.write("5. **统计方法**: 跨文件平均，标准误差估计\n\n")

            f.write(f"---\n\n**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

        print(f"✅ 分析报告已保存: {report_path}")

    def generate_rejection_report(self):
        """生成数据淘汰详细报告"""
        print("\n" + "="*60)
        print("生成数据淘汰报告")
        print("="*60)

        # 创建输出目录
        output_dir = Path("../../result/hep_seven_conditions_eeg_filtered")
        output_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = output_dir / f"data_rejection_report_{timestamp}.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# HEP数据淘汰详细报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**质量控制状态**: {'启用' if self.enable_quality_control else '禁用'}\n\n")

            # 文件选择阶段的淘汰
            if 'file_selection_stats' in self.analysis_results:
                stats = self.analysis_results['file_selection_stats']
                f.write("## 📁 文件选择阶段淘汰情况\n\n")
                f.write(f"- **总文件数**: {stats['total_files']}\n")
                f.write(f"- **高质量文件**: {stats['high_quality_files']}\n")
                f.write(f"- **低质量文件**: {len(stats['low_quality_files'])}\n")
                f.write(f"- **缺失文件**: {len(stats['missing_files'])}\n")
                f.write(f"- **无法识别条件**: {len(stats['unrecognized_conditions'])}\n\n")

                if stats['low_quality_files']:
                    f.write("### 低质量文件详情\n\n")
                    f.write("| 文件名 | EEG范围 | ECG范围 | EEG幅度(μV) | ECG幅度(μV) |\n")
                    f.write("|--------|---------|---------|-------------|-------------|\n")
                    for file_info in stats['low_quality_files'][:20]:  # 只显示前20个
                        f.write(f"| {file_info['file_name']} | {file_info['eeg_in_range']} | {file_info['ecg_in_range']} | {file_info['eeg_amplitude']:.1f} | {file_info['ecg_amplitude']:.1f} |\n")
                    if len(stats['low_quality_files']) > 20:
                        f.write(f"| ... | ... | ... | ... | ... |\n")
                        f.write(f"| 共{len(stats['low_quality_files'])}个文件 | | | | |\n")
                    f.write("\n")

            # 分析阶段的淘汰
            f.write("## 🔬 分析阶段淘汰情况\n\n")
            total_rejected = 0
            for condition in self.conditions.keys():
                if condition in self.analysis_results['rejected_data']:
                    rejected = self.analysis_results['rejected_data'][condition]
                    condition_total = sum(len(v) for v in rejected.values())
                    total_rejected += condition_total

                    if condition_total > 0:
                        f.write(f"### {condition} - {self.conditions[condition]}\n\n")
                        for reason, files in rejected.items():
                            if files:
                                f.write(f"**{reason}**: {len(files)} 个文件\n")
                                for file_info in files[:5]:  # 只显示前5个
                                    f.write(f"- {file_info['file_name']}")
                                    if 'valid_heartbeats' in file_info:
                                        f.write(f" (心跳: {file_info['valid_heartbeats']}/{file_info.get('total_heartbeats', '?')})")
                                    f.write("\n")
                                if len(files) > 5:
                                    f.write(f"- ... 还有 {len(files)-5} 个文件\n")
                                f.write("\n")

            f.write(f"## 📊 淘汰统计总结\n\n")
            f.write(f"- **质量控制参数**:\n")
            f.write(f"  - 最小心跳数: {self.hep_params['min_heartbeats']}\n")
            f.write(f"  - R-R间隔范围: {self.hep_params['rr_interval_range'][0]}-{self.hep_params['rr_interval_range'][1]}秒\n")
            f.write(f"  - 基线标准差阈值: {self.hep_params['baseline_std_threshold']}\n\n")

            f.write(f"- **总淘汰文件数**: {total_rejected}\n")
            f.write(f"- **质量控制状态**: {'启用' if self.enable_quality_control else '禁用'}\n\n")

            f.write("## 💡 建议\n\n")
            if self.enable_quality_control:
                f.write("1. 如需包含更多数据，可考虑:\n")
                f.write("   - 降低最小心跳数要求\n")
                f.write("   - 放宽R-R间隔范围\n")
                f.write("   - 调整基线稳定性阈值\n")
                f.write("   - 或使用 `enable_quality_control=False` 禁用质量控制\n\n")
            else:
                f.write("1. 当前已禁用质量控制，使用所有可用数据\n")
                f.write("2. 如需更严格的质量控制，可使用 `enable_quality_control=True`\n\n")

        print(f"✅ 数据淘汰报告已保存: {report_path}")
        return report_path

    def _print_analysis_summary(self):
        """打印分析总结"""
        print(f"\n📊 七个实验条件HEP分析总结:")

        total_files = 0
        total_successful = 0

        for condition in self.conditions.keys():
            if condition in self.analysis_results['hep_data']:
                data = self.analysis_results['hep_data'][condition]
                successful = len(data['successful'])
                failed = len(data['failed'])
                total = successful + failed

                total_files += total
                total_successful += successful

                print(f"  {condition} ({self.conditions[condition]}):")
                print(f"    文件数: {total}, 成功: {successful}, 成功率: {successful/total*100:.1f}%")

        print(f"\n🎯 总体统计:")
        print(f"  总文件数: {total_files}")
        print(f"  总成功数: {total_successful}")
        if total_files > 0:
            print(f"  总成功率: {total_successful/total_files*100:.1f}%")
        else:
            print(f"  总成功率: 0.0%")


if __name__ == "__main__":
    # 配置参数
    data_directory = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"
    validation_excel = r"D:\ecgeeg\30-数据分析\5-HBA\result\unit_conversion\final_voltage_validation_data_20250602_225214.xlsx"

    # 选择运行模式
    print("请选择运行模式:")
    print("1. 启用质量控制（默认，严格筛选数据）")
    print("2. 禁用质量控制（使用所有可用数据）")

    choice = input("请输入选择 (1/2，默认为1): ").strip()
    enable_qc = choice != "2"

    print(f"\n{'='*80}")
    print(f"运行模式: {'启用质量控制' if enable_qc else '禁用质量控制'}")
    print(f"{'='*80}")

    # 创建七条件HEP分析器并运行（支持质量控制开关）
    analyzer = SevenConditionsHEPAnalyzer(data_directory, validation_excel, enable_quality_control=enable_qc)

    # 强制重新计算以应用R波检测修复
    analyzer.run_complete_analysis(n_files_per_condition=40, force_recompute=True)
