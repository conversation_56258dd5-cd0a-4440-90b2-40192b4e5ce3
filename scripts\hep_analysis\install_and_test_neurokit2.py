#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装和测试NeuroKit2的脚本

这个脚本将：
1. 安装NeuroKit2
2. 测试R波检测功能
3. 与现有方法进行对比
4. 生成测试报告

作者: HEP Analysis Team
日期: 2024-12-19
"""

import subprocess
import sys
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import mne
import warnings
warnings.filterwarnings('ignore')

def install_neurokit2():
    """安装NeuroKit2"""
    print("🔧 正在安装NeuroKit2...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "neurokit2"])
        print("✅ NeuroKit2 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ NeuroKit2 安装失败: {e}")
        return False

def test_neurokit2_import():
    """测试NeuroKit2导入"""
    try:
        import neurokit2 as nk
        print(f"✅ NeuroKit2 导入成功，版本: {nk.__version__}")
        return True, nk
    except ImportError as e:
        print(f"❌ NeuroKit2 导入失败: {e}")
        return False, None

def test_r_wave_detection(nk, test_file_path):
    """测试R波检测功能"""
    print(f"\n🧪 测试R波检测功能...")
    print(f"📁 测试文件: {test_file_path}")
    
    try:
        # 读取测试数据
        raw = mne.io.read_raw_fif(test_file_path, preload=True, verbose=False)
        data = raw.get_data() * 1000  # mV -> μV
        sampling_rate = raw.info['sfreq']
        
        # 获取ECG数据（使用ECG11通道）
        ecg_data = data[61:, :]  # ECG通道
        ecg_signal = ecg_data[10, :]  # ECG11
        
        print(f"📊 ECG信号长度: {len(ecg_signal)} 样本点")
        print(f"📊 采样率: {sampling_rate} Hz")
        print(f"📊 信号时长: {len(ecg_signal)/sampling_rate:.1f} 秒")
        
        # 测试多种NeuroKit2方法
        methods = ['neurokit', 'pantompkins1985', 'hamilton2002', 'christov2004']
        results = {}
        
        for method in methods:
            try:
                print(f"\n  🔍 测试方法: {method}")
                
                # 使用NeuroKit2检测R波
                signals, info = nk.ecg_process(
                    ecg_signal, 
                    sampling_rate=sampling_rate,
                    method=method
                )
                
                r_peaks = info["ECG_R_Peaks"]
                
                # 计算基本统计
                if len(r_peaks) > 1:
                    rr_intervals = np.diff(r_peaks) / sampling_rate
                    mean_hr = 60 / np.mean(rr_intervals)
                    hr_std = np.std(60 / rr_intervals)
                    
                    results[method] = {
                        'r_peaks': r_peaks,
                        'count': len(r_peaks),
                        'mean_hr': mean_hr,
                        'hr_std': hr_std,
                        'signals': signals,
                        'info': info
                    }
                    
                    print(f"    ✅ 检测到 {len(r_peaks)} 个R波")
                    print(f"    📈 平均心率: {mean_hr:.1f} ± {hr_std:.1f} BPM")
                else:
                    print(f"    ❌ 未检测到足够的R波")
                    
            except Exception as e:
                print(f"    ❌ {method} 失败: {e}")
        
        return results, ecg_signal, sampling_rate
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None, None, None

def visualize_results(results, ecg_signal, sampling_rate, save_path):
    """可视化检测结果"""
    print(f"\n📊 生成可视化结果...")
    
    if not results:
        print("❌ 没有可视化的结果")
        return
    
    # 创建图形
    n_methods = len(results)
    fig, axes = plt.subplots(n_methods + 1, 1, figsize=(15, 3 * (n_methods + 1)))
    
    if n_methods == 0:
        return
    
    # 时间轴
    time_axis = np.arange(len(ecg_signal)) / sampling_rate
    
    # 原始ECG信号
    axes[0].plot(time_axis, ecg_signal, 'k-', linewidth=0.8, alpha=0.7)
    axes[0].set_title('原始ECG信号 (ECG11通道)', fontsize=12, fontweight='bold')
    axes[0].set_ylabel('幅度 (μV)')
    axes[0].grid(True, alpha=0.3)
    
    # 各种方法的检测结果
    colors = ['red', 'blue', 'green', 'orange', 'purple']
    
    for i, (method, result) in enumerate(results.items()):
        ax = axes[i + 1]
        
        # 绘制ECG信号
        ax.plot(time_axis, ecg_signal, 'k-', linewidth=0.8, alpha=0.5)
        
        # 标记R波位置
        r_peaks = result['r_peaks']
        r_times = r_peaks / sampling_rate
        r_amplitudes = ecg_signal[r_peaks]
        
        ax.scatter(r_times, r_amplitudes, c=colors[i % len(colors)], 
                  s=50, marker='o', alpha=0.8, zorder=5)
        
        # 添加垂直线
        for r_time in r_times:
            ax.axvline(r_time, color=colors[i % len(colors)], 
                      alpha=0.3, linestyle='--', linewidth=1)
        
        ax.set_title(f'{method}: {result["count"]}个R波, '
                    f'心率={result["mean_hr"]:.1f}±{result["hr_std"]:.1f} BPM',
                    fontsize=11)
        ax.set_ylabel('幅度 (μV)')
        ax.grid(True, alpha=0.3)
    
    axes[-1].set_xlabel('时间 (秒)')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ 可视化结果已保存: {save_path}")
    
    return fig

def generate_comparison_report(results):
    """生成对比报告"""
    print(f"\n📋 生成对比报告...")
    
    if not results:
        print("❌ 没有结果可以对比")
        return
    
    print("\n" + "="*60)
    print("           NeuroKit2 R波检测方法对比报告")
    print("="*60)
    
    print(f"{'方法':<20} {'R波数量':<10} {'平均心率':<12} {'心率变异':<12} {'状态':<10}")
    print("-" * 60)
    
    best_method = None
    best_score = 0
    
    for method, result in results.items():
        count = result['count']
        mean_hr = result['mean_hr']
        hr_std = result['hr_std']
        
        # 简单的质量评分
        if 50 <= mean_hr <= 120 and count >= 30:
            status = "✅ 良好"
            score = count * (1 / (1 + hr_std/mean_hr))  # 数量 × 稳定性
        elif count >= 20:
            status = "⚠️ 一般"
            score = count * 0.5
        else:
            status = "❌ 差"
            score = 0
        
        if score > best_score:
            best_score = score
            best_method = method
        
        print(f"{method:<20} {count:<10} {mean_hr:<12.1f} {hr_std:<12.1f} {status:<10}")
    
    print("-" * 60)
    if best_method:
        print(f"🏆 推荐方法: {best_method}")
    print("="*60)

def main():
    """主函数"""
    print("🚀 NeuroKit2 安装和测试程序")
    print("="*50)
    
    # 1. 安装NeuroKit2
    if not install_neurokit2():
        print("❌ 安装失败，程序退出")
        return
    
    # 2. 测试导入
    success, nk = test_neurokit2_import()
    if not success:
        print("❌ 导入失败，程序退出")
        return
    
    # 3. 查找测试文件
    data_dir = Path("D:/ecgeeg/18-eegecg手动预处理5-重参考2-双侧乳突")
    test_files = list(data_dir.rglob("*_test_*.fif"))
    
    if not test_files:
        print("❌ 未找到测试文件")
        return
    
    test_file = test_files[0]  # 使用第一个测试文件
    
    # 4. 测试R波检测
    results, ecg_signal, sampling_rate = test_r_wave_detection(nk, test_file)
    
    if results is None:
        print("❌ R波检测测试失败")
        return
    
    # 5. 生成可视化
    save_dir = Path("D:/ecgeeg/30-数据分析/5-HBA/result/neurokit2_test")
    save_dir.mkdir(parents=True, exist_ok=True)
    
    viz_path = save_dir / "neurokit2_r_wave_comparison.png"
    visualize_results(results, ecg_signal, sampling_rate, viz_path)
    
    # 6. 生成报告
    generate_comparison_report(results)
    
    print(f"\n🎉 测试完成！")
    print(f"📁 结果保存在: {save_dir}")
    print(f"\n💡 使用建议:")
    print(f"   1. 查看生成的可视化图像")
    print(f"   2. 根据报告选择最佳检测方法")
    print(f"   3. 在HEP分析中使用NeuroKit2替代现有方法")

if __name__ == "__main__":
    main()
