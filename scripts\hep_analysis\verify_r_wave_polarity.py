#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证R波极性和对齐准确性

这个脚本将：
1. 详细分析ECG11通道的信号特征
2. 手动检查R波的真实极性
3. 验证哪种检测方法更准确
4. 生成详细的信号分析报告

作者: HEP Analysis Team
日期: 2024-12-19
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import mne
import warnings
warnings.filterwarnings('ignore')

# 导入NeuroKit2
try:
    import neurokit2 as nk
    NEUROKIT_AVAILABLE = True
except ImportError:
    NEUROKIT_AVAILABLE = False

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_analyze_ecg():
    """加载并分析ECG信号"""
    print("🔍 加载并分析ECG信号...")
    
    # 加载数据
    data_dir = Path("D:/ecgeeg/18-eegecg手动预处理5-重参考2-双侧乳突")
    test_files = list(data_dir.rglob("*_test_*.fif"))
    
    if not test_files:
        raise FileNotFoundError("未找到测试文件")
    
    test_file = test_files[0]
    print(f"📁 分析文件: {test_file.name}")
    
    # 读取数据
    raw = mne.io.read_raw_fif(test_file, preload=True, verbose=False)
    data = raw.get_data() * 1000  # mV -> μV
    sampling_rate = raw.info['sfreq']
    
    # 获取ECG数据
    ecg_data = data[61:, :]  # 后58个通道是ECG
    ecg11_signal = ecg_data[10, :]  # ECG11通道
    
    print(f"📊 ECG11信号统计:")
    print(f"   - 长度: {len(ecg11_signal)} 样本点")
    print(f"   - 时长: {len(ecg11_signal)/sampling_rate:.1f} 秒")
    print(f"   - 幅度范围: {np.min(ecg11_signal):.1f} 到 {np.max(ecg11_signal):.1f} μV")
    print(f"   - 平均值: {np.mean(ecg11_signal):.1f} μV")
    print(f"   - 标准差: {np.std(ecg11_signal):.1f} μV")
    
    return {
        'ecg11_signal': ecg11_signal,
        'sampling_rate': sampling_rate,
        'file_name': test_file.name
    }

def analyze_signal_segments(ecg_signal, sampling_rate, n_segments=5):
    """分析信号的多个片段来确定R波特征"""
    print(f"\n🔍 分析信号片段以确定R波特征...")
    
    segment_length = int(10 * sampling_rate)  # 10秒片段
    total_length = len(ecg_signal)
    
    segments_info = []
    
    for i in range(n_segments):
        start_idx = i * (total_length // n_segments)
        end_idx = min(start_idx + segment_length, total_length)
        
        if end_idx - start_idx < segment_length // 2:
            break
            
        segment = ecg_signal[start_idx:end_idx]
        
        # 分析这个片段
        segment_info = {
            'start_time': start_idx / sampling_rate,
            'end_time': end_idx / sampling_rate,
            'segment': segment,
            'max_value': np.max(segment),
            'min_value': np.min(segment),
            'max_abs': np.max(np.abs(segment)),
            'range': np.max(segment) - np.min(segment)
        }
        
        # 判断主要极性
        if abs(segment_info['max_value']) > abs(segment_info['min_value']):
            segment_info['dominant_polarity'] = 'positive'
        else:
            segment_info['dominant_polarity'] = 'negative'
        
        segments_info.append(segment_info)
        
        print(f"   片段 {i+1}: {segment_info['start_time']:.1f}-{segment_info['end_time']:.1f}s, "
              f"范围: {segment_info['min_value']:.1f} 到 {segment_info['max_value']:.1f} μV, "
              f"主导极性: {segment_info['dominant_polarity']}")
    
    return segments_info

def detect_with_multiple_methods(ecg_signal, sampling_rate):
    """使用多种方法检测R波"""
    print(f"\n🔍 使用多种方法检测R波...")
    
    results = {}
    
    # 1. NeuroKit2方法
    if NEUROKIT_AVAILABLE:
        try:
            signals, info = nk.ecg_process(ecg_signal, sampling_rate=sampling_rate, method='neurokit')
            r_peaks_nk = info["ECG_R_Peaks"]
            results['neurokit2'] = {
                'r_peaks': r_peaks_nk,
                'count': len(r_peaks_nk),
                'method': 'NeuroKit2'
            }
            print(f"   NeuroKit2: {len(r_peaks_nk)} 个R波")
        except Exception as e:
            print(f"   NeuroKit2 失败: {e}")
    
    # 2. 简单峰值检测 - 正向
    from scipy import signal
    peaks_pos, _ = signal.find_peaks(
        ecg_signal,
        height=np.mean(ecg_signal) + 2*np.std(ecg_signal),
        distance=int(0.4 * sampling_rate)
    )
    results['peaks_positive'] = {
        'r_peaks': peaks_pos,
        'count': len(peaks_pos),
        'method': 'Simple_Positive'
    }
    print(f"   正向峰值检测: {len(peaks_pos)} 个峰值")
    
    # 3. 简单峰值检测 - 负向
    peaks_neg, _ = signal.find_peaks(
        -ecg_signal,
        height=-(np.mean(ecg_signal) - 2*np.std(ecg_signal)),
        distance=int(0.4 * sampling_rate)
    )
    results['peaks_negative'] = {
        'r_peaks': peaks_neg,
        'count': len(peaks_neg),
        'method': 'Simple_Negative'
    }
    print(f"   负向峰值检测: {len(peaks_neg)} 个峰值")
    
    # 4. 绝对值峰值检测
    abs_signal = np.abs(ecg_signal)
    peaks_abs, _ = signal.find_peaks(
        abs_signal,
        height=np.mean(abs_signal) + 2*np.std(abs_signal),
        distance=int(0.4 * sampling_rate)
    )
    results['peaks_absolute'] = {
        'r_peaks': peaks_abs,
        'count': len(peaks_abs),
        'method': 'Absolute_Value'
    }
    print(f"   绝对值峰值检测: {len(peaks_abs)} 个峰值")
    
    return results

def visualize_detailed_analysis(data, segments_info, detection_results, save_dir):
    """生成详细的分析可视化"""
    print(f"\n📊 生成详细分析可视化...")
    
    ecg_signal = data['ecg11_signal']
    sampling_rate = data['sampling_rate']
    
    # 创建时间轴
    time_axis = np.arange(len(ecg_signal)) / sampling_rate
    
    # 只显示前60秒
    display_duration = 60
    display_samples = int(display_duration * sampling_rate)
    
    if len(ecg_signal) > display_samples:
        ecg_display = ecg_signal[:display_samples]
        time_display = time_axis[:display_samples]
    else:
        ecg_display = ecg_signal
        time_display = time_axis
    
    # 创建多子图
    fig, axes = plt.subplots(4, 1, figsize=(15, 16))
    
    # 1. 原始信号
    axes[0].plot(time_display, ecg_display, 'k-', linewidth=0.8, alpha=0.7)
    axes[0].set_title('ECG11 原始信号', fontsize=12, fontweight='bold')
    axes[0].set_ylabel('幅度 (μV)')
    axes[0].grid(True, alpha=0.3)
    
    # 标记分析片段
    for i, seg in enumerate(segments_info[:3]):  # 只显示前3个片段
        if seg['end_time'] <= display_duration:
            axes[0].axvspan(seg['start_time'], seg['end_time'], 
                           alpha=0.2, color=f'C{i}', 
                           label=f'片段{i+1} ({seg["dominant_polarity"]})')
    axes[0].legend()
    
    # 2. NeuroKit2检测结果
    if 'neurokit2' in detection_results:
        axes[1].plot(time_display, ecg_display, 'k-', linewidth=0.8, alpha=0.7)
        nk_peaks = detection_results['neurokit2']['r_peaks']
        nk_peaks_display = nk_peaks[nk_peaks < display_samples]
        if len(nk_peaks_display) > 0:
            nk_times = nk_peaks_display / sampling_rate
            nk_amplitudes = ecg_display[nk_peaks_display]
            axes[1].scatter(nk_times, nk_amplitudes, c='blue', s=50, marker='o', 
                           alpha=0.8, zorder=5, label=f'NeuroKit2 (n={len(nk_peaks_display)})')
        axes[1].set_title('NeuroKit2 检测结果', fontsize=12, fontweight='bold')
        axes[1].set_ylabel('幅度 (μV)')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
    
    # 3. 正向vs负向峰值检测对比
    axes[2].plot(time_display, ecg_display, 'k-', linewidth=0.8, alpha=0.7, label='ECG信号')
    
    # 正向峰值
    if 'peaks_positive' in detection_results:
        pos_peaks = detection_results['peaks_positive']['r_peaks']
        pos_peaks_display = pos_peaks[pos_peaks < display_samples]
        if len(pos_peaks_display) > 0:
            pos_times = pos_peaks_display / sampling_rate
            pos_amplitudes = ecg_display[pos_peaks_display]
            axes[2].scatter(pos_times, pos_amplitudes, c='red', s=60, marker='^', 
                           alpha=0.7, zorder=5, label=f'正向峰值 (n={len(pos_peaks_display)})')
    
    # 负向峰值
    if 'peaks_negative' in detection_results:
        neg_peaks = detection_results['peaks_negative']['r_peaks']
        neg_peaks_display = neg_peaks[neg_peaks < display_samples]
        if len(neg_peaks_display) > 0:
            neg_times = neg_peaks_display / sampling_rate
            neg_amplitudes = ecg_display[neg_peaks_display]
            axes[2].scatter(neg_times, neg_amplitudes, c='green', s=60, marker='v', 
                           alpha=0.7, zorder=5, label=f'负向峰值 (n={len(neg_peaks_display)})')
    
    axes[2].set_title('正向 vs 负向峰值检测对比', fontsize=12, fontweight='bold')
    axes[2].set_ylabel('幅度 (μV)')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    # 4. 绝对值检测
    abs_signal = np.abs(ecg_display)
    axes[3].plot(time_display, abs_signal, 'purple', linewidth=0.8, alpha=0.7, label='绝对值信号')
    
    if 'peaks_absolute' in detection_results:
        abs_peaks = detection_results['peaks_absolute']['r_peaks']
        abs_peaks_display = abs_peaks[abs_peaks < display_samples]
        if len(abs_peaks_display) > 0:
            abs_times = abs_peaks_display / sampling_rate
            abs_amplitudes = abs_signal[abs_peaks_display]
            axes[3].scatter(abs_times, abs_amplitudes, c='orange', s=50, marker='s', 
                           alpha=0.8, zorder=5, label=f'绝对值峰值 (n={len(abs_peaks_display)})')
    
    axes[3].set_title('绝对值信号峰值检测', fontsize=12, fontweight='bold')
    axes[3].set_xlabel('时间 (秒)')
    axes[3].set_ylabel('幅度 (μV)')
    axes[3].legend()
    axes[3].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图像
    save_path = save_dir / "ecg_polarity_verification.png"
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ 详细分析图已保存: {save_path}")
    
    return fig

def generate_analysis_report(data, segments_info, detection_results):
    """生成分析报告"""
    print(f"\n📋 ECG极性分析报告")
    print("="*50)
    
    # 信号统计
    ecg_signal = data['ecg11_signal']
    print(f"📊 信号统计:")
    print(f"   - 最大值: {np.max(ecg_signal):.1f} μV")
    print(f"   - 最小值: {np.min(ecg_signal):.1f} μV")
    print(f"   - 绝对最大值: {np.max(np.abs(ecg_signal)):.1f} μV")
    print(f"   - 极性判断: {'正向主导' if np.max(ecg_signal) > abs(np.min(ecg_signal)) else '负向主导'}")
    
    # 片段分析
    print(f"\n📊 片段极性分析:")
    positive_segments = sum(1 for seg in segments_info if seg['dominant_polarity'] == 'positive')
    negative_segments = sum(1 for seg in segments_info if seg['dominant_polarity'] == 'negative')
    print(f"   - 正向主导片段: {positive_segments}")
    print(f"   - 负向主导片段: {negative_segments}")
    print(f"   - 整体趋势: {'正向' if positive_segments > negative_segments else '负向'}")
    
    # 检测结果对比
    print(f"\n📊 检测结果对比:")
    for method, result in detection_results.items():
        print(f"   - {result['method']}: {result['count']} 个峰值")
    
    # 推荐
    print(f"\n🎯 分析结论:")
    
    # 基于信号统计的推荐
    if np.max(ecg_signal) > abs(np.min(ecg_signal)):
        signal_recommendation = "正向"
    else:
        signal_recommendation = "负向"
    
    # 基于片段分析的推荐
    if positive_segments > negative_segments:
        segment_recommendation = "正向"
    else:
        segment_recommendation = "负向"
    
    print(f"   - 基于信号统计: R波应为{signal_recommendation}")
    print(f"   - 基于片段分析: R波应为{segment_recommendation}")
    
    if signal_recommendation == segment_recommendation:
        print(f"   ✅ 一致结论: ECG11通道的R波为{signal_recommendation}")
        if signal_recommendation == "正向":
            print(f"   💡 建议: NeuroKit2的检测结果更可能正确")
        else:
            print(f"   💡 建议: 原版本的检测结果更可能正确")
    else:
        print(f"   ⚠️ 结论不一致，需要进一步分析")

def main():
    """主函数"""
    print("🚀 ECG R波极性验证分析")
    print("="*50)
    
    # 1. 加载和分析ECG信号
    data = load_and_analyze_ecg()
    
    # 2. 分析信号片段
    segments_info = analyze_signal_segments(data['ecg11_signal'], data['sampling_rate'])
    
    # 3. 多种方法检测R波
    detection_results = detect_with_multiple_methods(data['ecg11_signal'], data['sampling_rate'])
    
    # 4. 生成可视化
    save_dir = Path("D:/ecgeeg/30-数据分析/5-HBA/result/ecg_polarity_verification")
    save_dir.mkdir(parents=True, exist_ok=True)
    
    visualize_detailed_analysis(data, segments_info, detection_results, save_dir)
    
    # 5. 生成分析报告
    generate_analysis_report(data, segments_info, detection_results)
    
    print(f"\n🎉 分析完成!")
    print(f"📁 结果保存在: {save_dir}")

if __name__ == "__main__":
    main()
