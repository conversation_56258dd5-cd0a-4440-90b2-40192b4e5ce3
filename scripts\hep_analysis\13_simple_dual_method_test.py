#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化双方法HEP测试脚本 v1.0
============================

快速测试两种HEP提取方法：
1. 方法A：基于ECG通道的R波检测
2. 方法B：基于EEG心电伪迹的R波检测

目标：
- 快速验证两种方法的可行性
- 对比波形质量
- 为完整分析提供参考

作者：研究团队
日期：2025年6月
版本：1.0 - 简化测试版本
"""

import numpy as np
import mne
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
from scipy import signal
import random
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class SimpleDualMethodTester:
    """简化双方法HEP测试器"""

    def __init__(self):
        """初始化测试器"""
        self.data_dir = Path(r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突")
        self.output_dir = Path("../../result/simple_dual_method_test")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 简化参数
        self.params = {
            'time_window': (-0.2, 0.6),  # 时间窗口
            'baseline_window': (-0.1, 0),  # 基线窗口
            'sampling_rate': 500,
            'eeg_filter': {'l_freq': 0.5, 'h_freq': 40.0}
        }
        
        print("="*60)
        print("简化双方法HEP测试器")
        print("="*60)
        print(f"数据目录: {self.data_dir}")
        print(f"输出目录: {self.output_dir}")

    def load_test_file(self):
        """加载一个测试文件"""
        # 查找可用文件
        fif_files = list(self.data_dir.glob("*.fif"))
        if not fif_files:
            print("❌ 未找到.fif文件")
            return None
        
        # 随机选择一个文件
        test_file = random.choice(fif_files)
        print(f"📁 选择测试文件: {test_file.name}")
        
        try:
            raw = mne.io.read_raw_fif(test_file, preload=True, verbose=False)
            print(f"📥 加载成功: {raw.info['nchan']}通道, {raw.times[-1]:.1f}秒")
            
            # 最小滤波
            raw.filter(
                l_freq=self.params['eeg_filter']['l_freq'],
                h_freq=self.params['eeg_filter']['h_freq'],
                verbose=False
            )
            print(f"🔧 滤波完成: {self.params['eeg_filter']['l_freq']}-{self.params['eeg_filter']['h_freq']}Hz")
            
            return raw
            
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            return None

    def method_a_ecg_detection(self, raw):
        """方法A：ECG通道R波检测"""
        print("\n🫀 方法A：ECG通道R波检测")
        
        try:
            # 获取ECG数据（后58通道）
            ecg_data = raw.get_data()[61:, :]
            ecg11_signal = ecg_data[10, :]  # ECG11通道
            sampling_rate = raw.info['sfreq']
            
            # ECG滤波（5-15Hz）
            sos = signal.butter(4, [5, 15], btype='band', fs=sampling_rate, output='sos')
            ecg_filtered = signal.sosfilt(sos, ecg11_signal)
            
            # R波检测
            ecg_squared = ecg_filtered ** 2
            window_size = int(0.08 * sampling_rate)
            ecg_smoothed = np.convolve(ecg_squared, np.ones(window_size)/window_size, mode='same')
            
            threshold = np.mean(ecg_smoothed) + 2 * np.std(ecg_smoothed)
            peaks, _ = signal.find_peaks(
                ecg_smoothed,
                height=threshold,
                distance=int(0.3 * sampling_rate)
            )
            
            # 精确定位
            refined_peaks = []
            search_window = int(0.05 * sampling_rate)
            
            for peak in peaks:
                start_idx = max(0, peak - search_window)
                end_idx = min(len(ecg11_signal), peak + search_window)
                segment = ecg11_signal[start_idx:end_idx]
                
                if np.max(segment) > abs(np.min(segment)):
                    local_peak = np.argmax(segment)
                else:
                    local_peak = np.argmin(segment)
                
                refined_peaks.append(start_idx + local_peak)
            
            r_peaks = np.array(refined_peaks)
            
            if len(r_peaks) > 1:
                rr_intervals = np.diff(r_peaks) / sampling_rate
                heart_rate = 60 / np.mean(rr_intervals)
            else:
                heart_rate = 0
            
            print(f"  ✅ 检测到 {len(r_peaks)} 个R波")
            print(f"  💓 心率: {heart_rate:.1f} bpm")
            
            return r_peaks, {'method': 'ECG', 'n_peaks': len(r_peaks), 'heart_rate': heart_rate}
            
        except Exception as e:
            print(f"  ❌ ECG方法失败: {e}")
            return None, None

    def method_b_eeg_detection(self, raw):
        """方法B：EEG心电伪迹检测"""
        print("\n🧠 方法B：EEG心电伪迹检测")
        
        try:
            # 获取EEG数据（前61通道）
            eeg_data = raw.get_data()[:61, :]
            sampling_rate = raw.info['sfreq']
            
            # 心电敏感电极
            cardiac_channels = ['Fp1', 'Fp2', 'F7', 'F8']
            best_peaks = None
            best_quality = 0
            best_channel = None
            
            for channel in cardiac_channels:
                if channel in raw.ch_names[:61]:
                    try:
                        ch_idx = raw.ch_names.index(channel)
                        signal_data = eeg_data[ch_idx, :]
                        
                        # 心跳检测滤波
                        sos = signal.butter(4, [1, 20], btype='band', fs=sampling_rate, output='sos')
                        filtered_data = signal.sosfilt(sos, signal_data)
                        
                        # 组合信号
                        abs_signal = np.abs(filtered_data)
                        gradient = np.abs(np.gradient(filtered_data))
                        combined_signal = abs_signal + 0.5 * gradient
                        
                        # 简单阈值检测
                        threshold = np.mean(combined_signal) + 2 * np.std(combined_signal)
                        peaks, _ = signal.find_peaks(
                            combined_signal,
                            height=threshold,
                            distance=int(0.4 * sampling_rate)
                        )
                        
                        # 质量评估
                        if len(peaks) > 1:
                            rr_intervals = np.diff(peaks) / sampling_rate
                            heart_rate = 60 / np.mean(rr_intervals)
                            if 40 <= heart_rate <= 120:
                                quality = len(peaks) / 100
                                if quality > best_quality:
                                    best_quality = quality
                                    best_peaks = peaks
                                    best_channel = channel
                    
                    except Exception as e:
                        print(f"    ⚠️ 电极 {channel} 失败: {e}")
                        continue
            
            if best_peaks is not None:
                rr_intervals = np.diff(best_peaks) / sampling_rate
                heart_rate = 60 / np.mean(rr_intervals)
                print(f"  ✅ 最佳电极: {best_channel}")
                print(f"  ✅ 检测到 {len(best_peaks)} 个R波")
                print(f"  💓 心率: {heart_rate:.1f} bpm")
                
                return best_peaks, {'method': 'EEG', 'channel': best_channel, 'n_peaks': len(best_peaks), 'heart_rate': heart_rate}
            else:
                print(f"  ❌ EEG方法失败")
                return None, None
                
        except Exception as e:
            print(f"  ❌ EEG方法失败: {e}")
            return None, None

    def extract_hep(self, raw, r_peaks, method_name):
        """提取HEP"""
        print(f"\n📊 提取HEP - {method_name}")
        
        try:
            eeg_data = raw.get_data()[:61, :]
            sampling_rate = raw.info['sfreq']
            
            # 计算样本点
            pre_samples = int(abs(self.params['time_window'][0]) * sampling_rate)
            post_samples = int(self.params['time_window'][1] * sampling_rate)
            
            # 提取epochs
            epochs = []
            for r_peak in r_peaks:
                start_idx = r_peak - pre_samples
                end_idx = r_peak + post_samples
                
                if start_idx >= 0 and end_idx < eeg_data.shape[1]:
                    epoch = eeg_data[:, start_idx:end_idx]
                    
                    # 基线校正
                    baseline_start = int((self.params['baseline_window'][0] - self.params['time_window'][0]) * sampling_rate)
                    baseline_end = int((self.params['baseline_window'][1] - self.params['time_window'][0]) * sampling_rate)
                    
                    if baseline_start >= 0 and baseline_end <= epoch.shape[1]:
                        baseline_mean = np.mean(epoch[:, baseline_start:baseline_end], axis=1, keepdims=True)
                        epoch_corrected = epoch - baseline_mean
                        epochs.append(epoch_corrected)
            
            if epochs:
                hep_epochs = np.array(epochs)
                hep_average = np.mean(hep_epochs, axis=0)
                
                # 创建时间轴
                times = np.linspace(self.params['time_window'][0], self.params['time_window'][1], hep_average.shape[1])
                
                print(f"  ✅ 提取了 {len(epochs)} 个epochs")
                return hep_average, times
            else:
                print(f"  ❌ 未提取到有效epochs")
                return None, None
                
        except Exception as e:
            print(f"  ❌ HEP提取失败: {e}")
            return None, None

    def create_comparison_plot(self, hep_a, hep_b, times, info_a, info_b, filename):
        """创建对比图"""
        print(f"\n🎨 创建对比可视化")

        try:
            fig, axes = plt.subplots(2, 3, figsize=(15, 10))
            fig.suptitle(f'双方法HEP对比 - {filename}', fontsize=14, fontweight='bold')

            # 选择关键电极
            key_electrodes = ['Fp1', 'Fz', 'Cz', 'Pz', 'C3', 'C4']
            electrode_indices = [0, 10, 20, 30, 15, 25]  # 简化的电极索引

            times_ms = times * 1000  # 转换为毫秒

            for i, (electrode, idx) in enumerate(zip(key_electrodes, electrode_indices)):
                if i >= 6:
                    break

                ax = axes[i // 3, i % 3]

                # 绘制方法A结果
                if hep_a is not None and idx < hep_a.shape[0]:
                    ax.plot(times_ms, hep_a[idx, :] * 1e6, 'b-', linewidth=2,
                           label=f'方法A (ECG, n={info_a["n_peaks"]})')

                # 绘制方法B结果
                if hep_b is not None and idx < hep_b.shape[0]:
                    ax.plot(times_ms, hep_b[idx, :] * 1e6, 'r-', linewidth=2,
                           label=f'方法B (EEG, n={info_b["n_peaks"]})')

                # 标记R波和HEP窗口
                ax.axvline(x=0, color='black', linestyle='--', alpha=0.7, label='R波')
                ax.axvspan(200, 600, alpha=0.1, color='green')

                ax.set_title(f'{electrode}', fontsize=12)
                ax.set_xlabel('时间 (ms)')
                ax.set_ylabel('幅度 (μV)')
                ax.grid(True, alpha=0.3)

                if i == 0:
                    ax.legend(fontsize=8)

            plt.tight_layout()

            # 保存图像
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            plot_path = self.output_dir / f"dual_method_comparison_{timestamp}.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"  ✅ 对比图已保存: {plot_path}")
            return plot_path

        except Exception as e:
            print(f"  ❌ 可视化失败: {e}")
            return None

    def run_test(self, n_tests=3):
        """运行测试"""
        print("\n" + "="*60)
        print(f"开始双方法HEP测试 (测试{n_tests}个文件)")
        print("="*60)

        results_summary = []

        for test_num in range(n_tests):
            print(f"\n{'='*40}")
            print(f"测试 {test_num + 1}/{n_tests}")
            print("="*40)

            # 1. 加载测试文件
            raw = self.load_test_file()
            if raw is None:
                continue

            # 2. 方法A：ECG检测
            r_peaks_a, info_a = self.method_a_ecg_detection(raw)

            # 3. 方法B：EEG检测
            r_peaks_b, info_b = self.method_b_eeg_detection(raw)

            # 4. 提取HEP
            hep_a, times_a = None, None
            hep_b, times_b = None, None

            if r_peaks_a is not None and len(r_peaks_a) >= 10:
                hep_a, times_a = self.extract_hep(raw, r_peaks_a, "方法A")

            if r_peaks_b is not None and len(r_peaks_b) >= 10:
                hep_b, times_b = self.extract_hep(raw, r_peaks_b, "方法B")

            # 5. 创建对比图
            if hep_a is not None or hep_b is not None:
                times = times_a if times_a is not None else times_b
                filename = raw.filenames[0] if hasattr(raw, 'filenames') and raw.filenames else f"test_{test_num+1}"
                self.create_comparison_plot(hep_a, hep_b, times, info_a or {}, info_b or {}, filename)

            # 记录结果
            test_result = {
                'test_num': test_num + 1,
                'filename': filename,
                'method_a_success': info_a is not None,
                'method_b_success': info_b is not None,
                'method_a_peaks': info_a['n_peaks'] if info_a else 0,
                'method_b_peaks': info_b['n_peaks'] if info_b else 0,
                'method_a_hr': info_a['heart_rate'] if info_a else 0,
                'method_b_hr': info_b['heart_rate'] if info_b else 0
            }
            results_summary.append(test_result)

        # 6. 打印总结
        print(f"\n" + "="*60)
        print("多文件测试结果总结")
        print("="*60)

        method_a_success = sum(1 for r in results_summary if r['method_a_success'])
        method_b_success = sum(1 for r in results_summary if r['method_b_success'])

        print(f"总测试文件数: {len(results_summary)}")
        print(f"方法A成功率: {method_a_success}/{len(results_summary)} ({method_a_success/len(results_summary)*100:.1f}%)")
        print(f"方法B成功率: {method_b_success}/{len(results_summary)} ({method_b_success/len(results_summary)*100:.1f}%)")

        if method_a_success > 0:
            avg_a_peaks = np.mean([r['method_a_peaks'] for r in results_summary if r['method_a_success']])
            avg_a_hr = np.mean([r['method_a_hr'] for r in results_summary if r['method_a_success']])
            print(f"方法A平均: {avg_a_peaks:.1f}个R波, 心率{avg_a_hr:.1f}bpm")

        if method_b_success > 0:
            avg_b_peaks = np.mean([r['method_b_peaks'] for r in results_summary if r['method_b_success']])
            avg_b_hr = np.mean([r['method_b_hr'] for r in results_summary if r['method_b_success']])
            print(f"方法B平均: {avg_b_peaks:.1f}个R波, 心率{avg_b_hr:.1f}bpm")

        # 总体推荐
        if method_a_success > method_b_success:
            print(f"\n💡 总体推荐: 方法A (ECG) - 成功率更高")
        elif method_b_success > method_a_success:
            print(f"\n💡 总体推荐: 方法B (EEG) - 成功率更高")
        elif method_a_success == method_b_success and method_a_success > 0:
            print(f"\n💡 两种方法成功率相同，建议根据具体需求选择")
        else:
            print(f"\n❌ 两种方法在测试文件上都表现不佳")

        print("="*60)
        return True


if __name__ == "__main__":
    print("简化双方法HEP测试")
    print("="*30)
    print("快速测试两种HEP提取方法的可行性")
    print("方法A：基于ECG通道的R波检测")
    print("方法B：基于EEG心电伪迹的R波检测")
    print("="*30)

    # 创建测试器并运行
    tester = SimpleDualMethodTester()
    tester.run_test()
