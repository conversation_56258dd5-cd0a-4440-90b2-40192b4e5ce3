#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用NeuroKit2进行专业R波检测的HEP分析器

主要特点：
1. 使用NeuroKit2的专业R波检测算法
2. 多种检测方法自动选择最佳结果
3. 自动质量评估和伪迹清理
4. 支持极性自动识别
5. 专门针对HEP分析优化

作者: HEP Analysis Team
日期: 2024-12-19
"""

import numpy as np
import pandas as pd
import mne
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 安装NeuroKit2: pip install neurokit2
try:
    import neurokit2 as nk
    NEUROKIT_AVAILABLE = True
    print("✅ NeuroKit2 已安装并可用")
except ImportError:
    NEUROKIT_AVAILABLE = False
    print("❌ NeuroKit2 未安装，请运行: pip install neurokit2")

class NeuroKit2HEPAnalyzer:
    """使用NeuroKit2的专业HEP分析器"""
    
    def __init__(self, data_dir, enable_quality_control=False):
        """
        初始化分析器
        
        Parameters:
        -----------
        data_dir : str
            数据目录路径
        enable_quality_control : bool
            是否启用质量控制
        """
        if not NEUROKIT_AVAILABLE:
            raise ImportError("请先安装NeuroKit2: pip install neurokit2")
            
        self.data_dir = Path(data_dir)
        self.enable_quality_control = enable_quality_control
        
        # HEP分析参数
        self.hep_params = {
            'time_window': (-0.2, 0.8),  # HEP时间窗口
            'baseline_window': (-0.2, -0.05),  # 基线校正窗口
            'min_heartbeats': 30,  # 最少心跳数
            'rr_interval_range': (0.4, 2.0),  # 有效R-R间隔范围
        }
        
        # NeuroKit2检测参数
        self.nk_params = {
            'methods': ['neurokit', 'pantompkins1985', 'hamilton2002', 'christov2004'],
            'correct_artifacts': True,
            'show_info': True
        }
        
        # 实验条件定义
        self.conditions = {
            'rest_1': '静息态1',
            'rest_2': '静息态2', 
            'rest_3': '静息态3',
            'test_1': '测试态1',
            'test_2': '测试态2',
            'test_3': '测试态3',
            'prac': '练习'
        }
        
        # 文件匹配模式
        self.condition_patterns = {
            'rest_1': '*_01_*_rest_*.fif',
            'rest_2': '*_02_*_rest_*.fif',
            'rest_3': '*_03_*_rest_*.fif',
            'test_1': '*_01_*_test_*.fif',
            'test_2': '*_02_*_test_*.fif',
            'test_3': '*_03_*_test_*.fif',
            'prac': '*_prac_*.fif'
        }
        
        print(f"🔧 NeuroKit2 HEP分析器初始化完成")
        print(f"📁 数据目录: {self.data_dir}")
        print(f"🎛️ 质量控制: {'启用' if self.enable_quality_control else '禁用'}")

    def detect_r_waves_neurokit(self, ecg_data, sampling_rate, channel_name="ECG11"):
        """
        使用NeuroKit2进行专业R波检测
        
        Parameters:
        -----------
        ecg_data : array
            ECG信号数据 (channels x samples)
        sampling_rate : float
            采样率
        channel_name : str
            ECG通道名称
            
        Returns:
        --------
        r_peaks : array
            R波位置
        quality_info : dict
            检测质量信息
        """
        print(f"    🔍 使用NeuroKit2检测R波 (通道: {channel_name})")
        
        # 使用固定ECG11通道（索引10）
        fixed_channel = 10
        if fixed_channel >= ecg_data.shape[0]:
            raise ValueError(f"ECG通道{fixed_channel+1}不存在，数据只有{ecg_data.shape[0]}个通道")
            
        ecg_signal = ecg_data[fixed_channel, :]
        print(f"    📊 信号长度: {len(ecg_signal)} 样本点, 采样率: {sampling_rate} Hz")
        
        # 尝试多种NeuroKit2检测方法
        best_result = None
        best_quality = 0
        best_method = None
        
        for method in self.nk_params['methods']:
            try:
                print(f"      🧪 尝试方法: {method}")
                
                # 使用NeuroKit2处理ECG信号
                signals, info = nk.ecg_process(
                    ecg_signal, 
                    sampling_rate=sampling_rate,
                    method=method
                )
                
                # 获取R波位置
                r_peaks = info["ECG_R_Peaks"]
                
                if len(r_peaks) == 0:
                    print(f"        ⚠️ {method}: 未检测到R波")
                    continue
                
                # 计算质量指标
                quality_score = self._evaluate_detection_quality_nk(
                    ecg_signal, r_peaks, sampling_rate, signals
                )
                
                print(f"        📈 {method}: {len(r_peaks)}个R波, 质量={quality_score:.3f}")
                
                if quality_score > best_quality:
                    best_quality = quality_score
                    best_result = {
                        'r_peaks': r_peaks,
                        'signals': signals,
                        'info': info,
                        'method': method
                    }
                    best_method = method
                    
            except Exception as e:
                print(f"        ❌ {method} 失败: {e}")
                continue
        
        if best_result is None:
            print(f"    ❌ 所有NeuroKit2方法都失败")
            return np.array([]), {'quality': 0, 'method': 'failed'}
        
        print(f"    ✅ 最佳方法: {best_method}, {len(best_result['r_peaks'])}个R波, 质量={best_quality:.3f}")
        
        # 返回质量信息
        quality_info = {
            'quality': best_quality,
            'method': best_method,
            'total_peaks': len(best_result['r_peaks']),
            'signals': best_result['signals'],
            'info': best_result['info']
        }
        
        return best_result['r_peaks'], quality_info

    def _evaluate_detection_quality_nk(self, ecg_signal, r_peaks, sampling_rate, signals):
        """
        评估NeuroKit2检测质量
        
        Parameters:
        -----------
        ecg_signal : array
            原始ECG信号
        r_peaks : array
            检测到的R波位置
        sampling_rate : float
            采样率
        signals : DataFrame
            NeuroKit2处理后的信号
            
        Returns:
        --------
        quality_score : float
            质量评分 (0-1)
        """
        if len(r_peaks) < 2:
            return 0.0
        
        # 1. R-R间隔一致性
        rr_intervals = np.diff(r_peaks) / sampling_rate
        rr_cv = np.std(rr_intervals) / np.mean(rr_intervals) if np.mean(rr_intervals) > 0 else 1.0
        rr_quality = max(0, 1 - rr_cv * 2)  # 变异系数越小质量越高
        
        # 2. 心率合理性
        mean_hr = 60 / np.mean(rr_intervals) if np.mean(rr_intervals) > 0 else 0
        hr_quality = 1.0 if 50 <= mean_hr <= 120 else 0.5
        
        # 3. R波幅度一致性
        r_amplitudes = []
        for peak in r_peaks:
            if 0 <= peak < len(ecg_signal):
                r_amplitudes.append(abs(ecg_signal[peak]))
        
        if len(r_amplitudes) > 0:
            amp_cv = np.std(r_amplitudes) / np.mean(r_amplitudes)
            amp_quality = max(0, 1 - amp_cv)
        else:
            amp_quality = 0
        
        # 4. 数量充足性
        count_quality = min(1.0, len(r_peaks) / 50)  # 50个以上为满分
        
        # 综合质量评分
        quality_score = (rr_quality * 0.4 + hr_quality * 0.2 + 
                        amp_quality * 0.2 + count_quality * 0.2)
        
        return quality_score

    def extract_hep_epochs_nk(self, eeg_data, r_peaks, sampling_rate, ch_names=None):
        """
        使用NeuroKit2检测结果提取HEP时期
        
        Parameters:
        -----------
        eeg_data : array
            EEG数据 (channels x samples)
        r_peaks : array
            NeuroKit2检测的R波位置
        sampling_rate : float
            采样率
        ch_names : list, optional
            通道名称列表
            
        Returns:
        --------
        hep_epochs : array
            HEP时期数据 (epochs x channels x samples)
        times : array
            时间轴
        ch_names : list
            通道名称列表
        """
        print(f"    🔍 使用NeuroKit2结果提取HEP epochs...")
        print(f"      EEG数据形状: {eeg_data.shape}")
        print(f"      R波数量: {len(r_peaks)}")
        print(f"      采样率: {sampling_rate} Hz")
        
        # 计算时间窗口对应的样本点
        pre_samples = int(abs(self.hep_params['time_window'][0]) * sampling_rate)
        post_samples = int(self.hep_params['time_window'][1] * sampling_rate)
        total_samples = pre_samples + post_samples
        
        # 创建时间轴
        times = np.linspace(self.hep_params['time_window'][0],
                           self.hep_params['time_window'][1],
                           total_samples)
        
        # 如果没有提供通道名称，创建默认的通道名称
        if ch_names is None:
            ch_names = [f'EEG{i+1:02d}' for i in range(eeg_data.shape[0])]
        
        # 提取每个心跳周期的EEG数据
        valid_epochs = []
        
        for i, r_peak in enumerate(r_peaks):
            start_idx = r_peak - pre_samples
            end_idx = r_peak + post_samples
            
            # 检查边界条件
            if start_idx < 0 or end_idx >= eeg_data.shape[1]:
                continue
            
            # 提取epoch
            epoch = eeg_data[:, start_idx:end_idx]
            
            # 基线校正
            baseline_start_time = self.hep_params['baseline_window'][0] - self.hep_params['time_window'][0]
            baseline_end_time = self.hep_params['baseline_window'][1] - self.hep_params['time_window'][0]
            
            baseline_start = int(baseline_start_time * sampling_rate)
            baseline_end = int(baseline_end_time * sampling_rate)
            
            if baseline_start >= 0 and baseline_end <= epoch.shape[1] and baseline_start < baseline_end:
                baseline_mean = np.mean(epoch[:, baseline_start:baseline_end], axis=1, keepdims=True)
                epoch_corrected = epoch - baseline_mean
                valid_epochs.append(epoch_corrected)
        
        print(f"      ✅ 成功提取 {len(valid_epochs)} 个有效HEP epochs")
        
        if len(valid_epochs) > 0:
            hep_epochs = np.array(valid_epochs)
            return hep_epochs, times, ch_names
        else:
            return None, times, ch_names

    def analyze_single_file_nk(self, file_info):
        """
        使用NeuroKit2分析单个文件
        
        Parameters:
        -----------
        file_info : dict
            文件信息字典
            
        Returns:
        --------
        result : dict
            分析结果
        """
        print(f"    📁 文件: {file_info['file_name']}")
        
        # 读取数据
        raw = mne.io.read_raw_fif(file_info['file_path'], preload=True, verbose=False)
        
        # 获取基本信息
        sampling_rate = raw.info['sfreq']
        ch_names = raw.ch_names
        data = raw.get_data() * 1000  # mV -> μV
        
        print(f"    📊 数据形状: {data.shape}, 采样率: {sampling_rate} Hz")
        
        # 分离EEG和ECG数据
        eeg_data = data[:61, :]  # 前61个通道是EEG
        ecg_data = data[61:, :]  # 后58个通道是ECG
        
        # 使用NeuroKit2检测R波
        r_peaks, quality_info = self.detect_r_waves_neurokit(ecg_data, sampling_rate)
        
        if len(r_peaks) < self.hep_params['min_heartbeats']:
            return {
                'success': False,
                'error': f"R波数量不足: {len(r_peaks)} < {self.hep_params['min_heartbeats']}",
                'r_peaks_count': len(r_peaks),
                'quality_info': quality_info
            }
        
        # 提取HEP时期
        hep_epochs, times, ch_names_used = self.extract_hep_epochs_nk(
            eeg_data, r_peaks, sampling_rate, ch_names[:61]
        )
        
        if hep_epochs is None:
            return {
                'success': False,
                'error': "未提取到有效的HEP时期",
                'r_peaks_count': len(r_peaks),
                'quality_info': quality_info
            }
        
        return {
            'success': True,
            'hep_epochs': hep_epochs,
            'times': times,
            'channel_names': ch_names_used,
            'file_info': file_info,
            'r_peaks_count': len(r_peaks),
            'quality_info': quality_info,
            'neurokit_method': quality_info['method']
        }

if __name__ == "__main__":
    # 使用示例
    data_dir = "D:/ecgeeg/18-eegecg手动预处理5-重参考2-双侧乳突"
    
    # 创建NeuroKit2分析器
    analyzer = NeuroKit2HEPAnalyzer(data_dir, enable_quality_control=False)
    
    print("🚀 NeuroKit2 HEP分析器准备就绪")
    print("📝 使用方法:")
    print("   1. analyzer.analyze_single_file_nk(file_info)")
    print("   2. 或者直接调用 analyzer.detect_r_waves_neurokit(ecg_data, sampling_rate)")
