#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HEP数据读取和分析脚本

用于读取和分析保存的HEP fif文件数据。
功能包括：
- 读取指定目录下的所有HEP数据文件
- 按条件和被试分组统计数据
- 计算基本统计指标
- 可视化数据

作者：研究团队
日期：2024年3月
"""

import os
import numpy as np
import mne
from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class HEPDataReader:
    """HEP数据读取器"""
    
    def __init__(self, data_dir):
        """
        初始化HEP数据读取器

        Parameters:
        -----------
        data_dir : str
            数据目录路径，包含所有条件的子目录
        """
        self.data_dir = Path(data_dir)
        self.conditions = ['prac', 'rest_1', 'rest_2', 'rest_3', 'test_1', 'test_2', 'test_3']
        self.condition_names = {
            'prac': '练习状态',
            'rest_1': '静息状态1',
            'rest_2': '静息状态2',
            'rest_3': '静息状态3',
            'test_1': '测试状态1',
            'test_2': '测试状态2',
            'test_3': '测试状态3'
        }
        
        # 存储读取的数据
        self.data = {
            'epochs': {},      # 按条件存储epochs数据
            'subjects': {},    # 按条件存储被试信息
            'statistics': {}   # 按条件存储统计信息
        }
        
        print("="*80)
        print("HEP数据读取与分析")
        print("="*80)
        print(f"数据目录: {self.data_dir}")
        
    def scan_files(self):
        """扫描所有条件目录下的fif文件"""
        print("\n开始扫描数据文件...")
        
        all_files = {}
        total_files = 0
        
        for condition in self.conditions:
            condition_dir = self.data_dir / condition
            if not condition_dir.exists():
                print(f"  ⚠️ 未找到条件目录: {condition}")
                continue
                
            # 查找该条件下的所有fif文件
            fif_files = list(condition_dir.glob("*-epo.fif"))
            all_files[condition] = fif_files
            total_files += len(fif_files)
            
            print(f"  条件 {condition} ({self.condition_names[condition]}): {len(fif_files)}个文件")
            
        print(f"\n共找到 {total_files} 个数据文件")
        return all_files
        
    def inspect_raw(self, raw, file_name):
        """检查raw对象的详细结构"""
        print(f"\n文件结构分析: {file_name}")
        print("-" * 50)
        
        # 基本信息
        print("1. 基本信息:")
        print(f"  采样率: {raw.info['sfreq']} Hz")
        print(f"  数据时长: {raw.times[-1]:.2f} 秒")
        print(f"  采样点数: {len(raw.times)}")
        
        # 通道信息
        print("\n2. 通道信息:")
        n_eeg = len([ch for ch in raw.ch_names if ch.startswith('EEG')])
        n_ecg = len([ch for ch in raw.ch_names if ch.startswith('ECG')])
        print(f"  总通道数: {len(raw.ch_names)}")
        print(f"  脑电通道: {n_eeg}")
        print(f"  心电通道: {n_ecg}")
        print("\n  通道类型:")
        for ch_type in raw.get_channel_types():
            count = len(raw.get_channel_types(unique=False, only_data_chs=False))
            print(f"    - {ch_type}: {count}个")
        
        # 数据维度
        print("\n3. 数据维度:")
        print(f"  数据形状: {raw.get_data().shape}")
        
        # 数据统计
        print("\n4. 数据统计:")
        data = raw.get_data()
        print(f"  数值范围: {data.min():.6f} 到 {data.max():.6f}")
        print(f"  均值: {data.mean():.6f}")
        print(f"  标准差: {data.std():.6f}")
        
        # 其他信息
        print("\n5. 其他信息:")
        print(f"  高通滤波: {raw.info['highpass']} Hz")
        print(f"  低通滤波: {raw.info['lowpass']} Hz")
        if raw.info.get('description'):
            print(f"  描述: {raw.info['description']}")
        
        print("-" * 50)

    def analyze_epochs_data(self, epochs, file_name):
        """分析epochs数据的具体内容"""
        print(f"\n分析Epochs数据: {file_name}")
        print("-" * 50)
        
        # 获取数据数组
        data = epochs.get_data()
        
        # 数据基本信息
        print("1. 数据维度:")
        print(f"  形状: {data.shape}")
        print(f"  维度说明: [epochs数量 x 通道数 x 时间点数]")
        
        # 分通道类型的统计
        eeg_channels = [i for i, ch in enumerate(epochs.ch_names) if ch.startswith('EEG')]
        ecg_channels = [i for i, ch in enumerate(epochs.ch_names) if ch.startswith('ECG')]
        
        print("\n2. 脑电数据统计:")
        if eeg_channels:
            eeg_data = data[:, eeg_channels, :]
            print(f"  通道数量: {len(eeg_channels)}")
            print(f"  数值范围: {eeg_data.min():.2f}μV 到 {eeg_data.max():.2f}μV")
            print(f"  均值: {eeg_data.mean():.2f}μV")
            print(f"  标准差: {eeg_data.std():.2f}μV")
            
            # 每个通道的统计
            print("\n  各通道统计:")
            for i, ch_idx in enumerate(eeg_channels):
                ch_data = data[:, ch_idx, :]
                ch_name = epochs.ch_names[ch_idx]
                print(f"    {ch_name}:")
                print(f"      范围: {ch_data.min():.2f}μV 到 {ch_data.max():.2f}μV")
                print(f"      均值: {ch_data.mean():.2f}μV")
                print(f"      标准差: {ch_data.std():.2f}μV")
        
        print("\n3. 心电数据统计:")
        if ecg_channels:
            ecg_data = data[:, ecg_channels, :]
            print(f"  通道数量: {len(ecg_channels)}")
            print(f"  数值范围: {ecg_data.min():.2f}μV 到 {ecg_data.max():.2f}μV")
            print(f"  均值: {ecg_data.mean():.2f}μV")
            print(f"  标准差: {ecg_data.std():.2f}μV")
            
            # 每个通道的统计
            print("\n  各通道统计:")
            for i, ch_idx in enumerate(ecg_channels):
                ch_data = data[:, ch_idx, :]
                ch_name = epochs.ch_names[ch_idx]
                print(f"    {ch_name}:")
                print(f"      范围: {ch_data.min():.2f}μV 到 {ch_data.max():.2f}μV")
                print(f"      均值: {ch_data.mean():.2f}μV")
                print(f"      标准差: {ch_data.std():.2f}μV")
        
        # 时间信息
        print("\n4. 时间信息:")
        print(f"  时间点数: {len(epochs.times)}")
        print(f"  时间范围: {epochs.times[0]:.3f}s 到 {epochs.times[-1]:.3f}s")
        print(f"  采样率: {epochs.info['sfreq']} Hz")
        
        # Epochs信息
        print("\n5. Epochs分布:")
        print(f"  总数量: {len(epochs)}")
        if epochs.metadata is not None:
            print("\n  按条件分布:")
            for col in epochs.metadata.columns:
                value_counts = epochs.metadata[col].value_counts()
                print(f"\n    {col}:")
                for val, count in value_counts.items():
                    print(f"      {val}: {count}个")
        
        print("-" * 50)
        return data

    def read_single_file(self, file_path):
        """读取单个fif文件并分析其结构"""
        try:
            print(f"\n分析文件: {file_path}")
            
            # 根据文件后缀选择读取方法
            if str(file_path).endswith('-epo.fif'):
                print("检测到Epochs格式文件，使用read_epochs读取...")
                epochs = mne.read_epochs(file_path)
                self.inspect_epochs(epochs, Path(file_path).name)
                
                # 分析epochs数据
                print("\n是否要分析epochs数据的具体内容？(y/n)")
                choice = input("请输入选择: ").strip().lower()
                if choice == 'y':
                    data = self.analyze_epochs_data(epochs, Path(file_path).name)
                    
                    # 是否需要保存数据
                    print("\n是否要保存数据为numpy格式？(y/n)")
                    save_choice = input("请输入选择: ").strip().lower()
                    if save_choice == 'y':
                        save_path = str(Path(file_path).with_suffix('.npy'))
                        np.save(save_path, data)
                        print(f"数据已保存至: {save_path}")
            else:
                print("尝试读取Raw格式文件...")
                data = mne.io.read_raw_fif(file_path, preload=True)
                self.inspect_raw(data, Path(file_path).name)
            return True
        except Exception as e:
            print(f"❌ 读取失败: {str(e)}")
            return False

    def inspect_epochs(self, epochs, file_name):
        """检查epochs对象的详细结构"""
        print(f"\n文件结构分析: {file_name}")
        print("-" * 50)
        
        # 基本信息
        print("1. 基本信息:")
        print(f"  采样率: {epochs.info['sfreq']} Hz")
        print(f"  时间窗口: {epochs.times[0]:.3f}s 到 {epochs.times[-1]:.3f}s")
        print(f"  采样点数: {len(epochs.times)}")
        
        # 通道信息
        print("\n2. 通道信息:")
        n_eeg = len([ch for ch in epochs.ch_names if ch.startswith('EEG')])
        n_ecg = len([ch for ch in epochs.ch_names if ch.startswith('ECG')])
        print(f"  总通道数: {len(epochs.ch_names)}")
        print(f"  脑电通道: {n_eeg}")
        print(f"  心电通道: {n_ecg}")
        
        # 数据维度
        print("\n3. 数据维度:")
        print(f"  Epochs数量: {len(epochs)}")
        a = epochs.get_data()
        print(f"  数据形状: {epochs.get_data().shape}")
        
        # 元数据信息
        print("\n4. 元数据信息:")
        if epochs.metadata is not None:
            print("  包含以下列:")
            for col in epochs.metadata.columns:
                print(f"    - {col}")
        else:
            print("  无元数据")
        
        # 事件信息
        print("\n5. 事件信息:")
        unique_events = np.unique(epochs.events[:, 2])
        print(f"  事件类型: {unique_events}")
        print(f"  事件ID映射: {epochs.event_id}")
        
        # 数据统计
        print("\n6. 数据统计:")
        data = epochs.get_data()
        print(f"  数值范围: {data.min():.6f} 到 {data.max():.6f}")
        print(f"  均值: {data.mean():.6f}")
        print(f"  标准差: {data.std():.6f}")
        
        # 其他信息
        print("\n7. 其他信息:")
        print(f"  高通滤波: {epochs.info['highpass']} Hz")
        print(f"  低通滤波: {epochs.info['lowpass']} Hz")
        if epochs.info.get('description'):
            print(f"  描述: {epochs.info['description']}")
        
        print("-" * 50)

    def read_condition_data(self, condition, files):
        """读取单个条件的所有数据文件"""
        print(f"\n读取条件 {condition} ({self.condition_names[condition]}) 的数据...")
        
        data_list = []
        subjects = set()
        total_epochs = 0
        
        for file_path in files:
            try:
                # 根据文件后缀选择读取方法
                if str(file_path).endswith('-epo.fif'):
                    data = mne.read_epochs(file_path)
                    epochs_count = len(data)
                    data_info = f"Epochs: {epochs_count}"
                else:
                    data = mne.io.read_raw_fif(file_path, preload=True)
                    data_info = f"时长: {data.times[-1]:.2f}秒"
                
                # 获取被试ID
                subject_id = file_path.stem.split('_')[1].replace('sub-', '')
                subjects.add(subject_id)
                
                # 检查文件结构
                if isinstance(data, mne.Epochs):
                    self.inspect_epochs(data, file_path.name)
                    total_epochs += len(data)
                else:
                    self.inspect_raw(data, file_path.name)
                
                # 收集数据
                data_list.append(data)
                
                print(f"  ✅ 成功读取: {file_path.name}")
                print(f"    被试: {subject_id}, {data_info}")
                
            except Exception as e:
                print(f"  ❌ 读取失败 {file_path.name}: {str(e)}")
                continue
        
        # 存储数据
        if data_list:
            self.data['data'] = data_list
            self.data['subjects'][condition] = list(subjects)
            self.data['statistics'][condition] = {
                'total_epochs': total_epochs,
                'n_subjects': len(subjects),
                'n_files': len(files)
            }
            
            print(f"\n条件 {condition} 数据读取完成:")
            print(f"  总epochs数: {total_epochs}")
            print(f"  被试数量: {len(subjects)}")
            
    def read_all_data(self):
        """读取所有条件的数据"""
        # 扫描文件
        all_files = self.scan_files()
        
        # 读取每个条件的数据
        for condition, files in all_files.items():
            if files:
                self.read_condition_data(condition, files)
                
    def get_condition_summary(self, condition):
        """获取单个条件的数据摘要"""
        if condition not in self.data['statistics']:
            return None
            
        stats = self.data['statistics'][condition]
        return {
            'condition': condition,
            'condition_name': self.condition_names[condition],
            'total_epochs': stats['total_epochs'],
            'n_subjects': stats['n_subjects'],
            'n_files': stats['n_files']
        }
        
    def print_summary(self):
        """打印数据摘要"""
        print("\n数据摘要:")
        print("="*80)
        
        total_epochs = 0
        all_subjects = set()
        
        for condition in self.conditions:
            if condition in self.data['statistics']:
                stats = self.data['statistics'][condition]
                total_epochs += stats['total_epochs']
                all_subjects.update(self.data['subjects'][condition])
                
                print(f"\n{self.condition_names[condition]} ({condition}):")
                print(f"  文件数: {stats['n_files']}")
                print(f"  被试数: {stats['n_subjects']}")
                print(f"  总epochs数: {stats['total_epochs']}")
                
        print("\n总计:")
        print(f"  总被试数: {len(all_subjects)}")
        print(f"  总epochs数: {total_epochs}")
        
    def get_epochs_data(self, condition):
        """获取指定条件的epochs数据"""
        if condition in self.data['epochs']:
            return self.data['epochs'][condition]
        return None

    def analyze_all_hep_signals(self):
        """分析所有条件下的HEP信号"""
        print("\n开始分析所有HEP信号...")
        print("="*80)
        
        # 存储所有数据
        all_data = {
            'rest': {'data': [], 'subjects': [], 'condition': []},
            'test': {'data': [], 'subjects': [], 'condition': []}
        }
        
        # 扫描所有文件
        all_files = self.scan_files()
        
        # 按条件读取数据
        for condition, files in all_files.items():
            print(f"\n处理条件: {condition}")
            
            for file_path in files:
                try:
                    # 读取epochs数据
                    epochs = mne.read_epochs(file_path)
                    data = epochs.get_data()
                    
                    # 获取被试ID
                    subject_id = file_path.stem.split('_')[1].replace('sub-', '')
                    
                    # 根据条件分类存储
                    if 'rest' in condition:
                        category = 'rest'
                    elif 'test' in condition:
                        category = 'test'
                    else:
                        continue  # 跳过练习数据
                    
                    all_data[category]['data'].append(data)
                    all_data[category]['subjects'].append(subject_id)
                    all_data[category]['condition'].append(condition)
                    
                    print(f"  ✅ 成功读取: {file_path.name}")
                    print(f"    被试: {subject_id}, Epochs: {len(data)}")
                    
                except Exception as e:
                    print(f"  ❌ 读取失败 {file_path.name}: {str(e)}")
                    continue
        
        # 分析数据
        print("\n数据分析结果:")
        print("="*80)
        
        for category in ['rest', 'test']:
            if all_data[category]['data']:
                print(f"\n{category.upper()}状态分析:")
                
                # 合并所有数据
                all_epochs = np.concatenate(all_data[category]['data'], axis=0)
                
                # 基本信息
                print(f"\n1. 基本信息:")
                print(f"  总被试数: {len(set(all_data[category]['subjects']))}")
                print(f"  总epochs数: {len(all_epochs)}")
                print(f"  数据形状: {all_epochs.shape}")
                
                # 分通道类型的统计
                n_channels = all_epochs.shape[1]
                eeg_data = all_epochs[:, :61, :]  # 前61通道是脑电
                ecg_data = all_epochs[:, 61:, :]  # 后面是心电
                
                # 脑电数据统计
                print(f"\n2. 脑电数据统计 (前61通道):")
                print(f"  数值范围: {eeg_data.min():.2f}μV 到 {eeg_data.max():.2f}μV")
                print(f"  均值: {eeg_data.mean():.2f}μV")
                print(f"  标准差: {eeg_data.std():.2f}μV")
                
                # 心电数据统计
                print(f"\n3. 心电数据统计 (后{n_channels-61}通道):")
                print(f"  数值范围: {ecg_data.min():.2f}μV 到 {ecg_data.max():.2f}μV")
                print(f"  均值: {ecg_data.mean():.2f}μV")
                print(f"  标准差: {ecg_data.std():.2f}μV")
                
                # 是否保存数据
                print(f"\n是否要保存{category}状态的合并数据？(y/n)")
                save_choice = input("请输入选择: ").strip().lower()
                if save_choice == 'y':
                    # 创建保存目录
                    save_dir = Path("D:/ecgeeg/30-数据分析/5-HBA/result/hep_fif_data")
                    save_dir.mkdir(parents=True, exist_ok=True)
                    
                    # 保存数据
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    save_path = save_dir / f"hep_{category}_all_epochs_{timestamp}.npz"
                    np.savez(save_path,
                            data=all_epochs,
                            subjects=all_data[category]['subjects'],
                            conditions=all_data[category]['condition'])
                    print(f"数据已保存至: {save_path}")
                    
                    # 保存被试信息
                    subject_info = pd.DataFrame({
                        'subject': all_data[category]['subjects'],
                        'condition': all_data[category]['condition']
                    })
                    info_path = save_dir / f"hep_{category}_subject_info_{timestamp}.csv"
                    subject_info.to_csv(info_path, index=False)
                    print(f"被试信息已保存至: {info_path}")
            
            else:
                print(f"\n{category.upper()}状态: 无数据")
        
        print("\n分析完成!")
        return all_data

if __name__ == "__main__":
    # 设置数据目录
    data_dir = "D:/ecgeeg/30-数据分析/5-HBA/result/hep_fif_data"
    
    try:
        # 创建数据读取器
        reader = HEPDataReader(data_dir)
        
        # 选择操作模式
        print("\n请选择操作模式:")
        print("1. 读取所有数据并显示摘要")
        print("2. 分析单个文件结构")
        print("3. 分析所有HEP信号")
        
        choice = input("请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            # 读取所有数据
            reader.read_all_data()
            # 打印摘要
            reader.print_summary()
        elif choice == "2":
            # 获取所有fif文件
            all_files = reader.scan_files()
            
            # 列出所有文件供选择
            all_file_list = []
            print("\n可用的文件:")
            file_index = 1
            
            for condition, files in all_files.items():
                for file_path in files:
                    print(f"{file_index}. [{condition}] {file_path.name}")
                    all_file_list.append(file_path)
                    file_index += 1
            
            # 让用户选择文件
            file_choice = input("\n请输入要分析的文件编号: ").strip()
            try:
                file_index = int(file_choice) - 1
                if 0 <= file_index < len(all_file_list):
                    reader.read_single_file(all_file_list[file_index])
                else:
                    print("❌ 无效的文件编号")
            except ValueError:
                print("❌ 请输入有效的数字")
        elif choice == "3":
            # 分析所有HEP信号
            reader.analyze_all_hep_signals()
        else:
            print("❌ 无效的选择")
        
    except Exception as e:
        print(f"\n❌ 发生错误: {str(e)}")
        import traceback
        traceback.print_exc() 