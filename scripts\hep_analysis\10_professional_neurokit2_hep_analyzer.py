#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业NeuroKit2 HEP分析器

完全基于NeuroKit2的专业ECG处理方法：
1. 使用NeuroKit2的标准ECG预处理流程
2. 使用NeuroKit2的专业R波检测算法
3. 使用NeuroKit2的质量评估方法
4. 遵循心电图分析的国际标准

作者: HEP Analysis Team
日期: 2024-12-19
版本: v10 - 专业NeuroKit2版本
"""

import numpy as np
import pandas as pd
import mne
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入NeuroKit2
try:
    import neurokit2 as nk
    NEUROKIT_AVAILABLE = True
    print("✅ NeuroKit2 已导入")
except ImportError:
    NEUROKIT_AVAILABLE = False
    raise ImportError("请先安装NeuroKit2: pip install neurokit2")

class ProfessionalNeuroKit2HEPAnalyzer:
    """基于NeuroKit2专业方法的HEP分析器"""
    
    def __init__(self, data_dir, enable_quality_control=False):
        """
        初始化专业HEP分析器
        
        Parameters:
        -----------
        data_dir : str
            数据目录路径
        enable_quality_control : bool
            是否启用质量控制
        """
        self.data_dir = Path(data_dir)
        self.enable_quality_control = enable_quality_control
        
        # HEP分析参数
        self.hep_params = {
            'time_window': (-0.2, 0.8),  # HEP时间窗口
            'baseline_window': (-0.2, -0.05),  # 基线校正窗口
            'min_heartbeats': 30,  # 最少心跳数
            'rr_interval_range': (0.4, 2.0),  # 有效R-R间隔范围
        }
        
        # NeuroKit2专业参数
        self.nk_params = {
            'sampling_rate': 500,  # 采样率
            'method': 'neurokit',  # 默认使用neurokit方法
            'correct_artifacts': True  # 启用伪迹校正
        }
        
        # 实验条件定义
        self.conditions = {
            'rest_1': '静息态1',
            'rest_2': '静息态2', 
            'rest_3': '静息态3',
            'test_1': '测试态1',
            'test_2': '测试态2',
            'test_3': '测试态3',
            'prac': '练习'
        }
        
        # 文件匹配模式
        self.condition_patterns = {
            'rest_1': '*_01_*_rest_*.fif',
            'rest_2': '*_02_*_rest_*.fif',
            'rest_3': '*_03_*_rest_*.fif',
            'test_1': '*_01_*_test_*.fif',
            'test_2': '*_02_*_test_*.fif',
            'test_3': '*_03_*_test_*.fif',
            'prac': '*_prac_*.fif'
        }
        
        print(f"🔧 专业NeuroKit2 HEP分析器初始化完成")
        print(f"📁 数据目录: {self.data_dir}")
        print(f"🎛️ 质量控制: {'启用' if self.enable_quality_control else '禁用'}")
        print(f"⚙️ 默认检测方法: {self.nk_params['method']}")

    def process_ecg_with_neurokit2(self, ecg_signal, sampling_rate, method='neurokit'):
        """
        使用NeuroKit2专业方法处理ECG信号
        
        Parameters:
        -----------
        ecg_signal : array
            ECG信号（单通道）
        sampling_rate : float
            采样率
        method : str
            检测方法
            
        Returns:
        --------
        signals : DataFrame
            处理后的信号
        info : dict
            处理信息，包含R波位置
        """
        print(f"    🔍 使用NeuroKit2专业方法处理ECG...")
        print(f"    📊 信号长度: {len(ecg_signal)} 样本点")
        print(f"    📊 采样率: {sampling_rate} Hz")
        print(f"    📊 检测方法: {method}")
        
        try:
            # 使用NeuroKit2的完整ECG处理流程
            signals, info = nk.ecg_process(
                ecg_signal,
                sampling_rate=sampling_rate,
                method=method
            )
            
            # 获取R波位置
            r_peaks = info["ECG_R_Peaks"]
            
            print(f"    ✅ 处理完成: 检测到 {len(r_peaks)} 个R波")
            
            # 使用NeuroKit2的质量评估
            quality_metrics = self._assess_ecg_quality_nk2(signals, info, sampling_rate)
            
            return signals, info, quality_metrics
            
        except Exception as e:
            print(f"    ❌ NeuroKit2处理失败: {e}")
            return None, None, None

    def _assess_ecg_quality_nk2(self, signals, info, sampling_rate):
        """
        使用NeuroKit2方法评估ECG质量
        
        Parameters:
        -----------
        signals : DataFrame
            NeuroKit2处理的信号
        info : dict
            NeuroKit2处理信息
        sampling_rate : float
            采样率
            
        Returns:
        --------
        quality_metrics : dict
            质量指标
        """
        r_peaks = info["ECG_R_Peaks"]
        
        if len(r_peaks) < 2:
            return {
                'quality_score': 0.0,
                'heart_rate_mean': 0,
                'heart_rate_std': 0,
                'rr_intervals_mean': 0,
                'rr_intervals_std': 0,
                'quality_level': 'poor'
            }
        
        # 计算心率变异性指标
        rr_intervals = np.diff(r_peaks) / sampling_rate
        heart_rates = 60 / rr_intervals
        
        # 基本统计
        hr_mean = np.mean(heart_rates)
        hr_std = np.std(heart_rates)
        rr_mean = np.mean(rr_intervals)
        rr_std = np.std(rr_intervals)
        
        # 质量评分
        # 1. 心率合理性 (50-120 BPM)
        hr_quality = 1.0 if 50 <= hr_mean <= 120 else 0.5
        
        # 2. 心率变异性合理性 (CV < 0.3)
        hr_cv = hr_std / hr_mean if hr_mean > 0 else 1.0
        hrv_quality = max(0, 1 - hr_cv * 2)
        
        # 3. R波数量充足性
        count_quality = min(1.0, len(r_peaks) / 50)
        
        # 4. R-R间隔分布合理性
        valid_rr_ratio = np.sum((rr_intervals >= 0.4) & (rr_intervals <= 2.0)) / len(rr_intervals)
        
        # 综合质量评分
        quality_score = (hr_quality * 0.3 + hrv_quality * 0.3 + 
                        count_quality * 0.2 + valid_rr_ratio * 0.2)
        
        # 质量等级
        if quality_score >= 0.8:
            quality_level = 'excellent'
        elif quality_score >= 0.6:
            quality_level = 'good'
        elif quality_score >= 0.4:
            quality_level = 'fair'
        else:
            quality_level = 'poor'
        
        quality_metrics = {
            'quality_score': quality_score,
            'heart_rate_mean': hr_mean,
            'heart_rate_std': hr_std,
            'rr_intervals_mean': rr_mean,
            'rr_intervals_std': rr_std,
            'quality_level': quality_level,
            'r_peaks_count': len(r_peaks),
            'heart_rate_cv': hr_cv,
            'valid_rr_ratio': valid_rr_ratio
        }
        
        print(f"    📊 ECG质量评估:")
        print(f"      质量评分: {quality_score:.3f} ({quality_level})")
        print(f"      平均心率: {hr_mean:.1f} ± {hr_std:.1f} BPM")
        print(f"      R-R间隔: {rr_mean:.3f} ± {rr_std:.3f} 秒")
        print(f"      有效R-R比例: {valid_rr_ratio:.3f}")
        
        return quality_metrics

    def extract_hep_epochs_professional(self, eeg_data, r_peaks, sampling_rate, ch_names=None):
        """
        使用专业方法提取HEP时期
        
        Parameters:
        -----------
        eeg_data : array
            EEG数据 (channels x samples)
        r_peaks : array
            R波位置（来自NeuroKit2）
        sampling_rate : float
            采样率
        ch_names : list, optional
            通道名称列表
            
        Returns:
        --------
        hep_epochs : array
            HEP时期数据 (epochs x channels x samples)
        times : array
            时间轴
        ch_names : list
            通道名称列表
        """
        print(f"    🔍 使用专业方法提取HEP epochs...")
        print(f"      EEG数据形状: {eeg_data.shape}")
        print(f"      R波数量: {len(r_peaks)}")
        
        # 计算时间窗口对应的样本点
        pre_samples = int(abs(self.hep_params['time_window'][0]) * sampling_rate)
        post_samples = int(self.hep_params['time_window'][1] * sampling_rate)
        total_samples = pre_samples + post_samples
        
        # 创建时间轴
        times = np.linspace(self.hep_params['time_window'][0],
                           self.hep_params['time_window'][1],
                           total_samples)
        
        # 如果没有提供通道名称，创建默认的通道名称
        if ch_names is None:
            ch_names = [f'EEG{i+1:02d}' for i in range(eeg_data.shape[0])]
        
        # 提取每个心跳周期的EEG数据
        valid_epochs = []
        invalid_count = 0
        
        for r_peak in r_peaks:
            start_idx = r_peak - pre_samples
            end_idx = r_peak + post_samples
            
            # 检查边界条件
            if start_idx < 0 or end_idx >= eeg_data.shape[1]:
                invalid_count += 1
                continue
            
            # 提取epoch
            epoch = eeg_data[:, start_idx:end_idx]
            
            # 检查epoch长度
            if epoch.shape[1] != total_samples:
                invalid_count += 1
                continue
            
            # 基线校正
            baseline_start_time = self.hep_params['baseline_window'][0] - self.hep_params['time_window'][0]
            baseline_end_time = self.hep_params['baseline_window'][1] - self.hep_params['time_window'][0]
            
            baseline_start = int(baseline_start_time * sampling_rate)
            baseline_end = int(baseline_end_time * sampling_rate)
            
            # 确保基线窗口有效
            if (baseline_start >= 0 and baseline_end <= epoch.shape[1] and 
                baseline_start < baseline_end):
                baseline_mean = np.mean(epoch[:, baseline_start:baseline_end], axis=1, keepdims=True)
                epoch_corrected = epoch - baseline_mean
                valid_epochs.append(epoch_corrected)
            else:
                invalid_count += 1
        
        print(f"      ✅ 成功提取: {len(valid_epochs)}个有效epochs")
        print(f"      ⚠️ 边界无效: {invalid_count}个epochs")
        
        if len(valid_epochs) == 0:
            raise ValueError("未提取到任何有效的HEP epochs")
        
        hep_epochs = np.array(valid_epochs)
        print(f"      📊 最终HEP数据形状: {hep_epochs.shape}")
        
        return hep_epochs, times, ch_names

    def analyze_single_file_professional(self, file_info):
        """
        使用专业NeuroKit2方法分析单个文件
        
        Parameters:
        -----------
        file_info : dict
            文件信息字典
            
        Returns:
        --------
        result : dict
            分析结果
        """
        print(f"    📁 文件: {file_info['file_name']}")
        
        # 读取数据
        raw = mne.io.read_raw_fif(file_info['file_path'], preload=True, verbose=False)
        
        # 获取基本信息
        sampling_rate = raw.info['sfreq']
        ch_names = raw.ch_names
        data = raw.get_data() * 1000  # mV -> μV
        
        print(f"    📊 数据形状: {data.shape}, 采样率: {sampling_rate} Hz")
        
        # 分离EEG和ECG数据
        eeg_data = data[:61, :]  # 前61个通道是EEG
        ecg_data = data[61:, :]  # 后58个通道是ECG
        
        # 使用ECG11通道（标准导联）
        ecg11_signal = ecg_data[10, :]  # ECG11通道（索引10）
        print(f"    🎯 使用ECG11通道（标准导联）")
        
        # 使用NeuroKit2专业方法处理ECG
        signals, info, quality_metrics = self.process_ecg_with_neurokit2(
            ecg11_signal, sampling_rate, method=self.nk_params['method']
        )
        
        if signals is None or info is None:
            return {
                'success': False,
                'error': "NeuroKit2 ECG处理失败",
                'quality_metrics': None
            }
        
        # 获取R波位置
        r_peaks = info["ECG_R_Peaks"]
        
        if len(r_peaks) < self.hep_params['min_heartbeats']:
            return {
                'success': False,
                'error': f"R波数量不足: {len(r_peaks)} < {self.hep_params['min_heartbeats']}",
                'r_peaks_count': len(r_peaks),
                'quality_metrics': quality_metrics
            }
        
        # 提取HEP时期
        hep_epochs, times, ch_names_used = self.extract_hep_epochs_professional(
            eeg_data, r_peaks, sampling_rate, ch_names[:61]
        )
        
        return {
            'success': True,
            'hep_epochs': hep_epochs,
            'times': times,
            'channel_names': ch_names_used,
            'file_info': file_info,
            'r_peaks_count': len(r_peaks),
            'r_peaks': r_peaks,
            'quality_metrics': quality_metrics,
            'neurokit_signals': signals,
            'neurokit_info': info,
            'ecg_signal': ecg11_signal,
            'sampling_rate': sampling_rate
        }

if __name__ == "__main__":
    # 使用示例
    data_dir = "D:/ecgeeg/18-eegecg手动预处理5-重参考2-双侧乳突"
    
    # 创建专业NeuroKit2分析器
    analyzer = ProfessionalNeuroKit2HEPAnalyzer(data_dir, enable_quality_control=False)
    
    print("🚀 专业NeuroKit2 HEP分析器准备就绪")
    print("🎯 特点:")
    print("   ✅ 完全基于NeuroKit2专业方法")
    print("   ✅ 标准ECG导联处理")
    print("   ✅ 国际标准心电图分析")
    print("   ✅ 专业质量评估")
