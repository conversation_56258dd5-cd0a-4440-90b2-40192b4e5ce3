#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标准化HEP分析器 - 基于2024年最新文献标准
===========================================

基于Virjee等人(2024)的系统性综述，使用文献推荐的模态值参数
解决当前HEP分析中97%研究缺失最小RR间隔等关键问题

参考文献：
- <PERSON><PERSON><PERSON> et al. (2024). Methodological approaches to derive the heartbeat evoked potential
- <PERSON><PERSON><PERSON> et al. (2024). Respiratory influences on cardiac interoception
"""

import os
import sys
import mne
import numpy as np
import pandas as pd
from pathlib import Path
import matplotlib.pyplot as plt
from scipy import signal
from sklearn.decomposition import FastICA
import warnings
warnings.filterwarnings('ignore')

class StandardizedHEPAnalyzer:
    """
    标准化HEP分析器
    
    基于最新文献的标准方法：
    - 使用文献模态值参数
    - 实施关键的最小RR间隔控制
    - 标准化心电场伪迹去除
    - 全面质量控制
    """
    
    def __init__(self):
        self.setup_paths()
        self.setup_literature_params()
        
    def setup_paths(self):
        """设置文件路径"""
        self.base_path = Path("D:/ecgeeg")
        self.data_path = self.base_path / "18-eegecg手动预处理5-重参考2-双侧乳突"
        self.output_path = self.base_path / "30-数据分析/5-HBA/result/standardized_hep_analysis"
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        print(f"📁 数据路径: {self.data_path}")
        print(f"📁 输出路径: {self.output_path}")
    
    def setup_literature_params(self):
        """
        设置基于文献的标准参数
        来源：Virjee et al. (2024) 系统性综述的模态值

        基于97项研究的系统性综述结果：
        - 滤波：0.1-30Hz (最常用)
        - Epoch窗口：-200ms到600ms (模态值)
        - HEP窗口：200-600ms (模态值)
        - 最小RR间隔：600ms (关键但97%研究缺失)
        - 基线校正：-200ms到0ms (模态值)
        """
        self.params = {
            # === 滤波参数 (基于文献模态值) ===
            'highpass': 0.1,      # 26/97项研究使用0.1Hz高通
            'lowpass': 30.0,      # 26/97项研究使用30Hz低通

            # === Epoch参数 (基于文献模态值) ===
            'epoch_tmin': -0.2,   # 52/97项研究使用-200ms
            'epoch_tmax': 0.6,    # 49/97项研究使用600ms

            # === 关键参数：最小RR间隔 (97%研究缺失!) ===
            'min_rr_interval': 0.6,  # 600ms，基于生理学合理范围
            'max_rr_interval': 1.5,  # 1500ms，排除过慢心率

            # === 振幅拒绝阈值 ===
            'amplitude_threshold': 100e-6,  # 100μV，平衡信号保留和伪迹去除

            # === HEP分析窗口 (基于文献模态值) ===
            'hep_tmin': 0.2,      # 33/97项研究使用200ms
            'hep_tmax': 0.6,      # 34/97项研究使用600ms

            # === 基线校正 (基于文献模态值) ===
            'baseline_tmin': -0.2,  # 26/97项研究使用-200ms
            'baseline_tmax': 0.0,   # 44/97项研究使用0ms

            # === 采样率和质量控制 ===
            'sfreq': 500,
            'min_epochs': 30,     # 最少30个epochs保证统计功效
            'max_epochs': 300,    # 最多300个epochs避免过度平均

            # === R波检测参数 ===
            'r_detection_method': 'adaptive',  # 自适应检测
            'cardiac_freq_range': [0.8, 4.0],  # 心率频段 (48-240 bpm)

            # === 心电场伪迹去除参数 ===
            'cfa_method': 'ica',              # ICA方法
            'ica_components': 25,              # ICA成分数
            'cardiac_component_threshold': 0.3, # 心电成分识别阈值
        }
        
        print("📊 使用文献标准参数:")
        for key, value in self.params.items():
            print(f"   {key}: {value}")
    
    def load_and_preprocess_eeg(self, file_path):
        """
        加载并预处理EEG数据
        按照文献标准进行滤波
        """
        try:
            # 加载数据
            raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
            
            # 应用文献标准滤波 (0.1-30Hz)
            raw_filtered = raw.copy()
            raw_filtered.filter(
                l_freq=self.params['highpass'], 
                h_freq=self.params['lowpass'],
                fir_design='firwin',
                verbose=False
            )
            
            return raw_filtered
            
        except Exception as e:
            print(f"❌ 加载文件失败 {file_path}: {e}")
            return None
    
    def detect_r_peaks_standardized(self, raw):
        """
        标准化R波检测方法
        基于心电敏感电极的自动检测
        """
        print("    🫀 执行标准化R波检测...")
        
        # 心电敏感电极（按优先级排序）
        cardiac_channels = [
            'Fp1', 'Fp2', 'F7', 'F8', 'FT9', 'FT10',
            'T7', 'T8', 'Fz', 'Cz', 'FC1', 'FC2'
        ]
        
        available_channels = [ch for ch in cardiac_channels if ch in raw.ch_names]
        
        if not available_channels:
            print("❌ 未找到心电敏感电极")
            return None, None, 0
        
        best_r_peaks = None
        best_channel = None
        best_quality = 0
        
        for channel in available_channels[:3]:  # 测试前3个可用电极
            try:
                # 获取单通道数据
                ch_idx = raw.ch_names.index(channel)
                data = raw.get_data()[ch_idx, :]
                
                # 心跳检测专用滤波 (1-15Hz突出QRS)
                sos = signal.butter(4, [1, 15], btype='band', fs=self.params['sfreq'], output='sos')
                filtered_data = signal.sosfilt(sos, data)
                
                # R波检测
                r_peaks = self._detect_r_peaks_adaptive(filtered_data, self.params['sfreq'])
                
                if len(r_peaks) == 0:
                    continue
                
                # 质量评估
                quality = self._assess_r_peak_quality(r_peaks, self.params['sfreq'])
                
                if quality > best_quality:
                    best_quality = quality
                    best_r_peaks = r_peaks
                    best_channel = channel
                    
            except Exception as e:
                print(f"    ⚠️ 电极 {channel} 检测失败: {e}")
                continue
        
        if best_r_peaks is not None:
            print(f"    ✅ 最佳检测电极: {best_channel}")
            print(f"    ✅ 检测到 {len(best_r_peaks)} 个R波")
            print(f"    ✅ 质量评分: {best_quality:.1f}")
        
        return best_r_peaks, best_channel, best_quality
    
    def _detect_r_peaks_adaptive(self, signal_data, sfreq):
        """自适应R波检测算法"""
        # 计算信号的绝对值（支持正负向R波）
        abs_signal = np.abs(signal_data)
        
        # 自适应阈值
        threshold = np.std(abs_signal) * 3
        
        # 查找峰值
        peaks, _ = signal.find_peaks(
            abs_signal,
            height=threshold,
            distance=int(0.4 * sfreq),  # 最小间隔400ms
            prominence=threshold * 0.5
        )
        
        return peaks
    
    def _assess_r_peak_quality(self, r_peaks, sfreq):
        """评估R波检测质量"""
        if len(r_peaks) < 10:
            return 0
        
        # 计算RR间隔
        rr_intervals = np.diff(r_peaks) / sfreq
        
        # 心率评估
        heart_rate = 60 / np.mean(rr_intervals)
        hr_score = 100 if 50 <= heart_rate <= 120 else 50
        
        # RR间隔变异性
        rr_std = np.std(rr_intervals)
        variability_score = max(0, 100 - rr_std * 1000)
        
        # 综合质量评分
        quality = (hr_score + variability_score) / 2
        
        return quality
    
    def apply_rr_interval_filtering(self, r_peaks, sfreq):
        """
        应用RR间隔过滤 - 这是97%研究缺失的关键步骤！

        基于Virjee et al. (2024)的建议：
        1. 最小RR间隔：600ms (避免心律不齐)
        2. 最大RR间隔：1500ms (避免心动过缓)
        3. RR间隔变异性检查
        """
        if len(r_peaks) < 2:
            return r_peaks

        min_samples = int(self.params['min_rr_interval'] * sfreq)
        max_samples = int(self.params['max_rr_interval'] * sfreq)

        # 计算所有RR间隔
        rr_intervals = np.diff(r_peaks)

        # 第一步：基于最小/最大RR间隔过滤
        valid_indices = []
        valid_indices.append(0)  # 保留第一个R波

        for i in range(1, len(r_peaks)):
            rr_interval = r_peaks[i] - r_peaks[i-1]
            if min_samples <= rr_interval <= max_samples:
                valid_indices.append(i)

        filtered_peaks = r_peaks[valid_indices]

        # 第二步：RR间隔变异性检查 (去除异常值)
        if len(filtered_peaks) > 10:
            rr_intervals_filtered = np.diff(filtered_peaks)
            rr_mean = np.mean(rr_intervals_filtered)
            rr_std = np.std(rr_intervals_filtered)

            # 使用3σ准则去除异常RR间隔
            outlier_threshold = 3 * rr_std

            final_indices = [0]  # 保留第一个
            for i in range(1, len(filtered_peaks)):
                rr_interval = filtered_peaks[i] - filtered_peaks[i-1]
                if abs(rr_interval - rr_mean) <= outlier_threshold:
                    final_indices.append(i)

            final_peaks = filtered_peaks[final_indices]
        else:
            final_peaks = filtered_peaks

        # 统计信息
        removed_min_max = len(r_peaks) - len(filtered_peaks)
        removed_outliers = len(filtered_peaks) - len(final_peaks)
        total_removed = len(r_peaks) - len(final_peaks)

        if total_removed > 0:
            print(f"    🔧 RR间隔过滤:")
            print(f"       - 最小/最大RR过滤: 移除 {removed_min_max} 个R波")
            print(f"       - 异常值过滤: 移除 {removed_outliers} 个R波")
            print(f"       - 总计移除: {total_removed} 个R波 ({total_removed/len(r_peaks)*100:.1f}%)")

            # 计算最终的心率统计
            if len(final_peaks) > 1:
                final_rr = np.diff(final_peaks) / sfreq
                mean_hr = 60 / np.mean(final_rr)
                print(f"       - 过滤后平均心率: {mean_hr:.1f} bpm")

        return final_peaks

    def classify_files(self, files):
        """
        根据文件名对文件进行分类

        Parameters:
        -----------
        files : list
            文件路径列表

        Returns:
        --------
        conditions : dict
            按条件分类的文件字典
        """
        conditions = {}

        for file_path in files:
            file_name = file_path.name if hasattr(file_path, 'name') else str(file_path)

            # 根据文件名识别条件
            if 'rest' in file_name.lower():
                condition = 'rest'
            elif 'test' in file_name.lower():
                condition = 'test'
            elif 'prac' in file_name.lower():
                condition = 'practice'
            else:
                condition = 'unknown'

            if condition not in conditions:
                conditions[condition] = []
            conditions[condition].append(file_path)

        return conditions


    
    def remove_cardiac_field_artifact(self, raw, method='ica'):
        """
        移除心电场伪迹
        支持ICA和ASR方法
        """
        print(f"    🧹 执行心电场伪迹去除 ({method})...")
        
        if method == 'ica':
            return self._remove_cfa_with_ica(raw)
        elif method == 'asr':
            return self._remove_cfa_with_asr(raw)
        else:
            print("❌ 未知的伪迹去除方法")
            return raw
    
    def _remove_cfa_with_ica(self, raw):
        """使用ICA移除心电场伪迹"""
        try:
            # 创建ICA对象
            ica = mne.preprocessing.ICA(
                n_components=min(25, len(raw.ch_names)),
                method='fastica',
                random_state=42,
                verbose=False
            )
            
            # 拟合ICA
            ica.fit(raw, verbose=False)
            
            # 自动识别心电成分
            cardiac_components = self._identify_cardiac_components(ica, raw)
            
            if cardiac_components:
                print(f"    🎯 识别心电成分: {cardiac_components}")
                ica.exclude = cardiac_components
                raw_clean = ica.apply(raw.copy(), verbose=False)
                return raw_clean
            else:
                print("    ⚠️ 未识别到明显心电成分")
                return raw
                
        except Exception as e:
            print(f"    ❌ ICA处理失败: {e}")
            return raw
    
    def _identify_cardiac_components(self, ica, raw):
        """
        自动识别心电成分
        基于多重标准的心电成分识别算法
        """
        cardiac_components = []
        component_scores = []

        # 获取ICA源信号
        sources = ica.get_sources(raw)

        # 分析每个ICA成分
        for comp_idx in range(ica.n_components_):
            component_ts = sources.get_data()[comp_idx, :]
            score = 0

            # 标准1：心跳频段功率比例 (0.8-4Hz)
            freqs, psd = signal.welch(component_ts, fs=raw.info['sfreq'], nperseg=1024)
            cardiac_band = (freqs >= self.params['cardiac_freq_range'][0]) & \
                          (freqs <= self.params['cardiac_freq_range'][1])
            cardiac_power_ratio = np.sum(psd[cardiac_band]) / np.sum(psd)

            if cardiac_power_ratio > self.params['cardiac_component_threshold']:
                score += cardiac_power_ratio * 100

            # 标准2：峰值检测 - 是否有规律的心跳样峰值
            try:
                # 检测成分中的峰值
                peaks, _ = signal.find_peaks(
                    np.abs(component_ts),
                    distance=int(0.4 * raw.info['sfreq']),  # 最小间隔400ms
                    height=np.std(component_ts) * 2
                )

                if len(peaks) > 10:
                    # 计算峰值间隔的规律性
                    peak_intervals = np.diff(peaks) / raw.info['sfreq']
                    interval_cv = np.std(peak_intervals) / np.mean(peak_intervals)

                    # 心跳间隔应该相对规律 (CV < 0.5)
                    if interval_cv < 0.5:
                        score += (0.5 - interval_cv) * 100

                        # 检查平均心率是否在合理范围
                        avg_hr = 60 / np.mean(peak_intervals)
                        if 40 <= avg_hr <= 150:  # 合理心率范围
                            score += 20

            except Exception:
                pass

            # 标准3：地形图模式 - 心电成分通常在前额和颞部电极最强
            try:
                # 获取成分的地形图
                component_topo = ica.mixing_[:, comp_idx]

                # 查找前额电极 (Fp1, Fp2, F7, F8)
                frontal_channels = ['Fp1', 'Fp2', 'F7', 'F8', 'FT9', 'FT10']
                frontal_indices = [i for i, ch in enumerate(raw.ch_names)
                                 if ch in frontal_channels]

                if frontal_indices:
                    frontal_weights = np.abs(component_topo[frontal_indices])
                    max_frontal = np.max(frontal_weights)
                    mean_all = np.mean(np.abs(component_topo))

                    # 如果前额电极权重明显高于平均值
                    if max_frontal > mean_all * 1.5:
                        score += 30

            except Exception:
                pass

            component_scores.append((comp_idx, score))

        # 按分数排序，选择得分最高的成分
        component_scores.sort(key=lambda x: x[1], reverse=True)

        # 选择得分超过阈值的成分作为心电成分
        score_threshold = 50  # 最低分数阈值
        for comp_idx, score in component_scores:
            if score > score_threshold and len(cardiac_components) < 3:  # 最多3个成分
                cardiac_components.append(comp_idx)
                print(f"       - 心电成分 {comp_idx}: 得分 {score:.1f}")

        return cardiac_components
    
    def _remove_cfa_with_asr(self, raw):
        """使用ASR移除心电场伪迹"""
        # 这里简化处理，实际需要ASR库
        print("    📝 ASR方法需要额外库，暂时跳过")
        return raw
    
    def create_epochs_standardized(self, raw, r_peaks):
        """
        创建标准化的epochs
        使用文献推荐的参数
        """
        print("    📊 创建标准化epochs...")
        
        # 将R波位置转换为事件
        events = np.column_stack([
            r_peaks,
            np.zeros(len(r_peaks), dtype=int),
            np.ones(len(r_peaks), dtype=int)
        ])
        
        # 创建epochs
        epochs = mne.Epochs(
            raw,
            events,
            event_id={'heartbeat': 1},
            tmin=self.params['epoch_tmin'],
            tmax=self.params['epoch_tmax'],
            baseline=(self.params['baseline_tmin'], self.params['baseline_tmax']),
            reject={'eeg': self.params['amplitude_threshold']},
            preload=True,
            verbose=False
        )
        
        print(f"    ✅ 创建 {len(epochs)} 个epochs")
        print(f"    ✅ 振幅拒绝阈值: {self.params['amplitude_threshold']*1e6:.0f}μV")
        
        return epochs
    
    def compute_hep(self, epochs):
        """
        计算HEP
        使用文献标准的分析窗口
        """
        # 平均得到HEP
        hep = epochs.average()
        
        # 裁剪到HEP分析窗口
        hep_windowed = hep.copy().crop(
            tmin=self.params['hep_tmin'],
            tmax=self.params['hep_tmax']
        )
        
        return hep, hep_windowed
    
    def assess_hep_quality(self, epochs, hep):
        """
        全面的HEP质量评估
        基于多个维度评估HEP信号质量
        """
        quality_metrics = {
            # 基本统计
            'n_epochs': len(epochs),
            'n_channels': len(hep.ch_names),

            # 信号质量指标
            'snr': self._calculate_snr(hep),
            'peak_amplitude': self._find_peak_amplitude(hep),
            'peak_latency': self._find_peak_latency(hep),

            # 一致性指标
            'trial_consistency': self._assess_trial_consistency(epochs),
            'channel_consistency': self._assess_channel_consistency(hep),

            # 统计显著性
            'statistical_significance': self._test_statistical_significance(epochs),

            # 整体质量评分
            'overall_quality': 0  # 将在最后计算
        }

        # 计算整体质量评分 (0-100)
        quality_metrics['overall_quality'] = self._calculate_overall_quality(quality_metrics)

        return quality_metrics
    
    def _calculate_snr(self, hep):
        """计算信噪比"""
        # 信号期（200-600ms）
        signal_data = hep.copy().crop(0.2, 0.6).data
        signal_power = np.mean(signal_data ** 2)
        
        # 噪声期（baseline）
        noise_data = hep.copy().crop(-0.2, 0).data
        noise_power = np.mean(noise_data ** 2)
        
        if noise_power > 0:
            snr = 10 * np.log10(signal_power / noise_power)
        else:
            snr = 0
            
        return snr
    
    def _find_peak_amplitude(self, hep):
        """查找峰值振幅"""
        # 在HEP窗口内查找最大振幅
        hep_windowed = hep.copy().crop(0.2, 0.6)
        peak_amp = np.max(np.abs(hep_windowed.data))
        return peak_amp
    
    def _find_peak_latency(self, hep):
        """查找HEP峰值潜伏期"""
        # 在HEP窗口内查找峰值潜伏期
        hep_windowed = hep.copy().crop(self.params['hep_tmin'], self.params['hep_tmax'])

        # 计算全脑平均
        global_avg = np.mean(hep_windowed.data, axis=0)

        # 查找绝对值最大的点
        peak_idx = np.argmax(np.abs(global_avg))
        peak_latency = hep_windowed.times[peak_idx]

        return peak_latency

    def _assess_trial_consistency(self, epochs):
        """评估试次间一致性"""
        if len(epochs) < 10:
            return 0

        # 计算每个试次在HEP窗口的平均幅值
        hep_window_data = epochs.copy().crop(
            self.params['hep_tmin'],
            self.params['hep_tmax']
        ).get_data()

        # 计算每个试次的全脑平均HEP幅值
        trial_hep_values = np.mean(hep_window_data, axis=(1, 2))

        # 计算变异系数 (CV = std/mean)
        if np.mean(trial_hep_values) != 0:
            cv = np.std(trial_hep_values) / np.abs(np.mean(trial_hep_values))
            consistency = max(0, 1 - cv)  # CV越小，一致性越高
        else:
            consistency = 0

        return consistency

    def _assess_channel_consistency(self, hep):
        """评估通道间一致性"""
        # 在HEP窗口内计算每个通道的平均幅值
        hep_windowed = hep.copy().crop(self.params['hep_tmin'], self.params['hep_tmax'])
        channel_hep_values = np.mean(hep_windowed.data, axis=1)

        # 计算通道间相关性
        if len(channel_hep_values) > 1:
            # 计算所有通道对的相关系数
            correlations = []
            for i in range(len(hep_windowed.data)):
                for j in range(i+1, len(hep_windowed.data)):
                    corr = np.corrcoef(hep_windowed.data[i], hep_windowed.data[j])[0, 1]
                    if not np.isnan(corr):
                        correlations.append(abs(corr))

            if correlations:
                consistency = np.mean(correlations)
            else:
                consistency = 0
        else:
            consistency = 0

        return consistency

    def _test_statistical_significance(self, epochs):
        """测试HEP的统计显著性"""
        if len(epochs) < 10:
            return 0

        # 获取HEP窗口和基线窗口的数据
        hep_data = epochs.copy().crop(
            self.params['hep_tmin'],
            self.params['hep_tmax']
        ).get_data()

        baseline_data = epochs.copy().crop(
            self.params['baseline_tmin'],
            self.params['baseline_tmax']
        ).get_data()

        # 计算每个试次的HEP和基线平均值
        hep_values = np.mean(hep_data, axis=(1, 2))
        baseline_values = np.mean(baseline_data, axis=(1, 2))

        # 进行配对t检验
        from scipy import stats
        try:
            t_stat, p_value = stats.ttest_rel(hep_values, baseline_values)
            significance = 1 - p_value if p_value < 0.05 else 0
        except:
            significance = 0

        return significance

    def _calculate_overall_quality(self, metrics):
        """计算整体质量评分"""
        score = 0

        # Epochs数量评分 (0-25分)
        if metrics['n_epochs'] >= 100:
            score += 25
        elif metrics['n_epochs'] >= 50:
            score += 20
        elif metrics['n_epochs'] >= 30:
            score += 15
        elif metrics['n_epochs'] >= 20:
            score += 10

        # SNR评分 (0-25分)
        snr = metrics['snr']
        if snr >= 10:
            score += 25
        elif snr >= 5:
            score += 20
        elif snr >= 2:
            score += 15
        elif snr >= 0:
            score += 10

        # 一致性评分 (0-25分)
        consistency = (metrics['trial_consistency'] + metrics['channel_consistency']) / 2
        score += consistency * 25

        # 统计显著性评分 (0-25分)
        score += metrics['statistical_significance'] * 25

        return min(100, score)
    
    def process_single_file(self, file_path, condition):
        """处理单个文件"""
        print(f"\n🔬 处理文件: {file_path.name}")
        print(f"📋 条件: {condition}")
        
        try:
            # 1. 加载和预处理
            raw = self.load_and_preprocess_eeg(file_path)
            if raw is None:
                return None
            
            # 2. R波检测
            r_peaks, best_channel, quality = self.detect_r_peaks_standardized(raw)
            if r_peaks is None or len(r_peaks) < 10:
                print(f"❌ R波检测失败")
                return None
            
            # 3. 应用RR间隔过滤（关键步骤！）
            r_peaks_filtered = self.apply_rr_interval_filtering(r_peaks, raw.info['sfreq'])
            
            # 4. 移除心电场伪迹
            raw_clean = self.remove_cardiac_field_artifact(raw, method='ica')
            
            # 5. 创建epochs
            epochs = self.create_epochs_standardized(raw_clean, r_peaks_filtered)
            
            if len(epochs) < self.params['min_epochs']:
                print(f"❌ epochs数量不足: {len(epochs)} < {self.params['min_epochs']}")
                return None
            
            # 6. 计算HEP
            hep, hep_windowed = self.compute_hep(epochs)
            
            # 7. 质量评估
            quality_metrics = self.assess_hep_quality(epochs, hep)
            
            print(f"✅ 处理完成:")
            print(f"   - 检测电极: {best_channel}")
            print(f"   - R波数量: {len(r_peaks)} → {len(r_peaks_filtered)}")
            print(f"   - Epochs: {len(epochs)}")
            print(f"   - SNR: {quality_metrics['snr']:.1f} dB")
            
            return {
                'hep': hep,
                'hep_windowed': hep_windowed,
                'epochs': epochs,
                'quality_metrics': quality_metrics,
                'detection_info': {
                    'best_channel': best_channel,
                    'r_peaks_original': len(r_peaks),
                    'r_peaks_filtered': len(r_peaks_filtered),
                    'detection_quality': quality
                }
            }
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return None
    
    def run_analysis(self, conditions=None):
        """运行完整的标准化分析"""
        print("🚀 开始标准化HEP分析")
        print("=" * 60)

        # 如果没有提供条件，则自动查找文件
        if conditions is None:
            # 条件映射 - 修正文件匹配模式
            condition_patterns = {
            'prac': '*prac*',
            'rest': '*rest*',
            'test': '*test*'
        }
        
            results = {}
            summary_stats = []

            for condition, pattern in condition_patterns.items():
                print(f"\n📂 处理条件: {condition}")
                print("-" * 40)

                condition_files = list(self.data_path.glob(pattern))
                print(f"找到 {len(condition_files)} 个文件")

                condition_results = []

                for file_path in condition_files[:10]:  # 处理10个文件进行测试
                    result = self.process_single_file(file_path, condition)
                    if result is not None:
                        condition_results.append(result)

                if condition_results:
                    results[condition] = condition_results

                    # 统计信息
                    n_files = len(condition_results)
                    avg_epochs = np.mean([r['quality_metrics']['n_epochs'] for r in condition_results])
                    avg_snr = np.mean([r['quality_metrics']['snr'] for r in condition_results])

                    summary_stats.append({
                        'condition': condition,
                        'n_files': n_files,
                        'avg_epochs': avg_epochs,
                        'avg_snr': avg_snr
                    })

                    print(f"📊 {condition} 汇总:")
                    print(f"   - 成功文件: {n_files}")
                    print(f"   - 平均epochs: {avg_epochs:.0f}")
                    print(f"   - 平均SNR: {avg_snr:.1f} dB")
        else:
            # 使用传入的条件
            results = {}
            summary_stats = []

            for condition, file_list in conditions.items():
                print(f"\n📂 处理条件: {condition}")
                print("-" * 40)
                print(f"处理 {len(file_list)} 个文件")

                condition_results = []

                for file_path in file_list:
                    result = self.process_single_file(file_path, condition)
                    if result is not None:
                        condition_results.append(result)

                if condition_results:
                    results[condition] = condition_results

                    # 统计信息
                    n_files = len(condition_results)
                    avg_epochs = np.mean([r['quality_metrics']['n_epochs'] for r in condition_results])
                    avg_snr = np.mean([r['quality_metrics']['snr'] for r in condition_results])

                    summary_stats.append({
                        'condition': condition,
                        'n_files': n_files,
                        'avg_epochs': avg_epochs,
                        'avg_snr': avg_snr
                    })

                    print(f"📊 {condition} 汇总:")
                    print(f"   - 成功文件: {n_files}")
                    print(f"   - 平均epochs: {avg_epochs:.0f}")
                    print(f"   - 平均SNR: {avg_snr:.1f} dB")
        
        # 保存结果
        self.save_results(results, summary_stats)
        
        return results, summary_stats
    
    def save_results(self, results, summary_stats):
        """保存分析结果"""
        print(f"\n💾 保存结果到: {self.output_path}")
        
        # 保存每个条件的HEP数据
        for condition, condition_results in results.items():
            condition_dir = self.output_path / condition
            condition_dir.mkdir(exist_ok=True)
            
            for i, result in enumerate(condition_results):
                # 保存HEP
                hep_file = condition_dir / f"{condition}_hep_{i:03d}.fif"
                result['hep'].save(hep_file, overwrite=True, verbose=False)
                
                # 保存epochs
                epochs_file = condition_dir / f"{condition}_epochs_{i:03d}.fif"
                result['epochs'].save(epochs_file, overwrite=True, verbose=False)
        
        # 保存汇总统计
        summary_df = pd.DataFrame(summary_stats)
        summary_file = self.output_path / "standardized_hep_summary.csv"
        summary_df.to_csv(summary_file, index=False)
        
        print("✅ 结果保存完成")

        # 生成质量报告
        self.generate_quality_report(results, summary_stats)

    def generate_quality_report(self, results, summary_stats):
        """生成HEP质量分析报告"""
        print(f"\n📊 生成质量分析报告...")

        # 创建质量报告
        quality_data = []

        for condition, condition_results in results.items():
            for i, result in enumerate(condition_results):
                quality_metrics = result['quality_metrics']
                detection_info = result['detection_info']

                quality_data.append({
                    'condition': condition,
                    'file_index': i,
                    'n_epochs': quality_metrics['n_epochs'],
                    'snr_db': quality_metrics['snr'],
                    'peak_amplitude_uv': quality_metrics['peak_amplitude'] * 1e6,
                    'peak_latency_ms': quality_metrics['peak_latency'] * 1000,
                    'trial_consistency': quality_metrics['trial_consistency'],
                    'channel_consistency': quality_metrics['channel_consistency'],
                    'statistical_significance': quality_metrics['statistical_significance'],
                    'overall_quality': quality_metrics['overall_quality'],
                    'detection_channel': detection_info['best_channel'],
                    'r_peaks_original': detection_info['r_peaks_original'],
                    'r_peaks_filtered': detection_info['r_peaks_filtered'],
                    'detection_quality': detection_info['detection_quality']
                })

        # 保存详细质量数据
        quality_df = pd.DataFrame(quality_data)
        quality_file = self.output_path / "detailed_quality_metrics.csv"
        quality_df.to_csv(quality_file, index=False)

        # 生成质量统计摘要
        quality_summary = quality_df.groupby('condition').agg({
            'n_epochs': ['mean', 'std', 'min', 'max'],
            'snr_db': ['mean', 'std'],
            'overall_quality': ['mean', 'std'],
            'trial_consistency': ['mean', 'std'],
            'statistical_significance': ['mean', 'sum']
        }).round(2)

        quality_summary_file = self.output_path / "quality_summary_by_condition.csv"
        quality_summary.to_csv(quality_summary_file)

        # 创建可视化图表
        self.create_quality_visualizations(quality_df)

        print(f"📈 质量报告保存至: {self.output_path}")
        print(f"   - 详细指标: detailed_quality_metrics.csv")
        print(f"   - 条件摘要: quality_summary_by_condition.csv")
        print(f"   - 可视化图表: quality_plots.png")

    def create_quality_visualizations(self, quality_df):
        """创建质量分析可视化图表"""
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle('HEP质量分析报告', fontsize=16, fontweight='bold')

        # 1. Epochs数量分布
        ax1 = axes[0, 0]
        for condition in quality_df['condition'].unique():
            condition_data = quality_df[quality_df['condition'] == condition]
            ax1.hist(condition_data['n_epochs'], alpha=0.7, label=condition, bins=10)
        ax1.set_xlabel('Epochs数量')
        ax1.set_ylabel('文件数量')
        ax1.set_title('Epochs数量分布')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. SNR分布
        ax2 = axes[0, 1]
        for condition in quality_df['condition'].unique():
            condition_data = quality_df[quality_df['condition'] == condition]
            ax2.hist(condition_data['snr_db'], alpha=0.7, label=condition, bins=10)
        ax2.set_xlabel('SNR (dB)')
        ax2.set_ylabel('文件数量')
        ax2.set_title('信噪比分布')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 整体质量评分
        ax3 = axes[0, 2]
        conditions = quality_df['condition'].unique()
        quality_means = [quality_df[quality_df['condition'] == c]['overall_quality'].mean()
                        for c in conditions]
        quality_stds = [quality_df[quality_df['condition'] == c]['overall_quality'].std()
                       for c in conditions]

        bars = ax3.bar(conditions, quality_means, yerr=quality_stds, capsize=5, alpha=0.7)
        ax3.set_ylabel('整体质量评分')
        ax3.set_title('各条件整体质量')
        ax3.set_ylim(0, 100)
        ax3.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, mean in zip(bars, quality_means):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{mean:.1f}', ha='center', va='bottom')

        # 4. 试次一致性 vs 通道一致性
        ax4 = axes[1, 0]
        for condition in quality_df['condition'].unique():
            condition_data = quality_df[quality_df['condition'] == condition]
            ax4.scatter(condition_data['trial_consistency'],
                       condition_data['channel_consistency'],
                       alpha=0.7, label=condition, s=50)
        ax4.set_xlabel('试次一致性')
        ax4.set_ylabel('通道一致性')
        ax4.set_title('一致性分析')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        # 5. HEP峰值潜伏期分布
        ax5 = axes[1, 1]
        for condition in quality_df['condition'].unique():
            condition_data = quality_df[quality_df['condition'] == condition]
            ax5.hist(condition_data['peak_latency_ms'], alpha=0.7, label=condition, bins=10)
        ax5.set_xlabel('峰值潜伏期 (ms)')
        ax5.set_ylabel('文件数量')
        ax5.set_title('HEP峰值潜伏期')
        ax5.legend()
        ax5.grid(True, alpha=0.3)

        # 6. 统计显著性
        ax6 = axes[1, 2]
        sig_counts = []
        for condition in conditions:
            condition_data = quality_df[quality_df['condition'] == condition]
            sig_count = (condition_data['statistical_significance'] > 0).sum()
            total_count = len(condition_data)
            sig_percentage = (sig_count / total_count) * 100 if total_count > 0 else 0
            sig_counts.append(sig_percentage)

        bars = ax6.bar(conditions, sig_counts, alpha=0.7)
        ax6.set_ylabel('统计显著性比例 (%)')
        ax6.set_title('各条件统计显著性')
        ax6.set_ylim(0, 100)
        ax6.grid(True, alpha=0.3)

        # 添加百分比标签
        for bar, pct in zip(bars, sig_counts):
            ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{pct:.1f}%', ha='center', va='bottom')

        plt.tight_layout()

        # 保存图表
        plot_file = self.output_path / "quality_plots.png"
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 质量可视化图表已保存")

if __name__ == "__main__":
    # 运行标准化HEP分析
    analyzer = StandardizedHEPAnalyzer()
    results, summary = analyzer.run_analysis()
    
    print("\n🎉 标准化HEP分析完成！")
    print("📊 使用了最新文献的标准参数，包括:")
    print("   - 最小RR间隔控制 (600ms)")
    print("   - 标准滤波参数 (0.1-30Hz)")
    print("   - 文献推荐的epoch和HEP窗口")
    print("   - 自动化心电场伪迹去除") 