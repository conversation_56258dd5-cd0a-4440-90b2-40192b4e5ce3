#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HEP（心跳诱发电位）分析脚本 - Rest条件优化版本
基于07版本，专门优化rest条件的HEP提取成功率

关键技术改进：
1. 固定ECG11电极，避免极性差异问题
2. EEG-ECG同步滤波，确保相位一致性
3. Rest条件使用test条件的检测参数优化
4. 删除所有try逻辑，有错误立即停止便于调试
5. 自适应阈值调整，基于心率变异性优化检测

核心思路：
- 不复制test的心率模式，而是学习test成功检测的技术参数
- 利用test条件的信号质量分析来优化rest条件的检测阈值
- 保持严格的时间同步，避免滤波导致的相位偏移

作者：研究团队
日期：2025年1月
版本：3.0 - Rest条件优化版本
"""

import numpy as np
import mne
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
import pandas as pd
import random
from scipy import signal
import sys
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class HEPAnalyzer:
    """HEP分析器"""

    def __init__(self, data_dir, validation_excel_path, enable_quality_control=True):
        """
        初始化HEP分析器

        Parameters:
        -----------
        data_dir : str
            数据目录路径
        validation_excel_path : str
            电压验证结果Excel文件路径
        enable_quality_control : bool
            是否启用质量控制（默认True）
        """
        self.data_dir = Path(data_dir)
        self.validation_excel_path = Path(validation_excel_path)
        self.enable_quality_control = enable_quality_control

        # 七个实验条件定义
        self.conditions = {
            'prac': '练习状态',
            'rest_1': '静息状态1',
            'rest_2': '静息状态2', 
            'rest_3': '静息状态3',
            'test_1': '测试状态1',
            'test_2': '测试状态2',
            'test_3': '测试状态3'
        }

        # 条件文件匹配规则
        self.condition_patterns = {
            'prac': '*_01_*_prac_*.fif',
            'rest_1': '*_01_*_rest_*.fif',
            'rest_2': '*_02_*_rest_*.fif',
            'rest_3': '*_03_*_rest_*.fif',
            'test_1': '*_01_*_test_*.fif',
            'test_2': '*_02_*_test_*.fif',
            'test_3': '*_03_*_test_*.fif'
        }

        # HEP分析参数
        self.hep_params = {
            'time_window': (-0.2, 0.6),  # R波前200ms到后600ms（标准HEP窗口）
            'baseline_window': (-0.2, -0.05),  # R波前200ms到前50ms作为基线
            'min_heartbeats': 50 ,  # 最小心跳数
            'rr_interval_range': (0.3, 2.0),  # 有效R-R间隔范围(秒)
            'baseline_std_threshold': 0.1,  # 基线标准差阈值(100mV in V units)
            'sampling_rate': 500  # 采样率
        }
        
        # 初始化结果存储2
        self.analysis_results = {
            'selected_files': {},  # 按条件存储选择的文件
            'hep_data': {},       # 按条件存储HEP数据
            'quality_metrics': {} # 按条件存储质量指标
        }
        
        print("="*80)
        print("HEP（心跳诱发电位）分析 - EEG滤波增强版本")
        print("="*80)
        print(f"数据目录: {self.data_dir}")
        print(f"验证结果文件: {self.validation_excel_path}")
        print(f"实验条件: {list(self.conditions.keys())}")
        print(f"HEP时间窗口: {self.hep_params['time_window'][0]*1000:.0f}ms 到 {self.hep_params['time_window'][1]*1000:.0f}ms")

    def apply_synchronized_filtering(self, eeg_data, ecg_data, sampling_rate):
        """
        应用同步滤波，确保EEG和ECG的相位一致性

        关键改进：
        1. EEG和ECG使用相同的滤波器设计策略
        2. 零相位滤波避免时间偏移
        3. 保持严格的时间同步关系
        4. 不使用滤波器滚降，保持原始信号特征

        Parameters:
        -----------
        eeg_data : array
            EEG数据 (channels x samples)
        ecg_data : array
            ECG数据 (channels x samples)
        sampling_rate : float
            采样率

        Returns:
        --------
        eeg_filtered : array
            滤波后的EEG数据
        ecg_filtered : array
            滤波后的ECG数据
        """
        print("    🔧 应用同步滤波（无滤波器滚降）...")

        # 直接返回原始数据，不进行滤波
        eeg_filtered = eeg_data.copy()
        ecg_filtered = ecg_data.copy()

        print(f"    ✅ 同步处理完成（无滤波）- EEG: {eeg_filtered.shape}, ECG: {ecg_filtered.shape}")
        return eeg_filtered, ecg_filtered

    def detect_r_waves_fixed_electrode(self, ecg_data, sampling_rate, adaptive_params=None):
        """
        使用固定ECG11电极检测R波，避免极性差异问题

        关键改进：
        1. 固定使用ECG11电极，避免不同电极位置的极性差异
        2. 使用绝对值处理正负极性问题
        3. 支持从test条件学习的自适应参数
        4. 严格的R-R间隔验证

        Parameters:
        -----------
        ecg_data : array
            ECG信号数据 (channels x samples) - 已经过同步滤波
        sampling_rate : float
            采样率
        adaptive_params : dict, optional
            从test条件学习的自适应参数

        Returns:
        --------
        r_peaks : array
            R波峰值位置（样本点）
        quality_score : float
            检测质量评分
        """
        # 固定使用ECG11通道（索引10）
        fixed_channel = 10  # ECG11

        if fixed_channel >= ecg_data.shape[0]:
            raise ValueError(f"固定ECG通道{fixed_channel+1}不存在，数据只有{ecg_data.shape[0]}个通道")

        ecg_signal = ecg_data[fixed_channel, :]
        print(f"    🎯 使用固定电极: ECG{fixed_channel+1:02d}")

        # 步骤1: 增强QRS复合波 - 使用绝对值处理极性差异
        ecg_abs = np.abs(ecg_signal)  # 处理正负极性问题
        ecg_squared = ecg_abs ** 2

        # 步骤2: 移动平均平滑
        window_size = int(0.08 * sampling_rate)  # 80ms窗口
        ecg_smoothed = np.convolve(ecg_squared, np.ones(window_size)/window_size, mode='same')

        # 步骤3: 自适应阈值设置
        if adaptive_params and 'threshold_factor' in adaptive_params:
            threshold_factor = adaptive_params['threshold_factor']
            print(f"    📊 使用自适应阈值因子: {threshold_factor:.2f}")
        else:
            threshold_factor = 2.0  # 默认阈值

        threshold = np.mean(ecg_smoothed) + threshold_factor * np.std(ecg_smoothed)

        # 步骤4: 粗略峰值检测
        min_distance = int(0.3 * sampling_rate)  # 最小间隔300ms
        rough_peaks, _ = signal.find_peaks(
            ecg_smoothed,
            height=threshold,
            distance=min_distance,
            prominence=threshold * 0.3  # 添加突出度要求
        )

        print(f"    🔍 粗略检测: {len(rough_peaks)} 个候选峰值")

        # 步骤5: 精确定位到原始信号的真实R波位置
        refined_peaks = []
        search_window = int(0.05 * sampling_rate)  # ±50ms搜索窗口

        # 首先判断R波的主要极性
        r_wave_polarity = self._determine_r_wave_polarity(ecg_signal, rough_peaks)
        print(f"    📊 检测到R波极性: {'正向' if r_wave_polarity > 0 else '负向'}")

        for rough_peak in rough_peaks:
            start_idx = max(0, rough_peak - search_window)
            end_idx = min(len(ecg_signal), rough_peak + search_window)

            # 根据极性精确定位R波峰值
            search_segment = ecg_signal[start_idx:end_idx]

            if r_wave_polarity > 0:
                # 正向R波：找最大值
                local_peak = np.argmax(search_segment)
            else:
                # 负向R波：找最小值
                local_peak = np.argmin(search_segment)

            true_r_peak = start_idx + local_peak
            refined_peaks.append(true_r_peak)

        refined_peaks = np.array(refined_peaks)

        # 步骤6: R-R间隔验证和质量评估
        if len(refined_peaks) > 1:
            rr_intervals = np.diff(refined_peaks) / sampling_rate

            # 过滤生理不合理的R-R间隔
            valid_mask = (rr_intervals >= 0.3) & (rr_intervals <= 2.0)

            # 重建有效的R波序列
            valid_peaks = [refined_peaks[0]]  # 保留第一个峰值
            for i, is_valid in enumerate(valid_mask):
                if is_valid:
                    valid_peaks.append(refined_peaks[i + 1])

            valid_peaks = np.array(valid_peaks)

            # 计算质量指标
            if len(valid_peaks) > 1:
                valid_rr = np.diff(valid_peaks) / sampling_rate
                quality_score = len(valid_rr) / len(rr_intervals) if len(rr_intervals) > 0 else 0
                quality_score *= min(1.0, len(valid_peaks) / 50)  # 考虑R波数量
            else:
                quality_score = 0
        else:
            valid_peaks = refined_peaks
            quality_score = 0

        print(f"    ✅ 精确检测: {len(valid_peaks)} 个有效R波, 质量评分: {quality_score:.3f}")

        return valid_peaks, quality_score

    def _determine_r_wave_polarity(self, ecg_signal, rough_peaks):
        """
        判断R波的主要极性

        Parameters:
        -----------
        ecg_signal : array
            ECG信号
        rough_peaks : array
            粗略检测的R波位置

        Returns:
        --------
        polarity : float
            正数表示正向R波，负数表示负向R波
        """
        if len(rough_peaks) == 0:
            return 1.0  # 默认正向

        # 取前几个R波来判断极性
        sample_peaks = rough_peaks[:min(5, len(rough_peaks))]
        amplitudes = []

        for peak in sample_peaks:
            if 0 <= peak < len(ecg_signal):
                amplitudes.append(ecg_signal[peak])

        if len(amplitudes) > 0:
            mean_amplitude = np.mean(amplitudes)
            return mean_amplitude
        else:
            return 1.0  # 默认正向

    def analyze_test_detection_parameters(self, test_r_peaks, test_ecg_data, sampling_rate):
        """
        分析test条件成功检测的参数，用于优化rest检测

        重点：分析检测阈值和信号特征，而非心率模式

        Parameters:
        -----------
        test_r_peaks : array
            Test条件检测到的R波位置
        test_ecg_data : array
            Test条件ECG数据（已滤波）
        sampling_rate : float
            采样率

        Returns:
        --------
        adaptive_params : dict
            自适应参数，用于优化rest检测
        """
        if len(test_r_peaks) < 2:
            return None

        # 使用固定ECG11通道分析
        fixed_channel = 10
        ecg_signal = test_ecg_data[fixed_channel, :]

        # 分析R波幅度特征 - 保持原始极性信息
        r_amplitudes = []
        for peak in test_r_peaks:
            if 0 <= peak < len(ecg_signal):
                r_amplitudes.append(ecg_signal[peak])  # 保持原始极性，不使用绝对值

        if not r_amplitudes:
            return None

        mean_amplitude = np.mean(np.abs(r_amplitudes))  # 计算统计量时使用绝对值
        amplitude_std = np.std(np.abs(r_amplitudes))

        # 分析心率变异性（用于阈值调整）
        rr_intervals = np.diff(test_r_peaks) / sampling_rate
        rr_cv = np.std(rr_intervals) / np.mean(rr_intervals)  # 变异系数

        # 基于心率变异性调整阈值策略
        # 变异性低的信号可以使用更严格的阈值
        if rr_cv < 0.1:  # 低变异性
            optimal_threshold_factor = 2.5
        elif rr_cv < 0.2:  # 中等变异性
            optimal_threshold_factor = 2.0
        else:  # 高变异性
            optimal_threshold_factor = 1.5

        # 分析信号质量 - 使用绝对值计算功率
        signal_power = np.mean(np.array(r_amplitudes) ** 2)
        noise_power = np.var(ecg_signal)
        snr = signal_power / noise_power if noise_power > 0 else 0

        adaptive_params = {
            'threshold_factor': optimal_threshold_factor,
            'mean_amplitude': mean_amplitude,
            'amplitude_std': amplitude_std,
            'rr_variability': rr_cv,
            'snr': snr,
            'signal_quality': min(1.0, snr / 10)
        }

        print(f"    📊 Test分析结果:")
        print(f"      R波数量: {len(test_r_peaks)}")
        print(f"      平均幅度: {mean_amplitude:.2f} μV")
        print(f"      心率变异性: {rr_cv:.3f}")
        print(f"      推荐阈值因子: {optimal_threshold_factor:.2f}")
        print(f"      信噪比: {snr:.2f}")

        return adaptive_params

    def validate_r_peaks(self, r_peaks, sampling_rate):
        """
        验证R波检测质量（支持质量控制开关）

        Parameters:
        -----------
        r_peaks : array
            R波峰值位置
        sampling_rate : float
            采样率

        Returns:
        --------
        valid_peaks : array
            有效的R波峰值
        quality_metrics : dict
            质量指标
        """
        if len(r_peaks) < 2:
            return r_peaks, {'valid_peaks': len(r_peaks), 'rr_intervals': [], 'quality': 'poor'}

        # 计算R-R间隔
        rr_intervals = np.diff(r_peaks) / sampling_rate

        if self.enable_quality_control:
            # 启用质量控制：筛选有效的R-R间隔
            valid_rr_mask = (rr_intervals >= self.hep_params['rr_interval_range'][0]) & \
                           (rr_intervals <= self.hep_params['rr_interval_range'][1])

            # 移除异常间隔对应的R波
            valid_peaks = []
            for i in range(len(r_peaks)):
                if i == 0:  # 第一个峰值
                    if len(valid_rr_mask) > 0 and valid_rr_mask[0]:
                        valid_peaks.append(r_peaks[i])
                elif i == len(r_peaks) - 1:  # 最后一个峰值
                    if valid_rr_mask[i-1]:
                        valid_peaks.append(r_peaks[i])
                else:  # 中间的峰值
                    if valid_rr_mask[i-1] and valid_rr_mask[i]:
                        valid_peaks.append(r_peaks[i])

            valid_peaks = np.array(valid_peaks)
        else:
            # 禁用质量控制：使用所有R波
            valid_peaks = r_peaks

        valid_rr_intervals = np.diff(valid_peaks) / sampling_rate if len(valid_peaks) > 1 else []

        # 质量评估
        if self.enable_quality_control:
            if len(valid_peaks) >= self.hep_params['min_heartbeats']:
                quality = 'good'
            elif len(valid_peaks) >= 50:
                quality = 'fair'
            else:
                quality = 'poor'
        else:
            # 禁用质量控制时，根据总数评估
            if len(valid_peaks) >= 50:
                quality = 'acceptable_no_qc'
            else:
                quality = 'insufficient_data'

        quality_metrics = {
            'total_peaks': len(r_peaks),
            'valid_peaks': len(valid_peaks),
            'mean_rr_interval': np.mean(valid_rr_intervals) if len(valid_rr_intervals) > 0 else 0,
            'rr_std': np.std(valid_rr_intervals) if len(valid_rr_intervals) > 0 else 0,
            'quality': quality,
            'quality_control_enabled': self.enable_quality_control
        }

        return valid_peaks, quality_metrics

    def extract_hep_epochs(self, eeg_data, r_peaks, sampling_rate, ch_names=None):
        """
        提取HEP时期数据 - 添加详细调试信息，有错误立即停止

        Parameters:
        -----------
        eeg_data : array
            EEG数据 (channels x samples)
        r_peaks : array
            有效R波位置
        sampling_rate : float
            采样率
        ch_names : list, optional
            通道名称列表

        Returns:
        --------
        hep_epochs : array
            HEP时期数据 (epochs x channels x samples)
        times : array
            时间轴
        ch_names : list
            通道名称列表
        """
        print(f"    🔍 开始提取HEP epochs...")
        print(f"      EEG数据形状: {eeg_data.shape}")
        print(f"      R波数量: {len(r_peaks)}")
        print(f"      采样率: {sampling_rate} Hz")
        print(f"      时间窗口: {self.hep_params['time_window']} 秒")
        print(f"      基线窗口: {self.hep_params['baseline_window']} 秒")

        # 计算时间窗口对应的样本点
        pre_samples = int(abs(self.hep_params['time_window'][0]) * sampling_rate)
        post_samples = int(self.hep_params['time_window'][1] * sampling_rate)
        total_samples = pre_samples + post_samples

        print(f"      前置样本点: {pre_samples}")
        print(f"      后置样本点: {post_samples}")
        print(f"      总样本点: {total_samples}")

        # 创建时间轴
        times = np.linspace(self.hep_params['time_window'][0],
                           self.hep_params['time_window'][1],
                           total_samples)

        # 如果没有提供通道名称，创建默认的通道名称
        if ch_names is None:
            ch_names = ([f'EEG{i+1:02d}' for i in range(61)] +
                       [f'ECG{i+1:02d}' for i in range(58)])

        # 提取每个心跳周期的EEG数据
        valid_epochs = []
        invalid_reasons = []

        for i, r_peak in enumerate(r_peaks):
            start_idx = r_peak - pre_samples
            end_idx = r_peak + post_samples

            # 详细检查边界条件
            if start_idx < 0:
                invalid_reasons.append(f"R波{i+1}: start_idx({start_idx}) < 0")
                continue

            if end_idx >= eeg_data.shape[1]:
                invalid_reasons.append(f"R波{i+1}: end_idx({end_idx}) >= 数据长度({eeg_data.shape[1]})")
                continue

            # 提取epoch
            epoch = eeg_data[:, start_idx:end_idx]

            # 检查epoch形状
            if epoch.shape[1] != total_samples:
                invalid_reasons.append(f"R波{i+1}: epoch长度({epoch.shape[1]}) != 期望长度({total_samples})")
                continue

            # 基线校正
            baseline_start_time = self.hep_params['baseline_window'][0] - self.hep_params['time_window'][0]
            baseline_end_time = self.hep_params['baseline_window'][1] - self.hep_params['time_window'][0]

            baseline_start = int(baseline_start_time * sampling_rate)
            baseline_end = int(baseline_end_time * sampling_rate)

            # 检查基线窗口
            if baseline_start < 0:
                invalid_reasons.append(f"R波{i+1}: baseline_start({baseline_start}) < 0")
                continue

            if baseline_end > epoch.shape[1]:
                invalid_reasons.append(f"R波{i+1}: baseline_end({baseline_end}) > epoch长度({epoch.shape[1]})")
                continue

            if baseline_start >= baseline_end:
                invalid_reasons.append(f"R波{i+1}: baseline_start({baseline_start}) >= baseline_end({baseline_end})")
                continue

            # 应用基线校正
            baseline_mean = np.mean(epoch[:, baseline_start:baseline_end], axis=1, keepdims=True)
            epoch_corrected = epoch - baseline_mean
            valid_epochs.append(epoch_corrected)

        print(f"      有效epochs: {len(valid_epochs)}")
        print(f"      无效epochs: {len(invalid_reasons)}")

        # 打印前几个无效原因
        if invalid_reasons:
            print(f"      无效原因示例:")
            for reason in invalid_reasons[:5]:  # 只显示前5个
                print(f"        - {reason}")
            if len(invalid_reasons) > 5:
                print(f"        ... 还有{len(invalid_reasons)-5}个无效epochs")

        if len(valid_epochs) == 0:
            print(f"    ❌ 未提取到任何有效的HEP epochs!")
            print(f"    📊 详细信息:")
            print(f"      - R波位置范围: {np.min(r_peaks)} - {np.max(r_peaks)}")
            print(f"      - 数据总长度: {eeg_data.shape[1]} 样本点")
            print(f"      - 需要的前置空间: {pre_samples} 样本点")
            print(f"      - 需要的后置空间: {post_samples} 样本点")
            print(f"      - 第一个R波位置: {r_peaks[0] if len(r_peaks) > 0 else 'N/A'}")
            print(f"      - 最后一个R波位置: {r_peaks[-1] if len(r_peaks) > 0 else 'N/A'}")

            # 强制停止程序以便调试
            raise ValueError("未提取到任何有效的HEP epochs，请检查R波位置和数据长度的匹配关系")

        if len(valid_epochs) > 0:
            hep_epochs = np.array(valid_epochs)
            print(f"    ✅ 成功提取 {len(hep_epochs)} 个HEP epochs")
            print(f"      Epochs形状: {hep_epochs.shape}")
            return hep_epochs, times, ch_names
        else:
            return None, times, ch_names

    def save_as_fif(self, condition, hep_epochs, times, ch_names, sfreq, subject_id):
        """
        将HEP数据保存为MNE的.fif格式 - 删除try逻辑，有错误立即停止

        Parameters:
        -----------
        condition : str
            实验条件名称
        hep_epochs : array
            HEP时期数据 (epochs x channels x times)
        times : array
            时间点数组
        ch_names : list
            通道名称列表
        sfreq : float
            采样率
        subject_id : str
            被试编号
        """
        # 创建保存目录
        save_dir = Path("D:/ecgeeg/30-数据分析/5-HBA/result/hep_fif_data")
        save_dir.mkdir(parents=True, exist_ok=True)

        # 创建info对象
        info = mne.create_info(
            ch_names=ch_names,
            sfreq=sfreq,
            ch_types=['eeg']*61 + ['ecg']*58  # 前61个是脑电，后58个是心电
        )

        # 设置时间信息
        tmin = times[0]

        # 创建EpochsArray对象
        epochs = mne.EpochsArray(
            hep_epochs,
            info,
            tmin=tmin,
            events=np.array([[i, 0, 1] for i in range(len(hep_epochs))]),
            event_id={'hep': 1}
        )

        # 添加额外的元数据
        epochs.metadata = pd.DataFrame({
            'subject_id': [subject_id] * len(hep_epochs),
            'condition': [condition] * len(hep_epochs)
        })

        # 设置epochs的其他属性
        epochs.set_channel_types({ch: 'eeg' for ch in ch_names[:61]})
        epochs.set_channel_types({ch: 'ecg' for ch in ch_names[61:]})

        # 保存为fif文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        fif_file = save_dir / f"sub-{subject_id}_task-hep_cond-{condition}_{timestamp}-epo.fif"
        epochs.save(fif_file, overwrite=True)

        print(f"✅ 条件 {condition} 的HEP数据已保存为MNE格式: {fif_file}")
        return str(fif_file)

    def analyze_single_file(self, file_info):
        """
        分析单个文件 - 针对rest条件使用改进的检测方法

        关键改进：
        1. 对于rest条件，尝试使用对应test条件的参数优化
        2. 使用固定ECG电极和同步滤波
        3. 删除所有try逻辑，有错误立即停止
        """
        # 读取数据
        raw = mne.io.read_raw_fif(file_info['file_path'], preload=True)

        # 获取基本信息
        sampling_rate = raw.info['sfreq']
        ch_names = raw.ch_names
        data = raw.get_data() * 1000  # mV -> μV

        print(f"    📁 文件: {file_info['file_name']}")
        print(f"    📊 数据形状: {data.shape}, 采样率: {sampling_rate} Hz")

        # 分离EEG和ECG数据
        eeg_data = data[:61, :]  # 前61个通道是EEG
        ecg_data = data[61:, :]  # 后58个通道是ECG

        # 应用同步滤波
        eeg_filtered, ecg_filtered = self.apply_synchronized_filtering(eeg_data, ecg_data, sampling_rate)

        # 判断是否为rest条件，如果是则尝试使用test参数优化
        condition = file_info['condition']
        adaptive_params = None

        if 'rest' in condition:
            print(f"    🔄 检测到rest条件，尝试获取test参考参数...")

            # 构建对应test文件的模式
            subject_id = file_info['subject_id']
            if condition == 'rest_1':
                test_pattern = f"{subject_id}_01_*_test_*.fif"
            elif condition == 'rest_2':
                test_pattern = f"{subject_id}_02_*_test_*.fif"
            elif condition == 'rest_3':
                test_pattern = f"{subject_id}_03_*_test_*.fif"
            else:
                test_pattern = None

            # 查找对应的test文件
            if test_pattern:
                test_files = list(self.data_dir.glob(test_pattern))
                if test_files:
                    print(f"    📁 找到对应test文件: {test_files[0].name}")

                    # 读取test数据
                    raw_test = mne.io.read_raw_fif(test_files[0], preload=True)
                    data_test = raw_test.get_data() * 1000

                    # 对test数据应用相同的同步滤波
                    _, test_ecg_filtered = self.apply_synchronized_filtering(
                        data_test[:61, :], data_test[61:, :], sampling_rate
                    )

                    # 检测test的R波
                    test_r_peaks, test_quality = self.detect_r_waves_fixed_electrode(
                        test_ecg_filtered, sampling_rate
                    )

                    if len(test_r_peaks) > 0 and test_quality > 0.3:
                        # 分析test成功的检测参数
                        adaptive_params = self.analyze_test_detection_parameters(
                            test_r_peaks, test_ecg_filtered, sampling_rate
                        )
                        print(f"    🎯 Test参考: {len(test_r_peaks)}个R波, 质量:{test_quality:.3f}")
                    else:
                        print(f"    ⚠️ Test检测质量不佳: {len(test_r_peaks)}个R波, 质量:{test_quality:.3f}")
                else:
                    print(f"    ⚠️ 未找到对应的test文件")

        # 使用改进的R波检测方法
        if 'rest' in condition:
            # Rest条件使用固定电极和自适应参数
            r_peaks, quality_score = self.detect_r_waves_fixed_electrode(
                ecg_filtered, sampling_rate, adaptive_params
            )
        else:
            # Test和prac条件使用固定电极但不使用自适应参数
            r_peaks, quality_score = self.detect_r_waves_fixed_electrode(
                ecg_filtered, sampling_rate
            )

        # 验证R波检测结果
        valid_peaks, quality_metrics = self.validate_r_peaks(r_peaks, sampling_rate)

        if len(valid_peaks) < self.hep_params['min_heartbeats']:
            return {
                'success': False,
                'error': f"有效心跳数不足: {len(valid_peaks)} < {self.hep_params['min_heartbeats']}",
                'r_peaks_count': len(r_peaks),
                'valid_peaks_count': len(valid_peaks),
                'quality_score': quality_score
            }

        # 提取HEP时期
        hep_epochs, times, ch_names_used = self.extract_hep_epochs(eeg_filtered, valid_peaks, sampling_rate, ch_names[:61])

        if hep_epochs is None:
            return {
                'success': False,
                'error': "未提取到有效的HEP时期",
                'r_peaks_count': len(r_peaks),
                'valid_peaks_count': len(valid_peaks),
                'quality_score': quality_score
            }

        return {
            'success': True,
            'hep_epochs': hep_epochs,
            'times': times,
            'channel_names': ch_names_used,
            'quality_metrics': quality_metrics,
            'file_info': file_info,
            'valid_heartbeats': len(valid_peaks),
            'r_peaks_count': len(r_peaks),
            'quality_score': quality_score,
            'adaptive_params_used': adaptive_params is not None
        }

    def select_files_by_condition(self, n_files_per_condition=40):
        """
        按条件选择文件 - 删除try逻辑，有错误立即停止

        Parameters:
        -----------
        n_files_per_condition : int
            每个条件选择的文件数量

        Returns:
        --------
        bool
            是否成功选择文件
        """
        print("\n📂 开始选择文件...")

        # 初始化文件选择结果
        self.analysis_results['selected_files'] = {}

        # 遍历每个条件
        for condition in self.conditions.keys():
            print(f"\n处理条件: {condition} ({self.conditions[condition]})")

            # 构建文件匹配模式
            pattern = self.condition_patterns[condition]

            # 在数据目录中查找匹配的文件
            matched_files = list(Path(self.data_dir).rglob(pattern))

            if not matched_files:
                print(f"  ⚠️ 未找到条件 {condition} 的文件")
                continue

            print(f"  📄 找到 {len(matched_files)} 个文件")

            # 随机选择指定数量的文件
            selected_files = random.sample(matched_files, min(n_files_per_condition, len(matched_files)))

            # 收集文件信息
            file_info_list = []
            for file_path in selected_files:
                # 从文件名中提取被试ID
                subject_id = file_path.stem.split('_')[0]

                # 获取文件大小
                file_size_mb = file_path.stat().st_size / (1024 * 1024)

                file_info = {
                    'file_path': str(file_path),
                    'file_name': file_path.name,
                    'subject_id': subject_id,
                    'condition': condition,
                    'file_size_mb': file_size_mb
                }

                file_info_list.append(file_info)

            self.analysis_results['selected_files'][condition] = file_info_list
            print(f"  ✅ 选择了 {len(file_info_list)} 个文件")

        total_files = sum(len(files) for files in self.analysis_results['selected_files'].values())
        if total_files == 0:
            print("\n❌ 未选择到任何文件")
            return False

        print(f"\n✅ 文件选择完成，共选择 {total_files} 个文件")
        return True

    def analyze_condition(self, condition):
        """
        分析单个条件的数据并逐个文件保存结果

        Parameters:
        -----------
        condition : str
            条件名称
        """
        print(f"\n开始分析条件: {condition} ({self.conditions[condition]})")

        if condition not in self.analysis_results['selected_files']:
            print(f"  ⚠️ 条件 {condition} 无选择的文件")
            return
        
        selected_files = self.analysis_results['selected_files'][condition]
        if not selected_files:
            print(f"  ⚠️ 条件 {condition} 无可用文件")
            return

        # 创建该条件的保存目录
        base_save_dir = Path("D:/ecgeeg/30-数据分析/5-HBA/result/hep_fif_data")
        condition_dir = base_save_dir / condition
        condition_dir.mkdir(parents=True, exist_ok=True)
        
        successful_count = 0
        failed_count = 0
        total_epochs = 0
        processed_subjects = set()
        
        for i, file_info in enumerate(selected_files, 1):
            print(f"\n  处理文件 {i}/{len(selected_files)}: {file_info['file_name']}")

            result = self.analyze_single_file(file_info)

            if result['success']:
                successful_count += 1
                total_epochs += len(result['hep_epochs'])
                processed_subjects.add(file_info['subject_id'])
                print(f"    ✅ 成功 - 心跳数: {result['valid_heartbeats']}")

                # 立即保存当前文件的数据
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                subject_id = file_info['subject_id']

                # 创建info对象并立即保存数据
                # 注意：我们只保存EEG数据，所以只需要EEG通道类型
                info = mne.create_info(
                    ch_names=result['channel_names'],
                    sfreq=self.hep_params['sampling_rate'],
                    ch_types=['eeg'] * len(result['channel_names'])
                )

                # 创建并立即保存EpochsArray对象
                epochs = mne.EpochsArray(
                    result['hep_epochs'],
                    info,
                    tmin=result['times'][0],
                    events=np.array([[i, 0, 1] for i in range(len(result['hep_epochs']))]),
                    event_id={'hep': 1}
                )

                # 添加元数据
                epochs.metadata = pd.DataFrame({
                    'subject_id': [subject_id] * len(result['hep_epochs']),
                    'condition': [condition] * len(result['hep_epochs'])
                })

                # 通道类型已在create_info中设置为EEG，无需额外设置

                # 保存为fif文件
                fif_file = condition_dir / f"sub-{subject_id}_task-hep_cond-{condition}_{timestamp}-epo.fif"
                epochs.save(fif_file, overwrite=True)

                print(f"    💾 数据已保存:")
                print(f"      📊 {len(result['hep_epochs'])} 个epochs")
                print(f"      📄 数据文件: {fif_file.name}")

                # 清理内存
                del epochs
                del result['hep_epochs']
                del result

            else:
                failed_count += 1
                print(f"    ❌ 失败 - {result['error']}")

            # 定期进行垃圾回收
            if i % 5 == 0:
                import gc
                gc.collect()

        # 存储简化的分析结果
        self.analysis_results['hep_data'][condition] = {
            'successful_count': successful_count,
            'failed_count': failed_count,
            'total_epochs': total_epochs,
            'processed_subjects': list(processed_subjects)
        }
        
        print(f"\n条件 {condition} 分析完成:")
        print(f"  成功: {successful_count}/{len(selected_files)}")
        print(f"  失败: {failed_count}/{len(selected_files)}")
        print(f"  总epochs数: {total_epochs}")
        print(f"  处理的被试数: {len(processed_subjects)}")
        print(f"  📁 数据保存在: {condition_dir}")
        
        # 最终清理
        gc.collect()

    def save_analysis_results(self):
        """打印分析汇总信息"""
        print("\n📊 分析汇总:")

        for condition in self.conditions.keys():
            if condition in self.analysis_results['hep_data']:
                condition_data = self.analysis_results['hep_data'][condition]

                # 安全地获取成功和失败的数据
                successful_data = condition_data.get('successful', [])
                failed_data = condition_data.get('failed', [])

                successful = len(successful_data)
                failed = len(failed_data)
                total_epochs = sum(len(r['hep_epochs']) for r in successful_data if 'hep_epochs' in r)
                subjects = sorted(list(set(r['file_info']['subject_id']
                                        for r in successful_data if 'file_info' in r)))

                print(f"\n条件: {condition} ({self.conditions[condition]})")
                print(f"  成功文件数: {successful}")
                print(f"  失败文件数: {failed}")
                print(f"  总epochs数: {total_epochs}")
                print(f"  被试数量: {len(subjects)}")
                if subjects:
                    print(f"  被试列表: {', '.join(subjects)}")

        print("\n✅ 分析完成!")

    def compare_paired_r_peaks(self, file_info_rest, file_info_test):
        """比较同一采集时段的rest和test的R波位置 - 删除try逻辑，有错误立即停止"""
        print(f"\n比较R波检测结果:")
        print(f"Rest文件: {file_info_rest['file_name']}")
        print(f"Test文件: {file_info_test['file_name']}")

        # 读取rest数据
        raw_rest = mne.io.read_raw_fif(file_info_rest['file_path'], preload=True)
        data_rest = raw_rest.get_data()
        data_rest = data_rest * 1000  # mV -> μV

        # 读取test数据
        raw_test = mne.io.read_raw_fif(file_info_test['file_path'], preload=True)
        data_test = raw_test.get_data()
        data_test = data_test * 1000  # mV -> μV

        # 应用同步滤波
        _, ecg_rest_filtered = self.apply_synchronized_filtering(data_rest[:61, :], data_rest[61:, :], raw_rest.info['sfreq'])
        _, ecg_test_filtered = self.apply_synchronized_filtering(data_test[:61, :], data_test[61:, :], raw_test.info['sfreq'])

        # 使用改进的R波检测方法
        r_peaks_rest, quality_rest = self.detect_r_waves_fixed_electrode(ecg_rest_filtered, raw_rest.info['sfreq'])
        r_peaks_test, quality_test = self.detect_r_waves_fixed_electrode(ecg_test_filtered, raw_test.info['sfreq'])

        # 计算基本统计信息
        print(f"\nR波数量对比:")
        print(f"Rest: {len(r_peaks_rest)} 个R波, 质量: {quality_rest:.3f}")
        print(f"Test: {len(r_peaks_test)} 个R波, 质量: {quality_test:.3f}")

        # 计算R-R间隔
        rr_intervals_rest = np.diff(r_peaks_rest) / raw_rest.info['sfreq'] if len(r_peaks_rest) > 1 else []
        rr_intervals_test = np.diff(r_peaks_test) / raw_test.info['sfreq'] if len(r_peaks_test) > 1 else []

        if len(rr_intervals_rest) > 0 and len(rr_intervals_test) > 0:
            print(f"\nR-R间隔统计 (秒):")
            print(f"Rest - 平均: {np.mean(rr_intervals_rest):.3f}, 标准差: {np.std(rr_intervals_rest):.3f}")
            print(f"Test - 平均: {np.mean(rr_intervals_test):.3f}, 标准差: {np.std(rr_intervals_test):.3f}")

        # 绘制ECG信号和R波检测结果
        plt.figure(figsize=(15, 10))

        # 选择一段相同时长的数据进行对比（例如前10秒）
        duration = 10  # 秒
        samples = int(duration * raw_rest.info['sfreq'])

        # Rest数据
        plt.subplot(211)
        plt.plot(data_rest[71, :samples], 'b-', label='ECG信号')  # ECG11通道
        if len(r_peaks_rest) > 0:
            valid_peaks_rest = r_peaks_rest[r_peaks_rest < samples]
            if len(valid_peaks_rest) > 0:
                plt.plot(valid_peaks_rest,
                        data_rest[71, valid_peaks_rest],
                        'r^', label='检测到的R波')
        plt.title('Rest条件 ECG信号和R波检测')
        plt.legend()

        # Test数据
        plt.subplot(212)
        plt.plot(data_test[71, :samples], 'g-', label='ECG信号')  # ECG11通道
        if len(r_peaks_test) > 0:
            valid_peaks_test = r_peaks_test[r_peaks_test < samples]
            if len(valid_peaks_test) > 0:
                plt.plot(valid_peaks_test,
                        data_test[71, valid_peaks_test],
                        'r^', label='检测到的R波')
        plt.title('Test条件 ECG信号和R波检测')
        plt.legend()

        plt.tight_layout()

        # 保存图像
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        plot_filename = f"r_wave_comparison_{timestamp}.png"
        plot_path = Path("D:/ecgeeg/30-数据分析/5-HBA/result/r_wave_analysis") / plot_filename
        plot_path.parent.mkdir(parents=True, exist_ok=True)
        plt.savefig(plot_path)
        plt.close()

        print(f"\n✅ R波对比图已保存: {plot_path}")

        return {
            'rest_peaks': len(r_peaks_rest),
            'test_peaks': len(r_peaks_test),
            'rest_quality': quality_rest,
            'test_quality': quality_test,
            'rest_rr_mean': np.mean(rr_intervals_rest) if len(rr_intervals_rest) > 0 else 0,
            'test_rr_mean': np.mean(rr_intervals_test) if len(rr_intervals_test) > 0 else 0,
            'rest_rr_std': np.std(rr_intervals_rest) if len(rr_intervals_rest) > 0 else 0,
            'test_rr_std': np.std(rr_intervals_test) if len(rr_intervals_test) > 0 else 0
        }

    def analyze_condition_pair(self, rest_condition, test_condition):
        """分析同一采集时段的rest和test条件对"""
        print(f"\n分析条件对: {rest_condition} - {test_condition}")
        
        # 获取rest和test的文件列表
        rest_files = self.analysis_results['selected_files'].get(rest_condition, [])
        test_files = self.analysis_results['selected_files'].get(test_condition, [])
        
        if not rest_files or not test_files:
            print("❌ 未找到配对的文件")
            return
        
        # 按被试ID配对文件
        paired_files = []
        for rest_file in rest_files:
            rest_subject = rest_file['subject_id']
            matching_test = next((test_file for test_file in test_files 
                                if test_file['subject_id'] == rest_subject), None)
            if matching_test:
                paired_files.append((rest_file, matching_test))
        
        print(f"找到 {len(paired_files)} 对配对文件")
        
        # 分析每对文件
        for rest_file, test_file in paired_files:
            print(f"\n分析被试 {rest_file['subject_id']}:")
            comparison_results = self.compare_paired_r_peaks(rest_file, test_file)
            
            if comparison_results:
                # 可以在这里添加更多的分析和处理逻辑
                pass

if __name__ == "__main__":
    # 配置参数
    data_directory = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"
    validation_excel = r"D:\ecgeeg\30-数据分析\5-HBA\result\unit_conversion\final_voltage_validation_data_20250602_225214.xlsx"

    # 选择运行模式
    print("请选择运行模式:")
    print("1. 启用质量控制（默认，严格筛选数据）")
    print("2. 禁用质量控制（使用所有可用数据）")

    choice = input("请输入选择 (1/2，默认为1): ").strip()
    enable_qc = choice != "2"

    print(f"\n{'='*80}")
    print(f"运行模式: {'启用质量控制' if enable_qc else '禁用质量控制'}")
    print(f"{'='*80}")

    # 创建HEP分析器
    analyzer = HEPAnalyzer(data_directory, validation_excel, enable_quality_control=enable_qc)

    # 运行分析
    print("\n开始HEP分析...")

    # 选择文件
    if not analyzer.select_files_by_condition():
        print("❌ 文件选择失败")
        sys.exit(1)

    # 分析每个条件
    for condition in analyzer.conditions.keys():
        print(f"\n分析条件: {condition}")
        analyzer.analyze_condition(condition)

    # 保存结果
    analyzer.save_analysis_results()

    print("\n✅ 分析完成!")
