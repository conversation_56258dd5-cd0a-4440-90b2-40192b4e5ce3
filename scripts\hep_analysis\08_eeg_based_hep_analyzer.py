#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于EEG信号的HEP心跳诱发电位分析脚本
直接从EEG信号中检测心跳事件，不依赖ECG信号

主要特点：
1. 基于EEG信号的心跳伪迹检测
2. 标准化HEP提取窗口(-200ms到650ms)
3. 七个实验条件分析
4. MNE标准fif格式保存

作者：研究团队
日期：2025年6月
版本：1.0 (EEG-based)
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import mne
import scipy.signal as signal
from scipy.stats import zscore
from pathlib import Path
from datetime import datetime
import pickle
import json
import random
import warnings

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['LXGW WenKai', 'SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore')

class EEGBasedHEPAnalyzer:
    """基于EEG信号的HEP分析器"""
    
    def __init__(self, data_dir, validation_excel_path, quality_control_level='moderate'):
        """
        初始化基于EEG的HEP分析器
        
        Parameters:
        -----------
        data_dir : str
            数据目录路径
        validation_excel_path : str
            电压验证结果Excel文件路径
        quality_control_level : str
            质量控制级别：'strict', 'moderate', 'loose'
        """
        self.data_dir = Path(data_dir)
        self.validation_excel_path = Path(validation_excel_path)
        self.quality_control_level = quality_control_level
        
        # 质量控制参数
        self.quality_levels = {
            'strict': {
                'min_heartbeats': 80,
                'artifact_threshold': 2.5,  # EEG伪迹检测阈值
                'consistency_threshold': 0.8,  # 心跳间隔一致性
                'min_quality_score': 70
            },
            'moderate': {
                'min_heartbeats': 60,
                'artifact_threshold': 2.0,
                'consistency_threshold': 0.6,
                'min_quality_score': 50
            },
            'loose': {
                'min_heartbeats': 40,
                'artifact_threshold': 1.5,
                'consistency_threshold': 0.4,
                'min_quality_score': 30
            }
        }
        
        # 当前使用的质量控制参数
        self.current_qc_params = self.quality_levels[quality_control_level]
        
        # HEP分析参数（标准化窗口）
        self.hep_params = {
            'time_window': (-0.2, 0.65),  # -200ms到650ms
            'baseline_window': (-0.2, 0),  # -200ms到0ms作为基线
            'sampling_rate': 500  # 采样率
        }
        
        # 实验条件定义
        self.conditions = {
            'prac': '练习状态',
            'rest1': '静息状态1',
            'rest2': '静息状态2', 
            'rest3': '静息状态3',
            'test1': '测试状态1',
            'test2': '测试状态2',
            'test3': '测试状态3'
        }
        
        # 心跳伪迹检测相关电极（胸部附近电极更容易检测到心跳伪迹）
        self.cardiac_sensitive_channels = [
            'Fp1', 'Fp2', 'F7', 'F8', 'FT9', 'FT10', 'T7', 'T8',
            'TP9', 'TP10', 'C3', 'C4', 'Cz'
        ]
        
        # 脑区电极分组
        self.electrode_groups = {
            '额叶': ['Fp1', 'Fp2', 'F7', 'F3', 'Fz', 'F4', 'F8'],
            '中央': ['FC5', 'FC1', 'FC2', 'FC6', 'C3', 'Cz', 'C4'],
            '顶叶': ['CP5', 'CP1', 'CP2', 'CP6', 'P7', 'P3', 'Pz', 'P4', 'P8'],
            '颞叶': ['FT9', 'FT10', 'T7', 'T8', 'TP9', 'TP10'],
            '枕叶': ['PO9', 'O1', 'Oz', 'O2', 'PO10'],
            '其他': ['AF7', 'AF3', 'AF4', 'AF8', 'F5', 'F1', 'F2', 'F6']
        }
        
        # 结果存储
        self.analysis_results = {
            'selected_files': {},  # 按条件存储选择的文件
            'hep_data': {},        # 按条件存储HEP数据
            'quality_metrics': {}, # 按条件存储质量指标
            'group_averages': {},  # 按条件存储脑区平均数据
            'rejected_data': {},   # 按条件存储被淘汰的数据详情
            'detection_stats': {} # 心跳检测统计信息
        }
        
        print(f"✅ 基于EEG的HEP分析器初始化完成")
        print(f"   质量控制级别: {quality_control_level}")
        print(f"   最小心跳数要求: {self.current_qc_params['min_heartbeats']}")
        print(f"   HEP时间窗口: {self.hep_params['time_window']}")
        print(f"   心跳检测基于: EEG信号伪迹检测")
    
    def detect_heartbeats_from_eeg(self, eeg_data, channel_names, sampling_rate):
        """
        从EEG信号中检测心跳事件
        
        改进版本：
        1. 基于信号质量自动选择最佳通道
        2. 扩展心跳敏感电极范围
        3. 使用多种方法综合评估
        
        Parameters:
        -----------
        eeg_data : array
            EEG数据 (channels x samples)
        channel_names : list
            通道名称列表
        sampling_rate : float
            采样率
            
        Returns:
        --------
        heartbeat_events : array
            心跳事件位置
        detection_info : dict
            检测信息和质量指标
        """
        print(f"    🫀 从EEG信号中检测心跳事件 (改进版本)...")
        
        # 扩展心跳敏感电极列表 - 包含更多可能检测到心跳伪迹的电极
        extended_cardiac_channels = [
            # 前额区域 - 最容易检测到心跳伪迹
            'Fp1', 'Fp2', 'Fpz', 'AF3', 'AF4', 'AF7', 'AF8',
            # 颞部区域 - 接近血管
            'F7', 'F8', 'FT7', 'FT8', 'FT9', 'FT10', 'T7', 'T8', 'TP7', 'TP8', 'TP9', 'TP10',
            # 中央区域 - 可能检测到传导的心跳信号
            'C3', 'C4', 'Cz', 'FC1', 'FC2', 'FC3', 'FC4', 'FC5', 'FC6',
            # 下方电极 - 更接近心脏
            'CP1', 'CP2', 'P3', 'P4', 'Pz',
            # 其他可能的通道
            'F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'Fz'
        ]
        
        # 找到可用的心跳敏感电极
        available_cardiac_channels = []
        cardiac_indices = []
        
        for electrode in extended_cardiac_channels:
            if electrode in channel_names:
                idx = channel_names.index(electrode)
                available_cardiac_channels.append(electrode)
                cardiac_indices.append(idx)
        
        # 如果没有找到标准的心跳敏感电极，使用前部分通道
        if not cardiac_indices:
            print(f"    ⚠️ 未找到标准心跳敏感电极，使用前32个通道")
            cardiac_indices = list(range(min(32, len(channel_names))))
            available_cardiac_channels = [channel_names[i] for i in cardiac_indices]
        
        print(f"    🔍 候选检测通道数: {len(cardiac_indices)}")
        print(f"    📡 检测通道: {', '.join(available_cardiac_channels[:10])}{'...' if len(available_cardiac_channels) > 10 else ''}")
        
        # 预筛选阶段：快速评估各通道的心跳信号强度
        print(f"    🎯 预筛选阶段：评估通道信号质量...")
        
        channel_scores = []
        for i, ch_idx in enumerate(cardiac_indices):
            ch_name = available_cardiac_channels[i]
            ch_data = eeg_data[ch_idx, :]
            
            # 快速评估：基于心跳频段的功率和信号特征
            try:
                # 1. 心跳频段功率
                freqs, psd = signal.welch(ch_data, fs=sampling_rate, nperseg=min(len(ch_data)//8, 512))
                heart_freq_mask = (freqs >= 0.8) & (freqs <= 2.5)
                heart_power = np.mean(psd[heart_freq_mask]) if np.any(heart_freq_mask) else 0
                
                # 2. 信号变异性（心跳伪迹通常有周期性变化）
                signal_std = np.std(ch_data)
                
                # 3. 带通滤波后的峰值特性
                try:
                    filtered = signal.filtfilt(
                        *signal.butter(2, [0.8, 3.0], btype='band', fs=sampling_rate),
                        ch_data
                    )
                    peak_strength = np.std(np.abs(filtered))
                except:
                    peak_strength = 0
                
                # 综合评分
                composite_score = (
                    np.log10(heart_power + 1e-10) * 0.4 +  # 心跳频段功率
                    np.log10(signal_std + 1e-10) * 0.3 +   # 信号变异性  
                    np.log10(peak_strength + 1e-10) * 0.3  # 峰值强度
                )
                
                channel_scores.append((ch_idx, ch_name, composite_score))
                
            except Exception as e:
                print(f"    ⚠️ 通道 {ch_name} 预筛选失败: {e}")
                channel_scores.append((ch_idx, ch_name, -100))  # 给失败的通道很低的分数
        
        # 根据预筛选分数排序
        channel_scores.sort(key=lambda x: x[2], reverse=True)
        
        # 选择前N个最有希望的通道进行详细检测
        top_n = min(8, len(channel_scores))  # 最多检测8个最好的通道
        selected_channels = channel_scores[:top_n]
        
        print(f"    🏆 选择前 {top_n} 个通道进行详细检测:")
        for i, (ch_idx, ch_name, score) in enumerate(selected_channels):
            print(f"      {i+1}. {ch_name} (评分: {score:.2f})")
        
        # 详细检测阶段
        print(f"    🔬 详细检测阶段...")
        
        best_events = []
        best_channel = None
        best_method = None
        max_quality_score = 0
        all_channel_results = {}
        
        for ch_idx, ch_name, prelim_score in selected_channels:
            ch_data = eeg_data[ch_idx, :]
            
            print(f"    🧪 正在检测通道: {ch_name}")
            
            # 使用改进的多方法检测
            channel_results = self._detect_cardiac_artifacts_multiple_methods(
                ch_data, sampling_rate, ch_name
            )
            all_channel_results[ch_name] = channel_results
            
            # 评估当前通道的检测质量
            current_quality = channel_results['best_quality_score']
            
            # 综合考虑预筛选分数和检测质量
            # 预筛选分数占30%，检测质量占70%
            if current_quality > 0:
                combined_score = 0.3 * (prelim_score + 10) + 0.7 * current_quality  # +10是为了避免负数
            else:
                combined_score = 0
                
            print(f"      预筛选分数: {prelim_score:.2f}, 检测质量: {current_quality:.1f}, 综合分数: {combined_score:.1f}")
            
            if combined_score > max_quality_score:
                max_quality_score = combined_score
                best_events = channel_results['best_events']
                best_channel = ch_name
                best_method = channel_results['best_method']
        
        # 结果汇总
        if best_events is not None and len(best_events) > 0:
            print(f"    ✅ 心跳检测成功！")
            print(f"      最佳通道: {best_channel}")
            print(f"      最佳方法: {best_method}")
            print(f"      检测到心跳数: {len(best_events)}")
            print(f"      质量分数: {max_quality_score:.1f}")
            
            # 计算心率统计
            if len(best_events) > 1:
                intervals = np.diff(best_events) / sampling_rate
                valid_intervals = intervals[(intervals >= 0.4) & (intervals <= 2.0)]
                if len(valid_intervals) > 0:
                    avg_heart_rate = 60 / np.mean(valid_intervals)
                    hr_std = 60 * np.std(valid_intervals) / (np.mean(valid_intervals) ** 2)
                    print(f"      平均心率: {avg_heart_rate:.1f} ± {hr_std:.1f} BPM")
        else:
            print(f"    ❌ 未能检测到有效的心跳事件")
            best_events = np.array([])
        
        detection_info = {
            'best_channel': best_channel,
            'best_method': best_method,
            'best_quality_score': max_quality_score,
            'selected_channels': [ch_name for _, ch_name, _ in selected_channels],
            'all_channel_results': all_channel_results,
            'detection_type': 'eeg_cardiac_artifacts_improved',
            'total_events': len(best_events) if best_events is not None else 0
        }
        
        return best_events, detection_info
    
    def _detect_cardiac_artifacts_multiple_methods(self, eeg_signal, sampling_rate, channel_name):
        """
        使用多种方法检测EEG中的心跳伪迹
        
        改进版本：
        1. 支持正向和负向R波检测
        2. 基于信号质量自动选择检测参数
        3. 与文献中的标准方法保持一致
        
        Parameters:
        -----------
        eeg_signal : array
            单通道EEG信号
        sampling_rate : float
            采样率
        channel_name : str
            通道名称
            
        Returns:
        --------
        results : dict
            包含各种方法的检测结果和质量评估
        """
        methods_results = {}
        
        # 方法1: 改进的R波峰值检测（同时检测正向和负向峰值）
        try:
            # 使用带通滤波器突出心跳频段（0.5-4Hz，更宽的频段以捕获不同形态的R波）
            filtered_signal = signal.filtfilt(
                *signal.butter(3, [0.5, 4], btype='band', fs=sampling_rate),
                eeg_signal
            )
            
            # 计算信号的绝对值，以同时捕获正向和负向峰值
            abs_filtered = np.abs(filtered_signal)
            
            # 动态阈值设置 - 基于信号的统计特性
            mean_abs = np.mean(abs_filtered)
            std_abs = np.std(abs_filtered)
            threshold = mean_abs + self.current_qc_params['artifact_threshold'] * std_abs
            
            # 检测峰值，使用更合理的距离限制
            min_distance = int(0.4 * sampling_rate)  # 最小心跳间隔400ms (150 BPM)
            max_distance = int(2.0 * sampling_rate)  # 最大心跳间隔2000ms (30 BPM)
            
            events_amplitude, _ = signal.find_peaks(
                abs_filtered,
                height=threshold,
                distance=min_distance,
                width=(int(0.05 * sampling_rate), int(0.15 * sampling_rate))  # R波宽度约50-150ms
            )
            
            # 验证检测到的峰值是否符合心跳规律
            if len(events_amplitude) > 1:
                intervals = np.diff(events_amplitude) / sampling_rate
                # 过滤掉不合理的间隔
                valid_mask = (intervals >= 0.4) & (intervals <= 2.0)
                if np.sum(valid_mask) > len(valid_mask) * 0.7:  # 至少70%的间隔是合理的
                    methods_results['improved_r_peak'] = events_amplitude
                else:
                    methods_results['improved_r_peak'] = np.array([])
            else:
                methods_results['improved_r_peak'] = events_amplitude
                
        except Exception as e:
            print(f"    ⚠️ 改进R波检测失败 ({channel_name}): {e}")
            methods_results['improved_r_peak'] = np.array([])
        
        # 方法2: 基于QRS复合波形态的检测
        try:
            # 使用更专业的QRS检测方法
            # 1. 带通滤波 (5-15 Hz) - QRS复合波的主要频段
            qrs_filtered = signal.filtfilt(
                *signal.butter(2, [5, 15], btype='band', fs=sampling_rate),
                eeg_signal
            )
            
            # 2. 导数运算以突出QRS斜率
            derivative = np.diff(qrs_filtered)
            derivative = np.append(derivative, derivative[-1])  # 保持长度一致
            
            # 平方运算以放大差异
            squared = derivative ** 2
            
            # 移动平均平滑
            window_size = int(0.08 * sampling_rate)  # 80ms窗口
            if window_size > 0:
                smoothed = signal.convolve(squared, np.ones(window_size)/window_size, mode='same')
            else:
                smoothed = squared
            
            # 自适应阈值检测
            threshold_qrs = np.percentile(smoothed, 85)
            
            events_qrs, _ = signal.find_peaks(
                smoothed,
                height=threshold_qrs,
                distance=int(0.4 * sampling_rate)
            )
            methods_results['qrs_morphology'] = events_qrs
            
        except Exception as e:
            print(f"    ⚠️ QRS形态检测失败 ({channel_name}): {e}")
            methods_results['qrs_morphology'] = np.array([])
        
        # 方法3: 基于心跳模板匹配的检测（支持正负极性）
        try:
            # 创建正向和负向的心跳模板
            template_length = int(0.12 * sampling_rate)  # 120ms模板长度
            
            # 正向R波模板 (上升-下降)
            t = np.linspace(0, 1, template_length)
            positive_template = np.exp(-((t - 0.3) / 0.1) ** 2) - 0.3 * np.exp(-((t - 0.7) / 0.2) ** 2)
            
            # 负向R波模板 (下降-上升) 
            negative_template = -positive_template
            
            # 分别与正负模板进行相关
            pos_correlation = signal.correlate(eeg_signal, positive_template, mode='valid')
            neg_correlation = signal.correlate(eeg_signal, negative_template, mode='valid')
            
            # 取绝对值，选择更强的相关性
            abs_pos_corr = np.abs(pos_correlation)
            abs_neg_corr = np.abs(neg_correlation)
            
            # 选择较强的相关性
            if np.max(abs_pos_corr) > np.max(abs_neg_corr):
                correlation = abs_pos_corr
                template_type = 'positive'
            else:
                correlation = abs_neg_corr
                template_type = 'negative'
            
            # 检测相关峰值
            threshold_corr = np.percentile(correlation, 88)
            events_template, _ = signal.find_peaks(
                correlation,
                height=threshold_corr,
                distance=int(0.4 * sampling_rate)
            )
            
            # 调整索引位置（考虑模板长度）
            events_template = events_template + template_length // 2
            
            # 确保索引不超出信号范围
            events_template = events_template[events_template < len(eeg_signal)]
            
            methods_results['template_matching'] = events_template
            
        except Exception as e:
            print(f"    ⚠️ 模板匹配检测失败 ({channel_name}): {e}")
            methods_results['template_matching'] = np.array([])
        
        # 方法4: 基于频域特征的心跳检测
        try:
            # 计算信号的频谱
            freqs, psd = signal.welch(eeg_signal, fs=sampling_rate, nperseg=min(len(eeg_signal)//4, 1024))
            
            # 寻找心跳频段的主峰 (0.8-2.5 Hz, 对应48-150 BPM)
            heart_freq_mask = (freqs >= 0.8) & (freqs <= 2.5)
            if np.any(heart_freq_mask):
                heart_freqs = freqs[heart_freq_mask]
                heart_psd = psd[heart_freq_mask]
                
                # 找到功率最大的频率
                peak_idx = np.argmax(heart_psd)
                dominant_freq = heart_freqs[peak_idx]
                
                # 基于主导频率预测心跳位置
                period_samples = int(sampling_rate / dominant_freq)
                
                # 在预期位置附近搜索实际的峰值
                predicted_events = []
                
                # 寻找信号中的第一个强峰作为起始点
                abs_signal = np.abs(eeg_signal)
                initial_threshold = np.percentile(abs_signal, 90)
                potential_starts, _ = signal.find_peaks(abs_signal, height=initial_threshold)
                
                if len(potential_starts) > 0:
                    start_pos = potential_starts[0]
                    current_pos = start_pos
                    
                    while current_pos < len(eeg_signal) - period_samples:
                        # 在下一个预期位置附近搜索
                        search_start = max(0, current_pos + int(0.7 * period_samples))
                        search_end = min(len(eeg_signal), current_pos + int(1.3 * period_samples))
                        
                        if search_start < search_end:
                            search_segment = abs_signal[search_start:search_end]
                            if len(search_segment) > 0:
                                local_max_idx = np.argmax(search_segment)
                                actual_pos = search_start + local_max_idx
                                predicted_events.append(actual_pos)
                                current_pos = actual_pos
                            else:
                                break
                        else:
                            break
                
                methods_results['frequency_based'] = np.array(predicted_events)
            else:
                methods_results['frequency_based'] = np.array([])
                
        except Exception as e:
            print(f"    ⚠️ 频域检测失败 ({channel_name}): {e}")
            methods_results['frequency_based'] = np.array([])
        
        # 评估各方法质量并选择最佳
        best_method = None
        best_events = []
        best_quality_score = 0
        
        print(f"    🔍 通道 {channel_name} 检测结果:")
        for method_name, events in methods_results.items():
            if len(events) > 0:
                quality_score = self._evaluate_heartbeat_detection_quality(
                    events, eeg_signal, sampling_rate
                )
                print(f"      {method_name}: {len(events)} 事件, 质量分数: {quality_score:.1f}")
                
                if quality_score > best_quality_score:
                    best_quality_score = quality_score
                    best_events = events
                    best_method = method_name
            else:
                print(f"      {method_name}: 0 事件")
        
        if best_method:
            print(f"    ✅ 最佳方法: {best_method} (质量分数: {best_quality_score:.1f})")
        else:
            print(f"    ❌ 未检测到有效心跳事件")
        
        return {
            'best_events': best_events,
            'best_method': best_method,
            'best_quality_score': best_quality_score,
            'all_methods': {method: len(events) for method, events in methods_results.items()},
            'channel_name': channel_name,
            'detection_type': 'eeg_cardiac_artifacts_improved'
        }
    
    def _evaluate_heartbeat_detection_quality(self, events, eeg_signal, sampling_rate):
        """
        评估心跳检测质量
        
        Parameters:
        -----------
        events : array
            检测到的心跳事件位置
        eeg_signal : array
            EEG信号
        sampling_rate : float
            采样率
            
        Returns:
        --------
        quality_score : float
            质量评分（越高越好）
        """
        if len(events) < 2:
            return 0
        
        # 计算心跳间隔
        intervals = np.diff(events) / sampling_rate
        
        # 质量评估指标
        # 1. 心跳数量（基础分）
        count_score = min(len(events) / 100, 1.0) * 30  # 最多30分
        
        # 2. 心跳间隔合理性（0.4-2.0秒，对应30-150 BPM）
        valid_intervals = intervals[(intervals >= 0.4) & (intervals <= 2.0)]
        if len(intervals) > 0:
            interval_validity = len(valid_intervals) / len(intervals)
            interval_score = interval_validity * 25  # 最多25分
        else:
            interval_score = 0
        
        # 3. 心跳间隔一致性
        if len(valid_intervals) > 1:
            cv = np.std(valid_intervals) / np.mean(valid_intervals)  # 变异系数
            consistency_score = max(0, (1 - cv)) * 25  # 最多25分
        else:
            consistency_score = 0
        
        # 4. 信号质量（基于幅度一致性）
        if len(events) > 0:
            amplitudes = np.abs(eeg_signal[events])
            if len(amplitudes) > 1:
                amp_cv = np.std(amplitudes) / np.mean(amplitudes)
                amplitude_score = max(0, (1 - amp_cv)) * 20  # 最多20分
            else:
                amplitude_score = 10
        else:
            amplitude_score = 0
        
        total_score = count_score + interval_score + consistency_score + amplitude_score
        return total_score 

    def extract_condition_from_filename(self, filename):
        """从文件名提取实验条件"""
        filename_lower = filename.lower()
        parts = filename_lower.split('_')

        # 查找条件关键词
        if 'prac' in filename_lower:
            return 'prac'
        elif 'rest' in filename_lower:
            # 根据被试编号和session编号确定rest1, rest2, rest3
            try:
                subject_id = int(parts[0])
                session_id = int(parts[1])
                if session_id == 1:
                    return 'rest1'
                elif session_id == 2:
                    return 'rest2'
                elif session_id == 3:
                    return 'rest3'
                else:
                    return 'rest1'
            except:
                return 'rest1'
        elif 'test' in filename_lower:
            # 根据被试编号和session编号确定test1, test2, test3
            try:
                subject_id = int(parts[0])
                session_id = int(parts[1])
                if session_id == 1:
                    return 'test1'
                elif session_id == 2:
                    return 'test2'
                elif session_id == 3:
                    return 'test3'
                else:
                    return 'test1'
            except:
                return 'test1'
        return None

    def select_files_by_condition(self, n_files_per_condition=40):
        """按实验条件选择文件"""
        print("\n" + "="*60)
        print("按实验条件选择文件")
        print("="*60)

        try:
            # 读取电压验证结果
            validation_df = pd.read_excel(self.validation_excel_path, sheet_name='验证结果')
            print(f"读取到 {len(validation_df)} 个文件的验证结果")

            # 筛选高质量文件
            high_quality_files = validation_df[validation_df['都在范围内'] == True]
            print(f"高质量文件数量: {len(high_quality_files)}")

            # 按条件分组文件
            condition_files = {condition: [] for condition in self.conditions.keys()}

            for _, row in high_quality_files.iterrows():
                filename = row['文件名']
                condition = self.extract_condition_from_filename(filename)

                if condition and condition in self.conditions:
                    file_path = self.data_dir / filename
                    if file_path.exists():
                        condition_files[condition].append({
                            'file_name': filename,
                            'file_path': file_path,
                            'eeg_amplitude': row['EEG幅度(μV)'],
                            'ecg_amplitude': row['ECG幅度(μV)'],
                            'quality_score': int(row['EEG生理范围']) + int(row['ECG生理范围'])
                        })

            # 为每个条件选择文件
            for condition in self.conditions.keys():
                available_files = condition_files[condition]
                print(f"\n条件 '{condition}' ({self.conditions[condition]}):")
                print(f"  可用文件数: {len(available_files)}")

                if len(available_files) == 0:
                    print(f"  ⚠️ 无可用文件")
                    self.analysis_results['selected_files'][condition] = []
                    continue

                # 按质量分数排序并随机选择
                available_files.sort(key=lambda x: x['quality_score'], reverse=True)
                n_select = min(n_files_per_condition, len(available_files))
                selected_files = random.sample(available_files[:len(available_files)], n_select)

                self.analysis_results['selected_files'][condition] = selected_files
                print(f"  选择文件数: {len(selected_files)}")

            return True

        except Exception as e:
            print(f"❌ 文件选择失败: {str(e)}")
            return False

    def extract_hep_epochs(self, eeg_data, heartbeat_events, sampling_rate):
        """
        提取HEP时期数据
        
        Parameters:
        -----------
        eeg_data : array
            EEG数据 (channels x samples)
        heartbeat_events : array
            心跳事件位置
        sampling_rate : float
            采样率
            
        Returns:
        --------
        hep_epochs : array
            HEP时期数据 (epochs x channels x samples)
        times : array
            时间轴
        """
        # 计算时间窗口对应的样本点
        pre_samples = int(abs(self.hep_params['time_window'][0]) * sampling_rate)
        post_samples = int(self.hep_params['time_window'][1] * sampling_rate)
        total_samples = pre_samples + post_samples

        # 创建时间轴
        times = np.linspace(self.hep_params['time_window'][0],
                           self.hep_params['time_window'][1],
                           total_samples)

        # 提取每个心跳周期的EEG数据
        valid_epochs = []

        for event in heartbeat_events:
            start_idx = event - pre_samples
            end_idx = event + post_samples

            # 确保索引在有效范围内
            if start_idx >= 0 and end_idx < eeg_data.shape[1]:
                epoch = eeg_data[:, start_idx:end_idx]

                # 基线校正
                baseline_start_time = self.hep_params['baseline_window'][0] - self.hep_params['time_window'][0]
                baseline_end_time = self.hep_params['baseline_window'][1] - self.hep_params['time_window'][0]

                baseline_start = int(baseline_start_time * sampling_rate)
                baseline_end = int(baseline_end_time * sampling_rate)

                if baseline_start >= 0 and baseline_end <= epoch.shape[1] and baseline_start < baseline_end:
                    baseline_mean = np.mean(epoch[:, baseline_start:baseline_end], axis=1, keepdims=True)
                    epoch_corrected = epoch - baseline_mean
                    valid_epochs.append(epoch_corrected)

        if len(valid_epochs) > 0:
            hep_epochs = np.array(valid_epochs)
            return hep_epochs, times
        else:
            return None, times

    def analyze_single_file(self, file_info, condition):
        """分析单个文件的HEP"""
        try:
            print(f"  📁 加载文件: {file_info['file_name']}")

            # 读取数据
            raw = mne.io.read_raw_fif(file_info['file_path'], preload=True, verbose=False)
            data = raw.get_data()
            sampling_rate = raw.info['sfreq']

            # 分离EEG数据（前61通道）
            eeg_data = data[:61, :]
            channel_names = raw.ch_names[:61]

            # 应用EEG滤波（0.1-30Hz）
            print(f"  🔧 应用EEG滤波 (0.1-30Hz)...")
            sos = signal.butter(4, [0.1, 30], btype='band', fs=sampling_rate, output='sos')
            eeg_data_filtered = signal.sosfilt(sos, eeg_data, axis=1)

            # 从EEG信号中检测心跳事件
            print(f"  🫀 从EEG信号检测心跳事件...")
            heartbeat_events, detection_info = self.detect_heartbeats_from_eeg(
                eeg_data_filtered, channel_names, sampling_rate
            )

            # 检查心跳数量是否足够
            if len(heartbeat_events) < self.current_qc_params['min_heartbeats']:
                return {
                    'file_name': file_info['file_name'],
                    'condition': condition,
                    'success': False,
                    'reason': 'insufficient_heartbeats',
                    'valid_heartbeats': len(heartbeat_events),
                    'detection_info': detection_info
                }

            # HEP提取
            print(f"  🧠 提取HEP时期...")
            hep_epochs, times = self.extract_hep_epochs(eeg_data_filtered, heartbeat_events, sampling_rate)

            if hep_epochs is None:
                return {
                    'file_name': file_info['file_name'],
                    'condition': condition,
                    'success': False,
                    'reason': 'hep_extraction_failed',
                    'detection_info': detection_info
                }

            # 计算平均HEP
            hep_average = np.mean(hep_epochs, axis=0)

            # 质量指标计算
            quality_metrics = self._calculate_hep_quality_metrics(hep_epochs, times)

            result = {
                'file_name': file_info['file_name'],
                'condition': condition,
                'success': True,
                'hep_epochs': hep_epochs,
                'hep_average': hep_average,
                'times': times,
                'valid_heartbeats': len(heartbeat_events),
                'quality_metrics': quality_metrics,
                'detection_info': detection_info,
                'channel_names': channel_names
            }

            print(f"  ✅ 成功 - 心跳: {len(heartbeat_events)}, HEP时期: {len(hep_epochs)}")
            return result

        except Exception as e:
            return {
                'file_name': file_info['file_name'],
                'condition': condition,
                'success': False,
                'reason': 'analysis_error',
                'error': str(e)
            }

    def _calculate_hep_quality_metrics(self, hep_epochs, times):
        """计算HEP质量指标"""
        # 基线窗口索引
        baseline_mask = (times >= self.hep_params['baseline_window'][0]) & \
                       (times <= self.hep_params['baseline_window'][1])

        # HEP成分窗口（0-400ms）
        hep_mask = (times >= 0) & (times <= 0.4)

        # 计算信噪比
        signal_power = np.mean(np.var(hep_epochs[:, :, hep_mask], axis=2))
        noise_power = np.mean(np.var(hep_epochs[:, :, baseline_mask], axis=2))
        snr = 10 * np.log10(signal_power / noise_power) if noise_power > 0 else 0

        # 基线稳定性
        baseline_std = np.mean(np.std(hep_epochs[:, :, baseline_mask], axis=2))

        # 伪迹检测
        epoch_amplitudes = np.max(np.abs(hep_epochs), axis=(1, 2))
        z_scores = np.abs(zscore(epoch_amplitudes))
        artifact_epochs = np.sum(z_scores > 3)
        artifact_rate = artifact_epochs / len(hep_epochs)

        return {
            'snr': snr,
            'baseline_std': baseline_std,
            'artifact_rate': artifact_rate,
            'artifact_epochs': artifact_epochs,
            'total_epochs': len(hep_epochs)
        }

    def analyze_condition(self, condition):
        """分析单个实验条件"""
        print(f"\n{'='*60}")
        print(f"🔬 分析条件: {condition} ({self.conditions[condition]})")
        print(f"{'='*60}")

        selected_files = self.analysis_results['selected_files'][condition]
        if not selected_files:
            print(f"⚠️ 条件 {condition} 无可用文件")
            return

        successful_results = []
        failed_results = []

        for i, file_info in enumerate(selected_files):
            print(f"\n📄 处理文件 {i+1}/{len(selected_files)}")
            result = self.analyze_single_file(file_info, condition)

            if result['success']:
                successful_results.append(result)
            else:
                failed_results.append(result)
                print(f"    ❌ 失败 - {result['reason']}")

        # 打印分析总结
        print(f"\n📊 条件 {condition} 分析完成:")
        print(f"  ✅ 成功: {len(successful_results)}/{len(selected_files)}")
        print(f"  ❌ 失败: {len(failed_results)}/{len(selected_files)}")

        # 存储结果
        self.analysis_results['hep_data'][condition] = {
            'successful': successful_results,
            'failed': failed_results
        }

        # 计算脑区平均
        if successful_results:
            self._calculate_brain_region_averages(condition, successful_results)

    def _calculate_brain_region_averages(self, condition, successful_results):
        """计算脑区平均HEP"""
        if not successful_results:
            return

        channel_names = successful_results[0]['channel_names']
        times = successful_results[0]['times']

        region_data = {}

        for region_name, electrode_list in self.electrode_groups.items():
            # 找到该脑区对应的通道索引
            region_indices = []
            for electrode in electrode_list:
                if electrode in channel_names:
                    region_indices.append(channel_names.index(electrode))

            if not region_indices:
                continue

            # 收集该脑区所有文件的HEP数据
            region_heps = []
            for result in successful_results:
                region_hep = np.mean(result['hep_average'][region_indices, :], axis=0)
                region_heps.append(region_hep)

            if region_heps:
                region_heps = np.array(region_heps)
                mean_hep = np.mean(region_heps, axis=0)
                sem_hep = np.std(region_heps, axis=0) / np.sqrt(len(region_heps))

                region_data[region_name] = {
                    'mean': mean_hep,
                    'sem': sem_hep,
                    'times': times,
                    'n_files': len(region_heps),
                    'electrodes': [channel_names[i] for i in region_indices]
                }

        self.analysis_results['group_averages'][condition] = region_data 

    def run_complete_analysis(self, n_files_per_condition=40):
        """运行完整的基于EEG的HEP分析"""
        print("="*80)
        print("🚀 基于EEG信号的HEP分析")
        print("="*80)
        print(f"质量控制级别: {self.quality_control_level}")
        print(f"最小心跳数要求: {self.current_qc_params['min_heartbeats']}")
        print(f"HEP时间窗口: {self.hep_params['time_window']}")
        print(f"心跳检测方法: EEG信号伪迹检测")
        print("="*80)

        # 第一步：文件选择
        print("\n🔍 第一步：文件选择")
        if not self.select_files_by_condition(n_files_per_condition):
            print("❌ 文件选择失败，终止分析")
            return False

        # 第二步：分析各个条件
        print("\n🔬 第二步：分析各个条件")
        for condition in self.conditions.keys():
            self.analyze_condition(condition)

        # 第三步：生成总结报告
        print("\n📊 第三步：生成总结报告")
        self.generate_analysis_report()

        # 第四步：保存结果
        print("\n💾 第四步：保存分析结果")
        self.save_analysis_results()

        print("\n" + "="*80)
        print("✅ 基于EEG的HEP分析完成！")
        print("="*80)

        return True

    def generate_analysis_report(self):
        """生成分析报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = Path("../../result/eeg_based_hep_analysis")
        output_dir.mkdir(parents=True, exist_ok=True)

        report_path = output_dir / f"eeg_hep_analysis_report_{timestamp}.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 基于EEG信号的HEP分析报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**质量控制级别**: {self.quality_control_level}\n")
            f.write(f"**HEP时间窗口**: {self.hep_params['time_window']}\n")
            f.write(f"**心跳检测方法**: EEG信号伪迹检测\n")
            f.write(f"**分析器版本**: EEG-based v1.0\n\n")

            # 总体统计
            f.write("## 📊 总体分析统计\n\n")
            total_files = 0
            total_successful = 0
            total_failed = 0

            for condition in self.conditions.keys():
                if condition in self.analysis_results['hep_data']:
                    hep_data = self.analysis_results['hep_data'][condition]
                    successful = len(hep_data['successful'])
                    failed = len(hep_data['failed'])
                    total_files += successful + failed
                    total_successful += successful
                    total_failed += failed

            f.write(f"- **总处理文件数**: {total_files}\n")
            f.write(f"- **成功分析**: {total_successful} ({total_successful/total_files*100:.1f}%)\n")
            f.write(f"- **分析失败**: {total_failed} ({total_failed/total_files*100:.1f}%)\n\n")

            # 各条件详细结果
            f.write("## 🔬 各实验条件分析结果\n\n")
            for condition in self.conditions.keys():
                if condition not in self.analysis_results['hep_data']:
                    continue

                f.write(f"### {condition} - {self.conditions[condition]}\n\n")

                hep_data = self.analysis_results['hep_data'][condition]
                successful = hep_data['successful']
                failed = hep_data['failed']

                f.write(f"- **成功**: {len(successful)} 个文件\n")
                f.write(f"- **失败**: {len(failed)} 个文件\n")

                if successful:
                    # 心跳统计
                    heartbeats = [r['valid_heartbeats'] for r in successful]
                    f.write(f"- **平均心跳数**: {np.mean(heartbeats):.1f} ± {np.std(heartbeats):.1f}\n")

                    # HEP时期统计
                    epochs = [len(r['hep_epochs']) for r in successful]
                    f.write(f"- **平均HEP时期数**: {np.mean(epochs):.1f} ± {np.std(epochs):.1f}\n")

                    # 质量评分统计
                    quality_scores = [r['detection_info']['best_quality_score'] for r in successful]
                    f.write(f"- **平均检测质量评分**: {np.mean(quality_scores):.1f}\n")

                f.write("\n")

            # 方法学说明
            f.write("## 🔬 方法学说明\n\n")
            f.write("### 心跳检测方法\n")
            f.write("本分析使用基于EEG信号的心跳伪迹检测方法，包括：\n")
            f.write("1. **幅度阈值检测**: 检测心跳在EEG中产生的幅度变化\n")
            f.write("2. **梯度检测**: 基于信号梯度变化检测心跳事件\n")
            f.write("3. **功率谱检测**: 分析1-3Hz频段的功率变化\n")
            f.write("4. **模板匹配**: 与典型心跳伪迹模板进行匹配\n\n")

            f.write("### HEP提取参数\n")
            f.write(f"- **时间窗口**: {self.hep_params['time_window']} 秒\n")
            f.write(f"- **基线窗口**: {self.hep_params['baseline_window']} 秒\n")
            f.write("- **滤波**: 0.1-30Hz 带通滤波\n")
            f.write("- **基线校正**: 基于基线窗口的均值校正\n\n")

            f.write("### 质量控制标准\n")
            f.write(f"- **最小心跳数**: {self.current_qc_params['min_heartbeats']}\n")
            f.write(f"- **伪迹检测阈值**: {self.current_qc_params['artifact_threshold']}\n")
            f.write(f"- **一致性阈值**: {self.current_qc_params['consistency_threshold']}\n\n")

        print(f"✅ 分析报告已保存: {report_path}")
        return report_path

    def save_analysis_results(self):
        """保存分析结果为MNE标准格式"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = Path("../../result/eeg_based_hep_analysis")
        output_dir.mkdir(parents=True, exist_ok=True)

        # 创建HEP数据子目录
        hep_data_dir = output_dir / "hep_data"
        hep_data_dir.mkdir(exist_ok=True)

        print(f"\n💾 保存基于EEG的HEP分析结果...")

        saved_files = {}

        # 按条件分别保存MNE格式数据
        for condition in self.conditions.keys():
            if condition not in self.analysis_results['hep_data']:
                continue

            hep_data = self.analysis_results['hep_data'][condition]
            successful_results = hep_data['successful']

            if not successful_results:
                print(f"  ⚠️ 条件 {condition} 无成功数据，跳过保存")
                continue

            print(f"  📁 保存条件 {condition} ({len(successful_results)} 个文件)...")

            # 保存MNE Epochs格式
            epochs_path = self._save_condition_as_mne_epochs(
                condition, successful_results, hep_data_dir, timestamp
            )

            # 保存平均HEP数据
            average_path = self._save_condition_average_hep(
                condition, successful_results, hep_data_dir, timestamp
            )

            saved_files[condition] = {
                'epochs_file': epochs_path,
                'average_file': average_path,
                'n_files': len(successful_results),
                'n_epochs': sum(len(r['hep_epochs']) for r in successful_results)
            }

        # 保存完整结果
        results_path = output_dir / f"eeg_hep_results_{timestamp}.pkl"
        with open(results_path, 'wb') as f:
            pickle.dump(self.analysis_results, f)

        # 保存元数据
        metadata = {
            'timestamp': timestamp,
            'quality_control_level': self.quality_control_level,
            'quality_params': self.current_qc_params,
            'hep_params': self.hep_params,
            'conditions': self.conditions,
            'electrode_groups': self.electrode_groups,
            'version': 'EEG-based v1.0',
            'saved_files': saved_files,
            'detection_method': 'eeg_cardiac_artifacts',
            'data_format': 'MNE_standard_with_condition_separation'
        }

        metadata_path = output_dir / f"eeg_hep_metadata_{timestamp}.json"
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

        print(f"✅ 分析结果已保存: {results_path}")
        print(f"✅ 元数据已保存: {metadata_path}")
        print(f"✅ MNE格式数据已按条件分别保存到: {hep_data_dir}")

        return results_path, metadata_path

    def _save_condition_as_mne_epochs(self, condition, successful_results, output_dir, timestamp):
        """将条件数据保存为MNE Epochs格式"""
        try:
            print(f"    💾 保存 {condition} 为MNE Epochs格式...")

            # 收集所有HEP epochs数据
            all_epochs = []
            all_events = []
            file_info = []

            # 获取第一个文件的信息作为模板
            first_result = successful_results[0]
            times = first_result['times']
            channel_names = first_result['channel_names']
            sampling_rate = int(len(times) / (times[-1] - times[0]))

            event_id = 0
            for result in successful_results:
                hep_epochs = result['hep_epochs']
                n_epochs = len(hep_epochs)

                # 添加epochs数据
                all_epochs.append(hep_epochs)

                # 创建事件标记
                for epoch_idx in range(n_epochs):
                    all_events.append([event_id * 1000, 0, 1])  # 心跳事件类型为1
                    file_info.append({
                        'file_name': result['file_name'],
                        'epoch_index': epoch_idx,
                        'valid_heartbeats': result['valid_heartbeats']
                    })
                    event_id += 1

            # 合并所有epochs
            all_epochs = np.concatenate(all_epochs, axis=0)
            all_events = np.array(all_events)

            print(f"    📊 总epochs数: {len(all_epochs)}, 通道数: {len(channel_names)}")

            # 创建MNE Info对象
            info = mne.create_info(
                ch_names=channel_names,
                sfreq=sampling_rate,
                ch_types='eeg',
                verbose=False
            )

            # 创建MNE Epochs对象
            epochs = mne.EpochsArray(
                all_epochs,
                info,
                events=all_events,
                tmin=times[0],
                event_id={'heartbeat': 1},
                verbose=False
            )

            # 设置电极位置
            try:
                montage = mne.channels.make_standard_montage('standard_1020')
                epochs.set_montage(montage, on_missing='ignore')
                print(f"    ✅ 已设置标准1020电极位置")
            except Exception as e:
                print(f"    ⚠️ 设置电极位置失败: {e}")

            # 保存epochs数据
            epochs_filename = f"eeg_hep_epochs_{condition}_{timestamp}.fif"
            epochs_path = output_dir / epochs_filename
            epochs.save(epochs_path, overwrite=True, verbose=False)

            # 保存文件信息
            info_filename = f"eeg_hep_epochs_info_{condition}_{timestamp}.json"
            info_path = output_dir / info_filename
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump({
                    'condition': condition,
                    'condition_name': self.conditions[condition],
                    'n_epochs': len(all_epochs),
                    'n_channels': len(channel_names),
                    'n_files': len(successful_results),
                    'sampling_rate': sampling_rate,
                    'time_window': [times[0], times[-1]],
                    'channel_names': channel_names,
                    'file_info': file_info,
                    'epochs_file': epochs_filename,
                    'detection_method': 'eeg_cardiac_artifacts'
                }, f, indent=2, ensure_ascii=False)

            print(f"    ✅ Epochs已保存: {epochs_path}")
            return str(epochs_path)

        except Exception as e:
            print(f"    ❌ 保存MNE Epochs失败: {str(e)}")
            return None

    def _save_condition_average_hep(self, condition, successful_results, output_dir, timestamp):
        """保存条件的平均HEP数据"""
        try:
            print(f"    💾 保存 {condition} 平均HEP数据...")

            # 获取基本信息
            first_result = successful_results[0]
            times = first_result['times']
            channel_names = first_result['channel_names']
            sampling_rate = int(len(times) / (times[-1] - times[0]))

            # 计算全体平均
            all_averages = []
            for result in successful_results:
                all_averages.append(result['hep_average'])

            grand_average = np.mean(all_averages, axis=0)
            grand_sem = np.std(all_averages, axis=0) / np.sqrt(len(all_averages))

            # 创建MNE Evoked对象
            info = mne.create_info(
                ch_names=channel_names,
                sfreq=sampling_rate,
                ch_types='eeg',
                verbose=False
            )

            evoked = mne.EvokedArray(
                grand_average,
                info,
                tmin=times[0],
                comment=f'EEG_HEP_{condition}',
                verbose=False
            )

            # 设置电极位置
            try:
                montage = mne.channels.make_standard_montage('standard_1020')
                evoked.set_montage(montage, on_missing='ignore')
            except Exception as e:
                print(f"    ⚠️ 设置电极位置失败: {e}")

            # 保存平均数据
            average_filename = f"eeg_hep_average_{condition}_{timestamp}.fif"
            average_path = output_dir / average_filename
            evoked.save(average_path, overwrite=True, verbose=False)

            print(f"    ✅ 平均数据已保存: {average_path}")
            return str(average_path)

        except Exception as e:
            print(f"    ❌ 保存平均HEP失败: {str(e)}")
            return None


def test_eeg_based_hep_analyzer():
    """测试基于EEG的HEP分析器"""
    print("🧪 测试基于EEG信号的HEP分析器")
    print("🎯 特点：直接从EEG信号中检测心跳事件，不依赖ECG")

    # 配置参数
    data_directory = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"
    validation_excel = r"D:\ecgeeg\30-数据分析\5-HBA\result\unit_conversion\final_voltage_validation_data_20250602_225214.xlsx"

    # 直接使用moderate级别，避免交互式输入
    selected_level = 'moderate'

    print(f"\n选择的质量控制级别: {selected_level}")
    print("🔧 关键特点:")
    print("  ✅ 基于EEG信号的心跳伪迹检测")
    print("  ✅ 多种检测方法（幅度、梯度、功率谱、模板匹配）")
    print("  ✅ 标准化HEP时间窗口（-200ms到650ms）")
    print("  ✅ MNE标准格式保存")

    # 创建基于EEG的分析器
    analyzer = EEGBasedHEPAnalyzer(
        data_directory,
        validation_excel,
        quality_control_level=selected_level
    )

    # 运行分析
    print("\n🚀 开始基于EEG的HEP分析...")
    success = analyzer.run_complete_analysis(n_files_per_condition=40)

    if success:
        print("\n🎉 基于EEG的HEP分析完成！")

        # 显示关键统计信息
        print("\n📈 关键统计信息:")
        for condition in analyzer.conditions.keys():
            if condition in analyzer.analysis_results['hep_data']:
                hep_data = analyzer.analysis_results['hep_data'][condition]
                successful = len(hep_data['successful'])
                failed = len(hep_data['failed'])
                total = successful + failed
                if total > 0:
                    success_rate = successful / total * 100
                    print(f"  {condition}: {successful}/{total} ({success_rate:.1f}%)")

        # 检查心跳检测信息
        print("\n🫀 心跳检测验证:")
        for condition in analyzer.conditions.keys():
            if condition in analyzer.analysis_results['hep_data']:
                hep_data = analyzer.analysis_results['hep_data'][condition]
                if hep_data['successful']:
                    quality_scores = [r['detection_info']['best_quality_score'] 
                                    for r in hep_data['successful']]
                    if quality_scores:
                        avg_quality = np.mean(quality_scores)
                        print(f"  {condition}: 平均检测质量评分 {avg_quality:.1f}")

        print("\n📝 说明:")
        print("  - 本分析直接从EEG信号中检测心跳事件")
        print("  - 使用多种方法确保检测准确性")
        print("  - 数据已保存为MNE标准格式，便于后续分析")
        print("  - 时间窗口：-200ms到650ms（标准化）")

        return True
    else:
        print("\n❌ 基于EEG的HEP分析失败")
        return False


if __name__ == "__main__":
    test_eeg_based_hep_analyzer() 