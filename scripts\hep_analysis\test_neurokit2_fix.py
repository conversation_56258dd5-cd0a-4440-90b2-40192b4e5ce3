#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试NeuroKit2修复版HEP分析器

这个脚本将：
1. 使用NeuroKit2修复版分析器处理测试文件
2. 生成HEP可视化验证R波对齐
3. 与原版本进行对比
4. 验证0ms位置是否正确对齐到R波峰值

作者: HEP Analysis Team
日期: 2024-12-19
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import mne
import warnings
warnings.filterwarnings('ignore')

# 导入修复版分析器
import sys
sys.path.append('.')
import importlib.util
spec = importlib.util.spec_from_file_location("neurokit2_analyzer", "09_neurokit2_fixed_hep_analyzer.py")
neurokit2_analyzer = importlib.util.module_from_spec(spec)
spec.loader.exec_module(neurokit2_analyzer)
NeuroKit2FixedHEPAnalyzer = neurokit2_analyzer.NeuroKit2FixedHEPAnalyzer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def test_single_file_analysis():
    """测试单个文件的分析效果"""
    print("🧪 测试NeuroKit2修复版HEP分析器")
    print("="*50)
    
    # 初始化分析器
    data_dir = "D:/ecgeeg/18-eegecg手动预处理5-重参考2-双侧乳突"
    analyzer = NeuroKit2FixedHEPAnalyzer(data_dir, enable_quality_control=False)
    
    # 查找测试文件
    test_files = list(Path(data_dir).rglob("*_test_*.fif"))
    if not test_files:
        print("❌ 未找到测试文件")
        return
    
    test_file = test_files[0]
    print(f"📁 测试文件: {test_file.name}")
    
    # 构建文件信息
    file_info = {
        'file_path': str(test_file),
        'file_name': test_file.name,
        'subject_id': test_file.stem.split('_')[0],
        'condition': 'test_1'
    }
    
    # 分析文件
    print(f"\n🔍 开始分析...")
    result = analyzer.analyze_single_file(file_info)
    
    if not result['success']:
        print(f"❌ 分析失败: {result['error']}")
        return
    
    print(f"✅ 分析成功!")
    print(f"📊 分析结果:")
    print(f"   - R波数量: {result['r_peaks_count']}")
    print(f"   - HEP epochs: {result['hep_epochs'].shape}")
    print(f"   - 检测方法: {result['neurokit_method']}")
    print(f"   - 对齐验证: {result['alignment_validated']}")
    print(f"   - 质量评分: {result['quality_info']['quality']:.3f}")
    
    return result

def visualize_hep_alignment(result):
    """可视化HEP对齐效果"""
    print(f"\n📊 生成HEP对齐验证图...")
    
    hep_epochs = result['hep_epochs']
    times = result['times']
    ch_names = result['channel_names']
    
    # 选择几个代表性电极进行可视化
    representative_channels = ['Fp1', 'AF3', 'AF7', 'F1', 'F3', 'F5', 'F7']
    available_channels = [ch for ch in representative_channels if ch in ch_names]
    
    if not available_channels:
        # 如果没有找到指定电极，使用前几个
        available_channels = ch_names[:7]
    
    # 计算平均HEP波形
    mean_hep = np.mean(hep_epochs, axis=0)
    
    # 创建图形
    fig, axes = plt.subplots(len(available_channels), 1, figsize=(12, 2*len(available_channels)))
    if len(available_channels) == 1:
        axes = [axes]
    
    for i, ch_name in enumerate(available_channels):
        if ch_name in ch_names:
            ch_idx = ch_names.index(ch_name)
            
            # 绘制平均HEP波形
            axes[i].plot(times * 1000, mean_hep[ch_idx, :], 'b-', linewidth=2, alpha=0.8)
            
            # 标记0ms位置（R波对齐点）
            axes[i].axvline(0, color='red', linestyle='--', linewidth=2, alpha=0.8, label='R波 (0ms)')
            
            # 标记HEP分析窗口
            axes[i].axvspan(455, 595, alpha=0.2, color='green', label='HEP窗口 (455-595ms)')
            
            # 设置标题和标签
            axes[i].set_title(f'{ch_name} - 平均HEP波形 (n={len(hep_epochs)})', fontweight='bold')
            axes[i].set_ylabel('幅度 (μV)')
            axes[i].grid(True, alpha=0.3)
            
            if i == 0:
                axes[i].legend()
    
    axes[-1].set_xlabel('时间 (ms)')
    
    # 添加总标题
    fig.suptitle(f'NeuroKit2修复版 - HEP对齐验证\n'
                f'检测方法: {result["neurokit_method"]}, '
                f'R波数量: {result["r_peaks_count"]}, '
                f'质量评分: {result["quality_info"]["quality"]:.3f}', 
                fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    
    # 保存图像
    save_dir = Path("D:/ecgeeg/30-数据分析/5-HBA/result/neurokit2_fix_test")
    save_dir.mkdir(parents=True, exist_ok=True)
    
    save_path = save_dir / "neurokit2_hep_alignment_verification.png"
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ HEP对齐验证图已保存: {save_path}")
    
    return fig

def check_r_wave_alignment(result):
    """检查R波对齐的准确性"""
    print(f"\n🔍 检查R波对齐准确性...")
    
    times = result['times']
    hep_epochs = result['hep_epochs']
    
    # 找到0ms位置的索引
    zero_idx = np.argmin(np.abs(times))
    print(f"📊 0ms位置索引: {zero_idx}")
    print(f"📊 实际时间: {times[zero_idx]*1000:.1f}ms")
    
    # 分析0ms位置附近的信号特征
    window_size = 10  # ±10个样本点
    start_idx = max(0, zero_idx - window_size)
    end_idx = min(len(times), zero_idx + window_size + 1)
    
    # 计算所有epochs在0ms附近的平均幅度
    mean_amplitudes_around_zero = []
    for epoch in hep_epochs:
        # 使用前几个EEG通道的平均值
        eeg_mean = np.mean(epoch[:10, :], axis=0)  # 前10个EEG通道
        window_amplitudes = eeg_mean[start_idx:end_idx]
        mean_amplitudes_around_zero.append(window_amplitudes)
    
    mean_amplitudes_around_zero = np.array(mean_amplitudes_around_zero)
    overall_mean = np.mean(mean_amplitudes_around_zero, axis=0)
    
    # 检查0ms位置是否为极值
    zero_relative_idx = zero_idx - start_idx
    if 0 <= zero_relative_idx < len(overall_mean):
        zero_amplitude = overall_mean[zero_relative_idx]
        
        # 检查是否为局部极值
        is_local_max = (zero_relative_idx == 0 or overall_mean[zero_relative_idx] >= overall_mean[zero_relative_idx-1]) and \
                       (zero_relative_idx == len(overall_mean)-1 or overall_mean[zero_relative_idx] >= overall_mean[zero_relative_idx+1])
        
        is_local_min = (zero_relative_idx == 0 or overall_mean[zero_relative_idx] <= overall_mean[zero_relative_idx-1]) and \
                       (zero_relative_idx == len(overall_mean)-1 or overall_mean[zero_relative_idx] <= overall_mean[zero_relative_idx+1])
        
        print(f"📊 0ms位置幅度: {zero_amplitude:.3f} μV")
        print(f"📊 是否为局部极大值: {is_local_max}")
        print(f"📊 是否为局部极小值: {is_local_min}")
        
        if is_local_max or is_local_min:
            print(f"✅ R波对齐验证通过: 0ms位置为局部极值")
            alignment_quality = "良好"
        else:
            print(f"⚠️ R波对齐可能存在偏差: 0ms位置非局部极值")
            alignment_quality = "需要检查"
    else:
        print(f"❌ 无法验证R波对齐")
        alignment_quality = "无法验证"
    
    return {
        'alignment_quality': alignment_quality,
        'zero_amplitude': zero_amplitude if 'zero_amplitude' in locals() else None,
        'is_extremum': (is_local_max or is_local_min) if 'is_local_max' in locals() else False
    }

def main():
    """主函数"""
    print("🚀 NeuroKit2修复版HEP分析器测试")
    print("="*60)
    
    # 1. 测试单个文件分析
    result = test_single_file_analysis()
    if result is None:
        return
    
    # 2. 可视化HEP对齐效果
    visualize_hep_alignment(result)
    
    # 3. 检查R波对齐准确性
    alignment_check = check_r_wave_alignment(result)
    
    # 4. 生成总结报告
    print(f"\n📋 测试总结报告")
    print("="*40)
    print(f"✅ 文件分析: 成功")
    print(f"📊 R波检测方法: {result['neurokit_method']}")
    print(f"📊 R波数量: {result['r_peaks_count']}")
    print(f"📊 HEP epochs: {result['hep_epochs'].shape[0]}")
    print(f"📊 质量评分: {result['quality_info']['quality']:.3f}")
    print(f"📊 对齐质量: {alignment_check['alignment_quality']}")
    print(f"📊 0ms位置为极值: {alignment_check['is_extremum']}")
    
    print(f"\n🎉 测试完成!")
    print(f"📁 结果保存在: D:/ecgeeg/30-数据分析/5-HBA/result/neurokit2_fix_test/")
    
    if alignment_check['alignment_quality'] == "良好":
        print(f"✅ R波对齐问题已修复!")
        print(f"💡 建议: 可以使用此版本替代原有的07版本")
    else:
        print(f"⚠️ 对齐效果需要进一步检查")
        print(f"💡 建议: 检查信号质量或调整检测参数")

if __name__ == "__main__":
    main()
