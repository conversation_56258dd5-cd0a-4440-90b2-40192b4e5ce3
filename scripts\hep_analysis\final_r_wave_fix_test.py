#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终R波对齐修复验证

这个脚本将：
1. 使用修复后的NeuroKit2分析器
2. 验证R波是否正确对齐到负向峰值
3. 生成最终的HEP可视化
4. 确认0ms位置对齐到向下的尖峰

作者: HEP Analysis Team
日期: 2024-12-19
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import mne
import warnings
warnings.filterwarnings('ignore')

# 导入修复版分析器
import sys
import importlib.util
spec = importlib.util.spec_from_file_location("neurokit2_analyzer", "09_neurokit2_fixed_hep_analyzer.py")
neurokit2_analyzer = importlib.util.module_from_spec(spec)
spec.loader.exec_module(neurokit2_analyzer)
NeuroKit2FixedHEPAnalyzer = neurokit2_analyzer.NeuroKit2FixedHEPAnalyzer

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def test_fixed_analyzer():
    """测试修复后的分析器"""
    print("🚀 测试修复后的NeuroKit2 HEP分析器")
    print("="*60)
    
    # 初始化修复版分析器
    data_dir = "D:/ecgeeg/18-eegecg手动预处理5-重参考2-双侧乳突"
    analyzer = NeuroKit2FixedHEPAnalyzer(data_dir, enable_quality_control=False)
    
    # 查找测试文件
    test_files = list(Path(data_dir).rglob("*_test_*.fif"))
    if not test_files:
        print("❌ 未找到测试文件")
        return None
    
    test_file = test_files[0]
    print(f"📁 测试文件: {test_file.name}")
    
    # 构建文件信息
    file_info = {
        'file_path': str(test_file),
        'file_name': test_file.name,
        'subject_id': test_file.stem.split('_')[0],
        'condition': 'test_1'
    }
    
    # 分析文件
    print(f"\n🔍 开始分析...")
    result = analyzer.analyze_single_file(file_info)
    
    if not result['success']:
        print(f"❌ 分析失败: {result['error']}")
        return None
    
    print(f"✅ 分析成功!")
    print(f"📊 分析结果:")
    print(f"   - R波数量: {result['r_peaks_count']}")
    print(f"   - HEP epochs: {result['hep_epochs'].shape}")
    print(f"   - 检测方法: {result['neurokit_method']}")
    print(f"   - 对齐验证: {result['alignment_validated']}")
    print(f"   - 质量评分: {result['quality_info']['quality']:.3f}")
    
    return result

def verify_negative_r_wave_alignment(result):
    """验证负向R波对齐"""
    print(f"\n🔍 验证负向R波对齐...")
    
    hep_epochs = result['hep_epochs']
    times = result['times']
    
    # 找到0ms位置
    zero_idx = np.argmin(np.abs(times))
    print(f"📊 0ms位置索引: {zero_idx}")
    print(f"📊 实际时间: {times[zero_idx]*1000:.1f}ms")
    
    # 分析0ms位置附近的信号特征
    window_size = 5  # ±5个样本点
    start_idx = max(0, zero_idx - window_size)
    end_idx = min(len(times), zero_idx + window_size + 1)
    
    # 计算前几个EEG通道在0ms附近的平均幅度
    representative_channels = slice(0, 10)  # 前10个EEG通道
    
    zero_amplitudes = []
    for epoch in hep_epochs:
        eeg_channels = epoch[representative_channels, :]
        mean_amplitude_at_zero = np.mean(eeg_channels[:, zero_idx])
        zero_amplitudes.append(mean_amplitude_at_zero)
    
    mean_amplitude_at_zero = np.mean(zero_amplitudes)
    
    # 检查0ms位置是否为局部极值
    window_amplitudes = []
    for epoch in hep_epochs:
        eeg_channels = epoch[representative_channels, :]
        window_mean = np.mean(eeg_channels[:, start_idx:end_idx], axis=0)
        window_amplitudes.append(window_mean)
    
    overall_window_mean = np.mean(window_amplitudes, axis=0)
    zero_relative_idx = zero_idx - start_idx
    
    if 0 <= zero_relative_idx < len(overall_window_mean):
        zero_value = overall_window_mean[zero_relative_idx]
        
        # 检查是否为局部极值
        is_local_min = True
        is_local_max = True
        
        for i in range(len(overall_window_mean)):
            if i != zero_relative_idx:
                if overall_window_mean[i] < zero_value:
                    is_local_min = False
                if overall_window_mean[i] > zero_value:
                    is_local_max = False
        
        print(f"📊 0ms位置平均幅度: {mean_amplitude_at_zero:.3f} μV")
        print(f"📊 是否为局部最小值: {is_local_min}")
        print(f"📊 是否为局部最大值: {is_local_max}")
        
        if is_local_min:
            print(f"✅ 验证通过: 0ms位置为局部最小值（负向峰值）")
            alignment_status = "正确对齐到负向峰值"
        elif is_local_max:
            print(f"⚠️ 注意: 0ms位置为局部最大值（正向峰值）")
            alignment_status = "对齐到正向峰值"
        else:
            print(f"❌ 对齐问题: 0ms位置非局部极值")
            alignment_status = "对齐有误"
    else:
        print(f"❌ 无法验证对齐")
        alignment_status = "无法验证"
    
    return {
        'alignment_status': alignment_status,
        'mean_amplitude_at_zero': mean_amplitude_at_zero,
        'is_local_min': is_local_min if 'is_local_min' in locals() else False,
        'is_local_max': is_local_max if 'is_local_max' in locals() else False
    }

def create_final_visualization(result, alignment_check, save_dir):
    """创建最终验证可视化"""
    print(f"\n📊 生成最终验证可视化...")
    
    hep_epochs = result['hep_epochs']
    times = result['times']
    ch_names = result['channel_names']
    
    # 选择代表性电极
    representative_channels = ['Fp1', 'AF3', 'AF7', 'F1', 'F3', 'F5', 'F7']
    available_channels = [ch for ch in representative_channels if ch in ch_names]
    
    if not available_channels:
        available_channels = ch_names[:7]
    
    # 计算平均HEP波形
    mean_hep = np.mean(hep_epochs, axis=0)
    
    # 创建图形
    fig, axes = plt.subplots(len(available_channels), 1, figsize=(14, 2.5*len(available_channels)))
    if len(available_channels) == 1:
        axes = [axes]
    
    for i, ch_name in enumerate(available_channels):
        if ch_name in ch_names:
            ch_idx = ch_names.index(ch_name)
            
            # 绘制平均HEP波形
            axes[i].plot(times * 1000, mean_hep[ch_idx, :], 'b-', linewidth=2.5, alpha=0.9)
            
            # 标记0ms位置（R波对齐点）
            axes[i].axvline(0, color='red', linestyle='--', linewidth=3, alpha=0.8, 
                           label='R波对齐点 (0ms)')
            
            # 标记HEP分析窗口
            axes[i].axvspan(455, 595, alpha=0.15, color='green', 
                           label='HEP分析窗口 (455-595ms)')
            
            # 突出显示0ms位置的点
            zero_idx = np.argmin(np.abs(times))
            zero_amplitude = mean_hep[ch_idx, zero_idx]
            axes[i].scatter([0], [zero_amplitude], c='red', s=100, marker='o', 
                           zorder=10, alpha=0.8)
            
            # 设置标题和标签
            axes[i].set_title(f'{ch_name} - 修复后HEP波形 (n={len(hep_epochs)})', 
                             fontweight='bold', fontsize=11)
            axes[i].set_ylabel('幅度 (μV)')
            axes[i].grid(True, alpha=0.3)
            
            # 添加对齐状态注释
            if i == 0:
                axes[i].text(0.02, 0.95, f'对齐状态: {alignment_check["alignment_status"]}', 
                            transform=axes[i].transAxes, fontsize=10, 
                            bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                            verticalalignment='top')
                axes[i].legend(loc='upper right')
    
    axes[-1].set_xlabel('时间 (ms)')
    
    # 添加总标题
    status_color = 'green' if 'negative' in alignment_check['alignment_status'].lower() else 'orange'
    fig.suptitle(f'NeuroKit2修复版 - 最终R波对齐验证\n'
                f'检测方法: {result["neurokit_method"]}, '
                f'R波数量: {result["r_peaks_count"]}, '
                f'质量评分: {result["quality_info"]["quality"]:.3f}\n'
                f'对齐状态: {alignment_check["alignment_status"]}', 
                fontsize=14, fontweight='bold', color=status_color)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.85)
    
    # 保存图像
    save_path = save_dir / "final_r_wave_alignment_verification.png"
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"✅ 最终验证图已保存: {save_path}")
    
    return fig

def main():
    """主函数"""
    print("🎯 最终R波对齐修复验证")
    print("="*60)
    
    # 1. 测试修复后的分析器
    result = test_fixed_analyzer()
    if result is None:
        return
    
    # 2. 验证负向R波对齐
    alignment_check = verify_negative_r_wave_alignment(result)
    
    # 3. 生成最终可视化
    save_dir = Path("D:/ecgeeg/30-数据分析/5-HBA/result/final_r_wave_fix")
    save_dir.mkdir(parents=True, exist_ok=True)
    
    create_final_visualization(result, alignment_check, save_dir)
    
    # 4. 生成最终报告
    print(f"\n📋 最终验证报告")
    print("="*50)
    print(f"✅ 文件分析: 成功")
    print(f"📊 R波检测方法: {result['neurokit_method']}")
    print(f"📊 R波数量: {result['r_peaks_count']}")
    print(f"📊 HEP epochs: {result['hep_epochs'].shape[0]}")
    print(f"📊 质量评分: {result['quality_info']['quality']:.3f}")
    print(f"📊 对齐状态: {alignment_check['alignment_status']}")
    print(f"📊 0ms为局部最小值: {alignment_check['is_local_min']}")
    print(f"📊 0ms为局部最大值: {alignment_check['is_local_max']}")
    
    print(f"\n🎉 验证完成!")
    print(f"📁 结果保存在: {save_dir}")
    
    # 最终结论
    if alignment_check['is_local_min']:
        print(f"\n✅ 🎯 修复成功!")
        print(f"   R波现在正确对齐到向下的尖峰（负向峰值）")
        print(f"   0ms位置为局部最小值，符合ECG11通道的负向R波特征")
        print(f"   可以使用此修复版本进行HEP分析")
    elif alignment_check['is_local_max']:
        print(f"\n⚠️ 仍需调整:")
        print(f"   R波对齐到了向上的尖峰（正向峰值）")
        print(f"   与ECG11通道的负向R波特征不符")
        print(f"   需要进一步调整检测算法")
    else:
        print(f"\n❌ 对齐问题:")
        print(f"   0ms位置非局部极值")
        print(f"   R波对齐可能存在偏差")

if __name__ == "__main__":
    main()
