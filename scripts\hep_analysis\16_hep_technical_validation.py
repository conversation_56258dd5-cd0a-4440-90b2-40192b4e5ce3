#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HEP技术验证分析器 v1.0
====================

针对审查过程中发现的技术问题进行深度分析和验证：
1. 滤波导致的波形形变和偏移误差
2. R波极性问题（朝上vs朝下）
3. 心脑传导延迟分析
4. HEP时间零点定位准确性

目标：
- 分析当前方案的技术实现
- 识别潜在的技术问题
- 提供改进建议和验证方案
- 确保与文献标准的一致性

作者：研究团队
日期：2025年6月
版本：1.0 - 技术验证版本
"""

import numpy as np
import mne
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
from scipy import signal
from scipy.signal import filtfilt, group_delay, correlate
import pandas as pd
import random
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class HEPTechnicalValidator:
    """HEP技术验证分析器"""

    def __init__(self, data_dir):
        """
        初始化技术验证分析器

        Parameters:
        -----------
        data_dir : str
            数据目录路径
        """
        self.data_dir = Path(data_dir)
        
        # 分析参数
        self.params = {
            'sampling_rate': 500,
            'ecg_filter_current': {'l_freq': 5.0, 'h_freq': 15.0},
            'eeg_filter_current': {'l_freq': 0.5, 'h_freq': 40.0},
            'time_window': (-0.2, 0.65),
            'search_window': 0.05  # ±50ms搜索窗口
        }
        
        # 结果保存路径
        self.results_save_dir = Path("../../result/hep_technical_validation")
        self.results_save_dir.mkdir(parents=True, exist_ok=True)
        
        print("="*80)
        print("HEP技术验证分析器")
        print("="*80)
        print("分析技术问题：滤波延迟、R波极性、心脑延迟、时间零点")

    def load_test_file(self):
        """加载一个测试文件"""
        fif_files = list(self.data_dir.glob("*.fif"))
        if not fif_files:
            print("❌ 未找到.fif文件")
            return None
        
        test_file = random.choice(fif_files)
        print(f"📁 选择测试文件: {test_file.name}")
        
        try:
            raw = mne.io.read_raw_fif(test_file, preload=True, verbose=False)
            print(f"📥 加载成功: {raw.info['nchan']}通道, {raw.times[-1]:.1f}秒")
            return raw
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            return None

    def analyze_filter_effects(self, raw):
        """分析滤波效果和延迟"""
        print("\n🔍 分析1：滤波效果和延迟")
        
        # 获取原始ECG信号
        ecg_data = raw.get_data()[61:, :]
        ecg11_signal = ecg_data[10, :]  # ECG11通道
        sampling_rate = raw.info['sfreq']
        
        # 当前方法：IIR滤波
        sos = signal.butter(4, [self.params['ecg_filter_current']['l_freq'], 
                               self.params['ecg_filter_current']['h_freq']], 
                           btype='band', fs=sampling_rate, output='sos')
        ecg_iir_filtered = signal.sosfilt(sos, ecg11_signal)
        
        # 改进方法：零相位滤波
        b, a = signal.butter(4, [self.params['ecg_filter_current']['l_freq'], 
                                self.params['ecg_filter_current']['h_freq']], 
                            btype='band', fs=sampling_rate)
        ecg_zero_phase = filtfilt(b, a, ecg11_signal)
        
        # 计算群延迟
        w, gd = group_delay((b, a), fs=sampling_rate)
        mean_delay = np.mean(gd)
        
        # 可视化对比
        fig, axes = plt.subplots(3, 1, figsize=(15, 10))
        
        # 选择一个时间段进行对比
        start_time = 10  # 10秒开始
        duration = 5     # 5秒长度
        start_idx = int(start_time * sampling_rate)
        end_idx = int((start_time + duration) * sampling_rate)
        time_axis = np.arange(start_idx, end_idx) / sampling_rate
        
        # 原始信号
        axes[0].plot(time_axis, ecg11_signal[start_idx:end_idx], 'b-', linewidth=1)
        axes[0].set_title('原始ECG信号')
        axes[0].set_ylabel('幅度')
        axes[0].grid(True, alpha=0.3)
        
        # IIR滤波 vs 零相位滤波
        axes[1].plot(time_axis, ecg_iir_filtered[start_idx:end_idx], 'r-', linewidth=1, label='IIR滤波（当前方法）')
        axes[1].plot(time_axis, ecg_zero_phase[start_idx:end_idx], 'g-', linewidth=1, label='零相位滤波（改进方法）')
        axes[1].set_title('滤波方法对比')
        axes[1].set_ylabel('幅度')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        # 差异分析
        difference = ecg_iir_filtered[start_idx:end_idx] - ecg_zero_phase[start_idx:end_idx]
        axes[2].plot(time_axis, difference, 'm-', linewidth=1)
        axes[2].set_title(f'滤波差异 (平均群延迟: {mean_delay:.1f}样本点 = {mean_delay/sampling_rate*1000:.1f}ms)')
        axes[2].set_xlabel('时间 (s)')
        axes[2].set_ylabel('差异幅度')
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        plot_path = self.results_save_dir / f"filter_analysis_{timestamp}.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"  📊 平均群延迟: {mean_delay:.1f}样本点 ({mean_delay/sampling_rate*1000:.1f}ms)")
        print(f"  📈 滤波对比图已保存: {plot_path}")
        
        return {
            'original': ecg11_signal,
            'iir_filtered': ecg_iir_filtered,
            'zero_phase': ecg_zero_phase,
            'group_delay_samples': mean_delay,
            'group_delay_ms': mean_delay/sampling_rate*1000
        }

    def analyze_r_wave_polarity(self, raw):
        """分析R波极性问题"""
        print("\n🔍 分析2：R波极性分析")
        
        # 获取所有ECG通道
        ecg_data = raw.get_data()[61:, :]
        sampling_rate = raw.info['sfreq']
        
        # 分析前10个ECG通道的极性
        polarity_results = {}
        
        for i in range(min(10, ecg_data.shape[0])):
            ecg_signal = ecg_data[i, :]
            
            # 简单滤波
            sos = signal.butter(4, [5, 15], btype='band', fs=sampling_rate, output='sos')
            ecg_filtered = signal.sosfilt(sos, ecg_signal)
            
            # 检测峰值
            ecg_squared = ecg_filtered ** 2
            window_size = int(0.08 * sampling_rate)
            ecg_smoothed = np.convolve(ecg_squared, np.ones(window_size)/window_size, mode='same')
            
            threshold = np.mean(ecg_smoothed) + 2 * np.std(ecg_smoothed)
            rough_peaks, _ = signal.find_peaks(ecg_smoothed, height=threshold, distance=int(0.3 * sampling_rate))
            
            # 分析极性
            positive_peaks = 0
            negative_peaks = 0
            
            for peak in rough_peaks[:20]:  # 分析前20个峰值
                search_start = max(0, peak - int(0.05 * sampling_rate))
                search_end = min(len(ecg_signal), peak + int(0.05 * sampling_rate))
                segment = ecg_signal[search_start:search_end]
                
                max_val = np.max(segment)
                min_val = np.min(segment)
                
                if abs(max_val) > abs(min_val):
                    positive_peaks += 1
                else:
                    negative_peaks += 1
            
            polarity_results[f'ECG{i+1}'] = {
                'positive_peaks': positive_peaks,
                'negative_peaks': negative_peaks,
                'dominant_polarity': 'positive' if positive_peaks > negative_peaks else 'negative',
                'confidence': max(positive_peaks, negative_peaks) / (positive_peaks + negative_peaks) if (positive_peaks + negative_peaks) > 0 else 0
            }
        
        # 可视化极性分析
        fig, axes = plt.subplots(2, 5, figsize=(20, 8))
        axes = axes.flatten()
        
        for i, (channel, result) in enumerate(polarity_results.items()):
            if i >= 10:
                break
                
            # 显示一段ECG信号
            start_idx = int(10 * sampling_rate)
            end_idx = int(15 * sampling_rate)
            time_axis = np.arange(start_idx, end_idx) / sampling_rate
            
            axes[i].plot(time_axis, ecg_data[i, start_idx:end_idx], 'b-', linewidth=1)
            axes[i].set_title(f'{channel}: {result["dominant_polarity"]}\n(置信度: {result["confidence"]:.2f})')
            axes[i].set_xlabel('时间 (s)')
            axes[i].set_ylabel('幅度')
            axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        plot_path = self.results_save_dir / f"r_wave_polarity_{timestamp}.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"  📊 ECG通道极性分析:")
        for channel, result in polarity_results.items():
            print(f"    {channel}: {result['dominant_polarity']} (置信度: {result['confidence']:.2f})")
        
        print(f"  📈 极性分析图已保存: {plot_path}")
        
        return polarity_results

    def analyze_cardiac_brain_delay(self, raw):
        """分析心脑传导延迟"""
        print("\n🔍 分析3：心脑传导延迟分析")
        
        # 获取ECG和EEG数据
        ecg_data = raw.get_data()[61:, :]
        eeg_data = raw.get_data()[:61, :]
        sampling_rate = raw.info['sfreq']
        
        # ECG R波检测
        ecg11_signal = ecg_data[10, :]
        sos = signal.butter(4, [5, 15], btype='band', fs=sampling_rate, output='sos')
        ecg_filtered = signal.sosfilt(sos, ecg11_signal)
        
        ecg_squared = ecg_filtered ** 2
        window_size = int(0.08 * sampling_rate)
        ecg_smoothed = np.convolve(ecg_squared, np.ones(window_size)/window_size, mode='same')
        
        threshold = np.mean(ecg_smoothed) + 2 * np.std(ecg_smoothed)
        ecg_peaks, _ = signal.find_peaks(ecg_smoothed, height=threshold, distance=int(0.3 * sampling_rate))
        
        # EEG心电伪迹检测（前额叶电极）
        cardiac_channels = ['Fp1', 'Fp2', 'F7', 'F8']
        eeg_cardiac_peaks = {}
        
        for channel in cardiac_channels:
            if channel in raw.ch_names[:61]:
                ch_idx = raw.ch_names.index(channel)
                eeg_signal = eeg_data[ch_idx, :]
                
                # EEG心电伪迹检测
                sos_eeg = signal.butter(4, [1, 20], btype='band', fs=sampling_rate, output='sos')
                eeg_filtered = signal.sosfilt(sos_eeg, eeg_signal)
                
                # 检测心电伪迹
                abs_signal = np.abs(eeg_filtered)
                gradient = np.abs(np.gradient(eeg_filtered))
                combined_signal = abs_signal + 0.5 * gradient
                
                threshold_eeg = np.mean(combined_signal) + 2 * np.std(combined_signal)
                eeg_peaks, _ = signal.find_peaks(combined_signal, height=threshold_eeg, distance=int(0.4 * sampling_rate))
                
                eeg_cardiac_peaks[channel] = eeg_peaks
        
        # 计算延迟
        delays = {}
        for channel, eeg_peaks in eeg_cardiac_peaks.items():
            if len(eeg_peaks) > 10 and len(ecg_peaks) > 10:
                # 寻找最佳匹配
                min_delay = float('inf')
                best_delay = 0
                
                for delay in range(-100, 101, 5):  # -200ms到+200ms，步长10ms
                    shifted_eeg_peaks = eeg_peaks + delay
                    
                    # 计算匹配度
                    matches = 0
                    for ecg_peak in ecg_peaks[:50]:  # 使用前50个峰值
                        closest_eeg = np.argmin(np.abs(shifted_eeg_peaks - ecg_peak))
                        if np.abs(shifted_eeg_peaks[closest_eeg] - ecg_peak) < 0.1 * sampling_rate:  # 100ms容差
                            matches += 1
                    
                    if matches > 0:
                        error = len(ecg_peaks[:50]) - matches
                        if error < min_delay:
                            min_delay = error
                            best_delay = delay
                
                delays[channel] = {
                    'delay_samples': best_delay,
                    'delay_ms': best_delay / sampling_rate * 1000,
                    'match_quality': 1 - min_delay / len(ecg_peaks[:50])
                }
        
        print(f"  📊 心脑传导延迟分析:")
        for channel, delay_info in delays.items():
            print(f"    {channel}: {delay_info['delay_ms']:.1f}ms (匹配质量: {delay_info['match_quality']:.2f})")
        
        return delays

    def run_technical_validation(self):
        """运行完整的技术验证"""
        print("开始HEP技术验证分析...")
        
        # 加载测试文件
        raw = self.load_test_file()
        if raw is None:
            return False
        
        # 分析1：滤波效果
        filter_results = self.analyze_filter_effects(raw)
        
        # 分析2：R波极性
        polarity_results = self.analyze_r_wave_polarity(raw)
        
        # 分析3：心脑延迟
        delay_results = self.analyze_cardiac_brain_delay(raw)
        
        # 生成技术验证报告
        self.generate_validation_report(filter_results, polarity_results, delay_results)
        
        print("\n" + "="*80)
        print("✅ HEP技术验证分析完成！")
        print("="*80)
        
        return True

    def generate_validation_report(self, filter_results, polarity_results, delay_results):
        """生成技术验证报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = self.results_save_dir / f"hep_technical_validation_report_{timestamp}.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# HEP技术验证分析报告\n\n")
            f.write(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 🔍 滤波效果分析\n\n")
            f.write(f"- **群延迟**: {filter_results['group_delay_ms']:.1f}ms\n")
            f.write(f"- **影响**: IIR滤波器引入相位延迟，可能影响R波时间精度\n")
            f.write(f"- **建议**: 考虑使用零相位滤波（filtfilt）减少延迟\n\n")
            
            f.write("## 🔍 R波极性分析\n\n")
            for channel, result in polarity_results.items():
                f.write(f"- **{channel}**: {result['dominant_polarity']} (置信度: {result['confidence']:.2f})\n")
            f.write(f"- **发现**: 不同ECG通道显示不同的R波极性\n")
            f.write(f"- **建议**: 需要标准化R波检测方法，确保极性一致性\n\n")
            
            f.write("## 🔍 心脑传导延迟分析\n\n")
            if delay_results:
                for channel, delay_info in delay_results.items():
                    f.write(f"- **{channel}**: {delay_info['delay_ms']:.1f}ms (匹配质量: {delay_info['match_quality']:.2f})\n")
                f.write(f"- **发现**: 存在心脑传导延迟，需要在HEP分析中考虑\n")
                f.write(f"- **建议**: 应用延迟补偿优化HEP时间零点\n\n")
            else:
                f.write("- **状态**: 未检测到明显的心脑传导延迟\n")
                f.write("- **可能原因**: EEG心电伪迹不明显或检测方法需要改进\n\n")
            
            f.write("## 💡 技术改进建议\n\n")
            f.write("1. **滤波优化**: 使用零相位滤波减少时间延迟\n")
            f.write("2. **极性标准化**: 建立统一的R波极性检测标准\n")
            f.write("3. **延迟补偿**: 考虑心脑传导延迟进行时间校正\n")
            f.write("4. **精度验证**: 与标准ECG设备进行对比验证\n\n")
        
        print(f"✅ 技术验证报告已保存: {report_path}")


if __name__ == "__main__":
    data_directory = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"
    
    print("HEP技术验证分析器")
    print("="*50)
    print("分析滤波延迟、R波极性、心脑延迟等技术问题")
    print("="*50)
    
    validator = HEPTechnicalValidator(data_directory)
    validator.run_technical_validation()
