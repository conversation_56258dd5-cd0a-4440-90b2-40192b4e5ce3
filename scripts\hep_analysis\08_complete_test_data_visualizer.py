#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试数据HEP可视化器

基于06_enhanced_hep_conditions_visualizer.py的设置，
对完整两阶段测试提取的HEP数据进行可视化，
保证数据显示的完整性，不修改原始脚本参数。

作者: HEP分析团队
日期: 2024年12月
版本: 1.0 - 基于完整测试数据
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json
import pickle
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体（复制自06脚本）
font_path = r"C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKaiMono-Regular.ttf"
try:
    from matplotlib.font_manager import FontProperties
    chinese_font = FontProperties(fname=font_path)
    plt.rcParams['font.family'] = chinese_font.get_name()
except:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class CompleteTestDataVisualizer:
    """完整测试数据HEP可视化器"""
    
    def __init__(self, test_data_dir):
        """
        初始化可视化器
        
        Parameters:
        -----------
        test_data_dir : str
            完整测试数据目录路径
        """
        self.test_data_dir = Path(test_data_dir)
        
        # 定义实验条件（复制自06脚本）
        self.conditions = {
            'prac': '练习状态',
            'rest1': '静息状态1',
            'rest2': '静息状态2',
            'rest3': '静息状态3',
            'test1': '测试状态1',
            'test2': '测试状态2',
            'test3': '测试状态3'
        }
        
        # 定义电极组（复制自06脚本）
        self.electrode_groups = {
            '前部电极': [0, 1, 2, 3, 4, 5],
            '中部电极': [6, 7, 8, 9, 10, 11],
            '后部电极': [12, 13, 14, 15, 16, 17],
            '左侧电极': [18, 19, 20, 21, 22, 23],
            '右侧电极': [24, 25, 26, 27, 28, 29],
            '中线电极': [30, 31, 32, 33, 34, 35]
        }
        
        # 可视化参数（复制自06脚本，确保完整性）
        self.viz_params = {
            'figsize': (20, 12),     # 图像大小
            'subplot_ratio': (2, 1), # 子图宽高比 2:1
            'time_window': (-200, 650),  # 显示时间窗口(ms)
            'y_range': 'auto',           # Y轴范围(μV) - 设为auto进行动态调整
            'hep_window': (200, 600),    # HEP成分窗口(ms)
            'colors': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728',
                      '#9467bd', '#8c564b', '#e377c2'],  # 7种颜色
            'alpha': 0.3,
            'linewidth': 2.0,
            'figure_dpi': 300
        }
        
        # 存储加载的数据
        self.loaded_data = {
            'zero_phase': {},
            'matlab_style': {}
        }
        
        print("🧠 完整测试数据HEP可视化器初始化完成")
        print(f"📁 测试数据目录: {self.test_data_dir}")
        print(f"📊 实验条件: {list(self.conditions.keys())}")
        print(f"🎯 电极组: {list(self.electrode_groups.keys())}")
    
    def load_test_data(self):
        """加载完整测试数据"""
        print("\n📁 加载完整测试数据...")
        
        # 查找最新的测试数据文件
        phase1_dir = self.test_data_dir / "phase1_hep_extraction"
        
        if not phase1_dir.exists():
            print(f"❌ 未找到第一阶段数据目录: {phase1_dir}")
            return False
        
        # 查找pickle数据文件
        pickle_files = list(phase1_dir.glob("extracted_hep_data_*.pkl"))
        
        if not pickle_files:
            print(f"❌ 未找到HEP数据文件")
            return False
        
        # 使用最新的数据文件
        latest_file = max(pickle_files, key=lambda x: x.stat().st_mtime)
        print(f"📄 加载数据文件: {latest_file.name}")
        
        try:
            with open(latest_file, 'rb') as f:
                extracted_data = pickle.load(f)
            
            self.loaded_data = extracted_data
            
            # 统计数据
            total_files = 0
            for method in ['zero_phase', 'matlab_style']:
                method_total = 0
                for condition in self.loaded_data[method]:
                    condition_count = len(self.loaded_data[method][condition])
                    method_total += condition_count
                    if condition_count > 0:
                        print(f"  ✅ {method} - {condition}: {condition_count} 个文件")
                
                total_files += method_total
                print(f"  📊 {method} 总计: {method_total} 个文件")
            
            print(f"✅ 数据加载完成，总计 {total_files} 个数据集")
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            return False
    
    def analyze_condition_data(self, condition, method='zero_phase'):
        """分析特定条件和方法的HEP数据"""
        print(f"\n🔍 分析条件: {condition} ({self.conditions.get(condition, condition)}) - {method}")
        
        if condition not in self.loaded_data[method]:
            print(f"⚠️ 未找到 {condition} 条件的 {method} 数据")
            return None
        
        condition_data_list = self.loaded_data[method][condition]
        
        if len(condition_data_list) == 0:
            print(f"⚠️ {condition} 条件的 {method} 数据为空")
            return None
        
        condition_results = {
            'total_epochs': 0,
            'files_processed': 0,
            'group_hep_data': {group: [] for group in self.electrode_groups.keys()},
            'times': None
        }
        
        # 处理每个文件的数据
        for i, result in enumerate(condition_data_list):
            if not result['success']:
                print(f"  ⚠️ 文件 {i+1}: 数据提取失败")
                continue
            
            data = result['data']
            hep_epochs = data['hep_epochs']
            times = data['times']
            
            print(f"  📄 文件 {i+1}: {data['file_info']['file_name']}")
            print(f"    💓 有效心跳: {data['valid_heartbeats']}")
            print(f"    📊 HEP形状: {hep_epochs.shape}")
            
            # 保存时间信息
            if condition_results['times'] is None:
                condition_results['times'] = times
            
            # 计算平均HEP（跨所有时期）
            hep_average = np.mean(hep_epochs, axis=0)  # 形状: (n_channels, n_times)
            
            # 按电极组分组计算平均
            for group_name, electrode_indices in self.electrode_groups.items():
                # 检查索引是否在有效范围内
                valid_indices = [idx for idx in electrode_indices if idx < hep_average.shape[0]]
                
                if len(valid_indices) > 0:
                    # 计算该组的平均HEP
                    group_hep = np.mean(hep_average[valid_indices, :], axis=0)
                    condition_results['group_hep_data'][group_name].append(group_hep)
            
            condition_results['total_epochs'] += len(hep_epochs)
            condition_results['files_processed'] += 1
        
        # 计算最终的组平均
        for group_name in self.electrode_groups.keys():
            if len(condition_results['group_hep_data'][group_name]) > 0:
                condition_results['group_hep_data'][group_name] = \
                    np.mean(condition_results['group_hep_data'][group_name], axis=0)
                print(f"  📊 {group_name}: 平均完成")
            else:
                condition_results['group_hep_data'][group_name] = None
                print(f"  ⚠️ {group_name}: 无数据")
        
        print(f"✅ {condition} 条件分析完成: {condition_results['files_processed']} 文件, {condition_results['total_epochs']} 时期")
        
        return condition_results
    
    def create_method_comparison_visualization(self):
        """创建方法对比可视化（基于06脚本的设置）"""
        print("\n🎨 开始创建方法对比HEP可视化...")
        
        # 为每种方法分析数据
        methods_results = {}
        
        for method in ['zero_phase', 'matlab_style']:
            print(f"\n📊 分析 {method} 方法...")
            method_results = {}
            times = None
            
            # 分析每个条件
            for condition in self.conditions.keys():
                result = self.analyze_condition_data(condition, method)
                if result is not None:
                    method_results[condition] = result
                    if times is None:
                        times = result['times']
            
            if method_results:
                methods_results[method] = {
                    'condition_results': method_results,
                    'times': times
                }
        
        if not methods_results:
            print("❌ 没有成功分析的数据")
            return
        
        # 创建可视化
        self._create_comprehensive_comparison_plot(methods_results)
        
        print("✅ 方法对比HEP可视化创建完成")
    
    def _create_comprehensive_comparison_plot(self, methods_results):
        """创建综合对比图表（基于06脚本的布局）"""
        # 创建输出目录
        output_dir = Path("../../result/complete_test_visualization")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 为每种方法创建单独的图表
        for method, method_data in methods_results.items():
            self._create_single_method_plot(method, method_data, output_dir)
        
        # 创建方法对比图
        if len(methods_results) == 2:
            self._create_methods_comparison_plot(methods_results, output_dir)
    
    def _create_single_method_plot(self, method, method_data, output_dir):
        """为单个方法创建图表（完全基于06脚本的设置）"""
        condition_results = method_data['condition_results']
        times = method_data['times']
        
        # 转换时间为毫秒
        times_ms = times * 1000
        
        # 计算全局Y轴范围（复制自06脚本的逻辑）
        all_data_values = []
        for condition_data in condition_results.values():
            for group_hep in condition_data['group_hep_data'].values():
                if group_hep is not None:
                    all_data_values.extend(group_hep * 1e6)  # 转换为μV
        
        if len(all_data_values) > 0:
            y_min = np.min(all_data_values)
            y_max = np.max(all_data_values)
            y_range = y_max - y_min
            y_margin = y_range * 0.1  # 10%边距
            global_y_range = (y_min - y_margin, y_max + y_margin)
        else:
            global_y_range = (-50, 50)  # 默认范围
        
        print(f"🎯 {method} 动态Y轴范围: {global_y_range[0]:.1f} 到 {global_y_range[1]:.1f} μV")
        
        # 创建图表（完全复制06脚本的布局）
        fig, axes = plt.subplots(2, 3, figsize=self.viz_params['figsize'])
        method_title = "零相位滤波方法" if method == 'zero_phase' else "MATLAB风格方法"
        fig.suptitle(f'HEP（心跳诱发电位）实验条件对比 - 各脑区分析\n{method_title} - 基于完整测试数据',
                    fontsize=20, fontweight='bold', y=0.95)
        
        axes = axes.flatten()
        
        # 为每个电极组创建子图（完全复制06脚本的逻辑）
        for i, (group_name, electrode_indices) in enumerate(self.electrode_groups.items()):
            if i >= len(axes):
                break
            
            ax = axes[i]
            
            # 绘制每个条件的HEP波形
            condition_count = 0
            for condition, condition_name in self.conditions.items():
                if condition in condition_results:
                    condition_data = condition_results[condition]
                    group_hep = condition_data['group_hep_data'][group_name]
                    
                    if group_hep is not None:
                        color = self.viz_params['colors'][condition_count % len(self.viz_params['colors'])]
                        ax.plot(times_ms, group_hep * 1e6,  # 转换为μV
                               label=f'{condition_name} (n={condition_data["total_epochs"]})',
                               color=color, linewidth=self.viz_params['linewidth'],
                               alpha=0.8)
                        condition_count += 1
            
            # 标记重要时间点（完全复制06脚本）
            ax.axvline(x=0, color='red', linestyle='--', alpha=0.8, linewidth=1.5, label='R波')
            ax.axvspan(self.viz_params['hep_window'][0], self.viz_params['hep_window'][1],
                      alpha=self.viz_params['alpha'], color='green', label='HEP窗口')
            
            # 设置子图属性（完全复制06脚本）
            electrode_str = f"通道 {electrode_indices[0]}-{electrode_indices[-1]}"
            ax.set_title(f'{group_name}\n{electrode_str}',
                        fontsize=14, fontweight='bold')
            ax.set_xlabel('时间 (ms)', fontsize=12)
            ax.set_ylabel('幅度 (μV)', fontsize=12)
            ax.grid(True, alpha=0.3)
            ax.set_xlim(self.viz_params['time_window'])
            ax.set_ylim(global_y_range)  # 使用动态计算的Y轴范围
            
            # 只在第一个子图显示图例
            if i == 0:
                ax.legend(loc='upper right', fontsize=10, framealpha=0.9)
        
        # 隐藏多余的子图
        for i in range(len(self.electrode_groups), len(axes)):
            axes[i].set_visible(False)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图像
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        plot_path = output_dir / f"complete_test_hep_conditions_{method}_{timestamp}.png"
        plt.savefig(plot_path, dpi=self.viz_params['figure_dpi'], bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"📊 {method} 条件对比图已保存: {plot_path}")
    
    def _create_methods_comparison_plot(self, methods_results, output_dir):
        """创建两种方法的直接对比图"""
        print("\n🔍 创建方法对比图...")
        
        # 找到两种方法都有的条件
        zero_phase_conditions = set(methods_results['zero_phase']['condition_results'].keys())
        matlab_style_conditions = set(methods_results['matlab_style']['condition_results'].keys())
        common_conditions = zero_phase_conditions & matlab_style_conditions
        
        if not common_conditions:
            print("⚠️ 两种方法没有共同的条件数据")
            return
        
        print(f"📊 共同条件: {list(common_conditions)}")
        
        # 为每个共同条件创建对比图
        for condition in common_conditions:
            self._create_single_condition_comparison(condition, methods_results, output_dir)
    
    def _create_single_condition_comparison(self, condition, methods_results, output_dir):
        """为单个条件创建方法对比图"""
        zp_data = methods_results['zero_phase']['condition_results'][condition]
        ms_data = methods_results['matlab_style']['condition_results'][condition]
        times = methods_results['zero_phase']['times']
        times_ms = times * 1000
        
        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=self.viz_params['figsize'])
        condition_name = self.conditions.get(condition, condition)
        fig.suptitle(f'方法对比：{condition_name} - 零相位滤波 vs MATLAB风格\n基于完整测试数据',
                    fontsize=20, fontweight='bold', y=0.95)
        
        axes = axes.flatten()
        
        # 为每个电极组创建对比子图
        for i, (group_name, electrode_indices) in enumerate(self.electrode_groups.items()):
            if i >= len(axes):
                break
            
            ax = axes[i]
            
            # 绘制两种方法的波形
            zp_hep = zp_data['group_hep_data'][group_name]
            ms_hep = ms_data['group_hep_data'][group_name]
            
            if zp_hep is not None:
                ax.plot(times_ms, zp_hep * 1e6, 
                       label=f'零相位滤波 (n={zp_data["total_epochs"]})',
                       color='blue', linewidth=self.viz_params['linewidth'], alpha=0.8)
            
            if ms_hep is not None:
                ax.plot(times_ms, ms_hep * 1e6,
                       label=f'MATLAB风格 (n={ms_data["total_epochs"]})',
                       color='red', linewidth=self.viz_params['linewidth'], alpha=0.8, linestyle='--')
            
            # 标记重要时间点
            ax.axvline(x=0, color='green', linestyle='--', alpha=0.8, linewidth=1.5, label='R波')
            ax.axvspan(self.viz_params['hep_window'][0], self.viz_params['hep_window'][1],
                      alpha=self.viz_params['alpha'], color='yellow', label='HEP窗口')
            
            # 设置子图属性
            electrode_str = f"通道 {electrode_indices[0]}-{electrode_indices[-1]}"
            ax.set_title(f'{group_name}\n{electrode_str}',
                        fontsize=14, fontweight='bold')
            ax.set_xlabel('时间 (ms)', fontsize=12)
            ax.set_ylabel('幅度 (μV)', fontsize=12)
            ax.grid(True, alpha=0.3)
            ax.set_xlim(self.viz_params['time_window'])
            
            # 只在第一个子图显示图例
            if i == 0:
                ax.legend(loc='upper right', fontsize=10, framealpha=0.9)
        
        # 隐藏多余的子图
        for i in range(len(self.electrode_groups), len(axes)):
            axes[i].set_visible(False)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图像
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        plot_path = output_dir / f"methods_comparison_{condition}_{timestamp}.png"
        plt.savefig(plot_path, dpi=self.viz_params['figure_dpi'], bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"📊 {condition} 方法对比图已保存: {plot_path}")


def main():
    """主函数"""
    # 查找最新的完整测试结果目录
    result_base_dir = Path("../../result")
    test_dirs = list(result_base_dir.glob("complete_hep_test_*"))
    
    if not test_dirs:
        print("❌ 未找到完整测试结果目录")
        return
    
    # 使用最新的测试目录
    latest_test_dir = max(test_dirs, key=lambda x: x.stat().st_mtime)
    print(f"📁 使用测试目录: {latest_test_dir}")
    
    # 创建可视化器
    visualizer = CompleteTestDataVisualizer(latest_test_dir)
    
    # 加载数据
    if not visualizer.load_test_data():
        print("❌ 数据加载失败")
        return
    
    # 创建可视化
    visualizer.create_method_comparison_visualization()
    
    print("\n🎉 完整测试数据可视化完成！")


if __name__ == "__main__":
    main()
