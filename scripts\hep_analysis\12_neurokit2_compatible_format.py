#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NeuroKit2兼容格式HEP分析器

完全兼容07脚本的保存格式：
1. 保存路径: D:/ecgeeg/30-数据分析/5-HBA/result/hep_fif_data/
2. 目录结构: condition_name/
3. 文件格式: sub-{subject_id}_task-hep_cond-{condition}_{timestamp}-epo.fif
4. 数据格式: MNE EpochsArray with metadata

作者: HEP Analysis Team
日期: 2024-12-19
版本: v12 - 格式兼容版本
"""

import numpy as np
import pandas as pd
import mne
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入专业NeuroKit2分析器
import sys
import importlib.util
spec = importlib.util.spec_from_file_location("professional_analyzer", "10_professional_neurokit2_hep_analyzer.py")
professional_analyzer = importlib.util.module_from_spec(spec)
spec.loader.exec_module(professional_analyzer)
ProfessionalNeuroKit2HEPAnalyzer = professional_analyzer.ProfessionalNeuroKit2HEPAnalyzer

# 导入NeuroKit2
try:
    import neurokit2 as nk
    NEUROKIT_AVAILABLE = True
except ImportError:
    NEUROKIT_AVAILABLE = False
    raise ImportError("请先安装NeuroKit2")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class NeuroKit2CompatibleFormatAnalyzer:
    """NeuroKit2兼容格式HEP分析器"""
    
    def __init__(self, data_dir):
        """
        初始化兼容格式分析器
        
        Parameters:
        -----------
        data_dir : str
            数据目录路径
        """
        self.data_dir = Path(data_dir)
        
        # 使用与07脚本完全相同的保存路径
        self.base_save_dir = Path("D:/ecgeeg/30-数据分析/5-HBA/result/hep_fif_data")
        self.base_save_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化专业分析器
        self.analyzer = ProfessionalNeuroKit2HEPAnalyzer(data_dir, enable_quality_control=False)
        
        # 分析结果存储
        self.analysis_results = {
            'hep_data': {},
            'failed_files': []
        }
        
        print(f"🚀 NeuroKit2兼容格式HEP分析器初始化完成")
        print(f"📁 数据目录: {self.data_dir}")
        print(f"📁 保存目录: {self.base_save_dir}")
        print(f"🔧 格式兼容: 与07脚本完全一致")

    def find_files_for_all_conditions(self):
        """查找所有条件的文件"""
        print(f"\n🔍 查找所有条件的文件...")
        
        all_files = {}
        
        for condition, pattern in self.analyzer.condition_patterns.items():
            files = list(self.data_dir.rglob(pattern))
            all_files[condition] = files
            print(f"   {condition}: {len(files)} 个文件")
        
        # 统计总数
        total_files = sum(len(files) for files in all_files.values())
        print(f"📊 总计: {total_files} 个文件")
        
        return all_files

    def save_as_compatible_fif(self, condition, result):
        """
        保存为与07脚本完全兼容的.fif格式
        
        Parameters:
        -----------
        condition : str
            实验条件名称
        result : dict
            分析结果
        """
        subject_id = result['file_info']['subject_id']
        
        # 创建条件目录 - 与07脚本相同
        condition_dir = self.base_save_dir / condition
        condition_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建info对象 - 与07脚本相同
        info = mne.create_info(
            ch_names=result['channel_names'],
            sfreq=result['sampling_rate'],
            ch_types=['eeg'] * len(result['channel_names'])  # 只保存EEG数据
        )
        
        # 设置时间信息
        tmin = result['times'][0]
        
        # 创建EpochsArray对象 - 与07脚本相同
        epochs = mne.EpochsArray(
            result['hep_epochs'],
            info,
            tmin=tmin,
            events=np.array([[i, 0, 1] for i in range(len(result['hep_epochs']))]),
            event_id={'hep': 1}
        )
        
        # 添加元数据 - 与07脚本相同
        epochs.metadata = pd.DataFrame({
            'subject_id': [subject_id] * len(result['hep_epochs']),
            'condition': [condition] * len(result['hep_epochs'])
        })
        
        # 保存为fif文件 - 使用与07脚本完全相同的命名格式
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        fif_file = condition_dir / f"sub-{subject_id}_task-hep_cond-{condition}_{timestamp}-epo.fif"
        epochs.save(fif_file, overwrite=True)
        
        print(f"    💾 数据已保存:")
        print(f"      📊 {len(result['hep_epochs'])} 个epochs")
        print(f"      📄 数据文件: {fif_file.name}")
        
        # 清理内存
        del epochs
        
        return str(fif_file)

    def analyze_condition_compatible(self, condition, files):
        """
        分析单个条件并保存为兼容格式
        
        Parameters:
        -----------
        condition : str
            条件名称
        files : list
            文件列表
        """
        print(f"\n🔍 分析条件: {condition} ({self.analyzer.conditions[condition]})")
        print(f"📁 文件数量: {len(files)}")
        
        successful_count = 0
        failed_count = 0
        total_epochs = 0
        processed_subjects = set()
        
        for i, file_path in enumerate(files, 1):
            print(f"\n  📁 [{i}/{len(files)}] {file_path.name}")
            
            # 构建文件信息
            file_info = {
                'file_path': str(file_path),
                'file_name': file_path.name,
                'subject_id': file_path.stem.split('_')[0],
                'condition': condition
            }
            
            try:
                # 使用专业NeuroKit2方法分析
                result = self.analyzer.analyze_single_file_professional(file_info)
                
                if result['success']:
                    successful_count += 1
                    total_epochs += len(result['hep_epochs'])
                    processed_subjects.add(file_info['subject_id'])
                    
                    print(f"    ✅ 成功: {result['r_peaks_count']}个R波, "
                          f"质量: {result['quality_metrics']['quality_level']}")
                    
                    # 立即保存为兼容格式
                    saved_file = self.save_as_compatible_fif(condition, result)
                    
                    # 清理内存
                    del result['hep_epochs']
                    del result
                    
                else:
                    failed_count += 1
                    self.analysis_results['failed_files'].append({
                        'file_info': file_info,
                        'error': result['error']
                    })
                    print(f"    ❌ 失败: {result['error']}")
                    
            except Exception as e:
                failed_count += 1
                self.analysis_results['failed_files'].append({
                    'file_info': file_info,
                    'error': str(e)
                })
                print(f"    ❌ 异常: {e}")
            
            # 定期进行垃圾回收
            if i % 5 == 0:
                import gc
                gc.collect()
        
        # 存储分析结果
        self.analysis_results['hep_data'][condition] = {
            'successful_count': successful_count,
            'failed_count': failed_count,
            'total_epochs': total_epochs,
            'processed_subjects': list(processed_subjects)
        }
        
        print(f"\n📊 条件 {condition} 分析完成:")
        print(f"   ✅ 成功: {successful_count}/{len(files)}")
        print(f"   ❌ 失败: {failed_count}/{len(files)}")
        print(f"   📊 总epochs数: {total_epochs}")
        print(f"   👥 处理的被试数: {len(processed_subjects)}")
        print(f"   📁 数据保存在: {self.base_save_dir / condition}")

    def run_complete_compatible_analysis(self):
        """运行完整的兼容格式分析"""
        print(f"🚀 开始NeuroKit2兼容格式HEP分析")
        print("="*70)
        
        start_time = datetime.now()
        
        # 1. 查找所有文件
        all_files = self.find_files_for_all_conditions()
        
        # 2. 分析每个条件
        for condition, files in all_files.items():
            if files:
                self.analyze_condition_compatible(condition, files)
            else:
                print(f"⚠️ 条件 {condition} 没有找到文件")
        
        # 3. 生成总结报告
        self.generate_final_report()
        
        # 4. 完成总结
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n🎉 兼容格式分析完成!")
        print(f"⏱️ 总耗时: {duration}")
        print(f"📁 结果保存在: {self.base_save_dir}")
        print(f"🔧 格式兼容: 与07脚本完全一致，可直接用于后续可视化")

    def generate_final_report(self):
        """生成最终报告"""
        print(f"\n📋 最终分析报告")
        print("="*60)
        
        total_success = 0
        total_failed = 0
        
        for condition, data in self.analysis_results['hep_data'].items():
            successful = data['successful_count']
            failed = data['failed_count']
            total_epochs = data['total_epochs']
            subjects = len(data['processed_subjects'])
            
            total_success += successful
            total_failed += failed
            
            print(f"\n{condition} ({self.analyzer.conditions[condition]}):")
            print(f"   ✅ 成功文件: {successful}")
            print(f"   ❌ 失败文件: {failed}")
            print(f"   📊 总epochs: {total_epochs}")
            print(f"   👥 被试数量: {subjects}")
        
        # 失败文件报告
        if self.analysis_results['failed_files']:
            print(f"\n❌ 失败文件详情:")
            for failed in self.analysis_results['failed_files']:
                print(f"   {failed['file_info']['file_name']}: {failed['error']}")
        
        print(f"\n📊 总体统计:")
        print(f"   ✅ 成功分析: {total_success} 个文件")
        print(f"   ❌ 分析失败: {total_failed} 个文件")
        print(f"   📈 成功率: {total_success/(total_success+total_failed)*100:.1f}%")
        
        print(f"\n💡 重要提示:")
        print(f"   🔧 数据格式与07脚本完全兼容")
        print(f"   📁 保存路径: {self.base_save_dir}")
        print(f"   🎯 可直接用于后续可视化脚本")

def main():
    """主函数"""
    print("🚀 NeuroKit2兼容格式HEP分析")
    print("="*50)
    
    # 设置路径
    data_dir = "D:/ecgeeg/18-eegecg手动预处理5-重参考2-双侧乳突"
    
    # 创建兼容格式分析器
    analyzer = NeuroKit2CompatibleFormatAnalyzer(data_dir)
    
    # 运行完整分析
    analyzer.run_complete_compatible_analysis()

if __name__ == "__main__":
    main()
