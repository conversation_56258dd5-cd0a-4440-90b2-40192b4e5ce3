#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版HEP结果可视化脚本 v3.0
专门用于读取和可视化修正版增强HEP分析数据

作者：研究团队
日期：2025年6月
版本：3.0 - 修正版增强数据专用可视化器
"""

import pickle
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
from pathlib import Path
from datetime import datetime
import pandas as pd
import mne
from mpl_toolkits.axes_grid1 import make_axes_locatable
import warnings
warnings.filterwarnings('ignore')

# 设置自定义字体
font_path = r"C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf"
try:
    if Path(font_path).exists():
        custom_font = fm.FontProperties(fname=font_path)
        plt.rcParams['font.family'] = custom_font.get_name()
        print(f"✅ 成功加载自定义字体: {custom_font.get_name()}")
    else:
        raise FileNotFoundError("字体文件不存在")
except Exception as e:
    print(f"⚠️ 无法加载自定义字体，使用默认字体: {e}")
    # 设置中文字体备选方案
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']

plt.rcParams['axes.unicode_minus'] = False

class EnhancedHEPVisualizer:
    """增强版HEP结果可视化器 v3.0 - 修正版专用"""

    def __init__(self):
        """
        初始化可视化器
        """
        # 设置数据目录和输出目录
        self.data_dir = Path(r"D:\ecgeeg\30-数据分析\5-HBA\result\hep_fif_data")
        self.output_dir = self.data_dir  # 输出到相同目录

        # 条件名称映射
        self.condition_names = {
            'prac': '练习状态',
            'rest_1': '静息状态1',
            'rest_2': '静息状态2',
            'rest_3': '静息状态3',
            'test_1': '测试状态1',
            'test_2': '测试状态2',
            'test_3': '测试状态3'
        }

        # 电极分组
        self.electrode_groups = {
            '左前额': ['Fp1', 'AF3', 'AF7', 'F1', 'F3', 'F5', 'F7'],
            '右前额': ['Fp2', 'AF4', 'AF8', 'F2', 'F4', 'F6', 'F8'],
            '额中央区域': ['Fpz', 'Fz', 'FC1', 'FC2', 'FC3', 'FC4'],
            '中央区域': ['FC5', 'FC6', 'C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'Cz', 'CPz'],
            '扩展额中央区域': ['Fpz', 'Fz', 'FC1', 'FC2', 'FC3', 'FC4', 'F1', 'F2', 'F3', 'F4', 'C1', 'C2', 'Cz'],
            'F1_F5组合': ['F1', 'F5'],
            'F2_F6组合': ['F2', 'F6'],
            'Cz_FCz组合': ['Cz', 'FCz'],
            'Cz_CPz组合': ['Cz', 'CPz']
        }

        # 可视化参数
        self.viz_params = {
            'figsize': (20, 12),
            'subplot_ratio': (2, 1),
            'time_window': (-200, 600),
            'hep_window': (200, 600),
            'colors': {
                'prac': '#1f77b4',
                'rest_1': '#ff7f0e',
                'rest_2': '#2ca02c',
                'rest_3': '#d62728',
                'test_1': '#9467bd',
                'test_2': '#8c564b',
                'test_3': '#e377c2'
            },
            'alpha': 0.3,
            'linewidth': 2.0,
            'figure_dpi': 300,
            'y_range': (-10, 10)  # 默认Y轴范围（μV）
        }

        # 存储加载的HEP数据
        self.hep_data = {}

        # 设置字体属性
        self.font_prop = fm.FontProperties(fname=font_path) if Path(font_path).exists() else None

        # 加载数据
        self.load_data()

    def load_data(self):
        """加载所有条件的HEP数据"""
        print("\n📂 开始加载HEP数据...")
        print("注意：数据单位将统一转换为μV")

        # 遍历所有条件文件夹
        for condition in self.condition_names.keys():
            condition_dir = self.data_dir / condition
            if not condition_dir.exists():
                print(f"⚠️ 找不到条件文件夹: {condition}")
                continue

            print(f"\n加载 {condition} 条件数据...")
            condition_data = {}

            # 查找并加载该条件下的所有.fif文件
            fif_files = list(condition_dir.glob("*-epo.fif"))
            if not fif_files:
                print(f"⚠️ 在 {condition} 文件夹中未找到.fif文件")
                continue

            # 读取所有epochs数据并计算平均
            all_data = []
            all_times = None
            ch_names = None
            
            for fif_file in fif_files:
                try:
                    # 读取epochs数据
                    epochs = mne.read_epochs(str(fif_file), preload=True)
                    
                    # 数据验证
                    if len(epochs) == 0:
                        print(f"⚠️ {fif_file.name} 不包含任何epochs")
                        continue
                        
                    # 验证采样率
                    if epochs.info['sfreq'] != 500:
                        print(f"⚠️ {fif_file.name} 采样率不是500Hz")
                        continue
                        
                    # 验证时间窗口
                    if epochs.times[0] > -0.2 or epochs.times[-1] < 0.5:
                        print(f"⚠️ {fif_file.name} 时间窗口不足(-0.5s到0.5s)")
                        continue
                    
                    # 计算平均得到evoked数据
                    evoked = epochs.average()
                    
                    # 数据范围检查（μV单位）
                    data_range = np.ptp(evoked.data)
                    if data_range > 200:  # 超过200μV的范围可能不合理
                        print(f"⚠️ {fif_file.name} 数据范围异常: {data_range:.2f}μV")
                        continue
                    
                    # 存储数据（μV单位）
                    all_data.append(evoked.data)
                    if all_times is None:
                        all_times = evoked.times
                    if ch_names is None:
                        ch_names = evoked.ch_names
                    print(f"✅ 成功加载: {fif_file.name}")
                    print(f"   Epochs数量: {len(epochs)}")
                    print(f"   通道数量: {len(evoked.ch_names)}")
                    print(f"   时间范围: {evoked.times[0]:.3f}s 到 {evoked.times[-1]:.3f}s")
                    print(f"   数据范围: {np.min(evoked.data):.2f}μV 到 {np.max(evoked.data):.2f}μV")
                    
                except Exception as e:
                    print(f"❌ 加载失败 {fif_file.name}: {str(e)}")
                    continue

            if not all_data:
                print(f"❌ {condition} 条件没有成功加载任何数据")
                continue

            # 计算总平均和标准误（数据已经是μV单位）
            all_data = np.array(all_data)  # [n_files, n_channels, n_times]
            grand_average = np.mean(all_data, axis=0)  # [n_channels, n_times]
            grand_sem = np.std(all_data, axis=0) / np.sqrt(len(all_data))  # [n_channels, n_times]

            # 存储条件数据（已经是μV单位）
            condition_data['times'] = all_times
            condition_data['data'] = grand_average
            condition_data['grand_average'] = grand_average
            condition_data['grand_sem'] = grand_sem
            condition_data['channel_names'] = ch_names
            condition_data['n_epochs'] = len(all_data)

            self.hep_data[condition] = condition_data
            print(f"✅ {condition}: 成功加载 {len(all_data)} 个数据文件")
            print(f"   数据形状: {grand_average.shape}")
            print(f"   时间范围: {all_times[0]:.3f}s 到 {all_times[-1]:.3f}s")
            print(f"   通道数量: {len(ch_names)}")
            print(f"   数据范围: {np.min(grand_average):.2f}μV 到 {np.max(grand_average):.2f}μV")

        print(f"\n✅ 总共加载了 {len(self.hep_data)} 个条件的数据")

        # 如果有数据，计算全局范围
        if self.hep_data:
            self.calculate_global_range()
        else:
            print("⚠️ 没有成功加载任何数据，使用默认Y轴范围")

    def calculate_global_range(self):
        """计算所有数据的全局范围用于Y轴自适应"""
        all_data = []
        for condition_data in self.hep_data.values():
            if 'grand_average' in condition_data:
                all_data.append(condition_data['grand_average'])

        if all_data:
            all_data = np.concatenate(all_data, axis=1)
            # 数据已经是μV单位，不需要再转换
            
            # 使用百分位数来避免异常值影响
            p1 = np.percentile(all_data, 1)
            p99 = np.percentile(all_data, 99)
            
            # 添加一些边距
            margin = (p99 - p1) * 0.1
            self.viz_params['y_range'] = (p1 - margin, p99 + margin)
            
            print(f"\n📊 数据范围（μV）: {self.viz_params['y_range'][0]:.2f} 到 {self.viz_params['y_range'][1]:.2f}")
        else:
            print("⚠️ 无法计算数据范围，使用默认值")

    def _create_montage(self):
        """创建电极位置信息"""
        try:
            # 使用MNE的标准10-20电极位置
            montage = mne.channels.make_standard_montage('standard_1020')
            return montage
        except Exception as e:
            print(f"⚠️ 创建电极位置失败: {e}")
            return None

    def _add_topography_subplot(self, fig, main_ax, conditions, region_name, time_window=(400, 600)):
        """在子图右下角添加脑地形图子图 - 子母图形式"""
        try:
            # 计算脑地形图的数量和布局
            n_conditions = len(conditions)

            # 存储所有地形图数据用于统一色条
            all_topo_data = []
            topo_info_list = []

            # 获取当前子图在4x2布局中的位置
            ax_pos = main_ax.get_position()
            is_right_column = ax_pos.x0 > 0.5  # 使用matplotlib的Bbox对象的x0属性

            # 为每个条件创建脑地形图 - 使用简化的位置计算
            from mpl_toolkits.axes_grid1.inset_locator import inset_axes

            for i, condition in enumerate(conditions):
                if condition in self.hep_data:
                    # 调整脑地形图位置 - 按照指定的精确位置
                    if n_conditions == 2:
                        # 2个条件：位置根据是否在右列调整
                        if i == 0:
                            bbox_anchor = (0.385, -0.2, 0.35, 0.8)  # 第一个位置
                        else:
                            bbox_anchor = (0.63, -0.2, 0.35, 0.8)   # 第二个位置
                    elif n_conditions == 3:
                        # 3个条件：位置根据是否在右列调整
                        if i == 0:
                            bbox_anchor = (0.38, -0.15, 0.25, 0.7)   # 第一个位置
                        elif i == 1:
                            bbox_anchor = (0.555, -0.15, 0.25, 0.7)  # 第二个位置
                        else:
                            bbox_anchor = (0.73, -0.15, 0.25, 0.7)   # 第三个位置
                    else:
                        continue

                    # 使用inset_axes创建子图
                    topo_ax = inset_axes(main_ax,
                                       width=f'{bbox_anchor[2]*100}%',
                                       height=f'{bbox_anchor[3]*100}%',
                                       loc='lower left',
                                       bbox_to_anchor=bbox_anchor,
                                       bbox_transform=main_ax.transAxes,
                                       borderpad=0)

                    # 获取条件数据
                    condition_data = self.hep_data[condition]
                    if 'grand_average' in condition_data and 'channel_names' in condition_data:
                        # 只使用EEG通道数据（前61个通道）
                        eeg_data = condition_data['grand_average'][:61]
                        eeg_ch_names = condition_data['channel_names'][:61]
                        times = condition_data['times']

                        # 使用固定的480-520ms窗口
                        times_ms = times * 1000
                        window_mask = (times_ms >= 480) & (times_ms <= 520)

                        if np.any(window_mask):
                            # 计算480-520ms窗口的平均值
                            window_data = eeg_data[:, window_mask]
                            topo_data = np.mean(window_data, axis=1)

                            # 固定时间窗口
                            best_time_start = 480
                            best_time_end = 520

                            # 创建蒙太奇
                            montage = mne.channels.make_standard_montage('standard_1020')
                            
                            # 创建info对象，只包含EEG通道
                            info = mne.create_info(eeg_ch_names, 500, ch_types='eeg')
                            info.set_montage(montage)

                            # 将数据转换为mV用于绘图
                            topo_data_mv = topo_data / 1000  # μV转换为mV
                            all_topo_data.append(topo_data_mv)

                            # 绘制地形图
                            im, _ = mne.viz.plot_topomap(
                                topo_data_mv, info,  # 使用mV单位的数据
                                axes=topo_ax,
                                show=False,
                                cmap='autumn',  # 使用hsv配色
                                contours=6,
                                mask_params=dict(marker='o', markerfacecolor='w',
                                               markeredgecolor='k', linewidth=0, markersize=1)
                            )

                            # 添加条件标签和时间窗口信息
                            topo_ax.set_title(f'{condition}\n{best_time_start:.0f}-{best_time_end:.0f}ms',
                                            fontsize=10, fontweight='bold',
                                            fontproperties=self.font_prop)

                            topo_info_list.append((topo_ax, im, condition))

            # 统一设置色条范围并为每个子图添加独立色条
            if all_topo_data and topo_info_list:
                # 计算480-520ms窗口内各条件均值的范围（mV单位）
                all_data = np.concatenate(all_topo_data)
                
                # 使用更稳健的色条范围计算：基于百分位数而非极值
                # 使用5%和95%百分位数来避免异常值影响
                p5 = np.percentile(all_data, 5)
                p95 = np.percentile(all_data, 95)

                # 如果范围太小，使用最小/最大值
                if (p95 - p5) < 0.0005:  # 如果范围小于0.5mV，使用全范围
                    data_min = np.min(all_data)
                    data_max = np.max(all_data)
                else:
                    data_min = p5
                    data_max = p95

                # 确保色条范围使用2位小数，并限制在合理范围内
                vlim = max(abs(data_min), abs(data_max))
                vlim = min(vlim, 0.003)  # 限制最大值为3mV

                # 更新所有地形图的色条范围
                for topo_ax, im, condition in topo_info_list:
                    im.set_clim(-vlim, vlim)

                # 获取主子图的位置信息
                main_ax_pos = main_ax.get_position()

                # 根据子图位置调整色条位置
                if is_right_column:
                    # 右列子图的色条位置
                    cbar_x = main_ax_pos.x1 - 0.01  # 距离右边界1%
                    cbar_width = 0.005  # 色条宽度
                else:
                    # 左列子图的色条位置
                    cbar_x = main_ax_pos.x1 + 0.02  # 距离右边界2%
                    cbar_width = 0.005  # 色条宽度

                # 色条高度和Y位置保持不变
                cbar_y = main_ax_pos.y0 + 0.01  # 距离底部1%（原来是0.06，改为0.01）
                cbar_height = 0.05  # 色条高度

                # 创建统一的色条轴
                cax = fig.add_axes([cbar_x, cbar_y, cbar_width, cbar_height])
                
                # 使用第一个地形图的图像对象创建色条
                cbar = plt.colorbar(topo_info_list[0][1], cax=cax, orientation='vertical')
                cbar.set_label('mV', fontsize=11, fontproperties=self.font_prop)
                cbar.ax.tick_params(labelsize=9)

                # 设置色条刻度
                cbar.set_ticks([-vlim, 0, vlim])
                cbar.set_ticklabels([f'{-vlim*1000:.1f}', '0.0', f'{vlim*1000:.1f}'])

        except Exception as e:
            print(f"⚠️ 添加脑地形图失败: {e}")
            import traceback
            traceback.print_exc()
            pass  # 如果脑地形图失败，不影响主图

    def _plot_region_conditions(self, ax, region_name, conditions, title, add_topography=True):
        """在指定的轴上绘制单个脑区的条件对比"""

        # 获取该脑区的电极列表
        region_electrodes = self.electrode_groups[region_name]

        # 定义鲜明对比但不压抑的配色方案
        condition_colors = {
            'test_1': '#1f77b4',  # 明亮蓝色
            'test_2': '#ff7f0e',  # 活力橙色
            'test_3': '#2ca02c',  # 生机绿色
            'rest_1': '#d62728',  # 深红色
            'rest_2': '#9467bd',  # 紫色
            'rest_3': '#8c564b'   # 棕色
        }
        line_styles = ['-', '-', '-']

        # 收集所有条件的数据用于基线校正
        all_conditions_data = []
        times = None
        
        # 首先收集所有条件的数据
        for condition in conditions:
            if condition in self.hep_data:
                condition_data = self.hep_data[condition]
                if 'grand_average' in condition_data and 'channel_names' in condition_data:
                    grand_average = condition_data['grand_average']
                    channel_names = [str(ch) for ch in condition_data['channel_names']]
                    times = condition_data['times']

                    # 找到该脑区电极在数据中的索引
                    region_indices = []
                    for electrode in region_electrodes:
                        if electrode in channel_names:
                            region_indices.append(channel_names.index(electrode))

                    if region_indices:
                        # 计算该脑区的平均HEP
                        region_hep = np.mean(grand_average[region_indices, :], axis=0)
                        all_conditions_data.append(region_hep)

        if all_conditions_data and times is not None:
            # 将所有条件的数据转换为numpy数组
            all_conditions_data = np.array(all_conditions_data)  # [n_conditions, n_times]
            
            # 找到基线时间窗口的索引 (-200ms到-50ms)
            times_ms = times * 1000
            baseline_mask = (times_ms >= -200) & (times_ms <= -50)  # 修改基线时间窗口
            
            # 计算基线期间的平均值
            baseline_means = np.mean(all_conditions_data[:, baseline_mask], axis=1, keepdims=True)
            
            # 对所有条件的数据进行基线校正
            all_conditions_data = all_conditions_data - baseline_means

            # 为每个条件绘制校正后的波形
            for i, (condition, condition_data) in enumerate(zip(conditions, all_conditions_data)):
                if condition in self.hep_data:
                    # 转换时间为毫秒
                    viz_mask = (times_ms >= self.viz_params['time_window'][0]) & \
                               (times_ms <= self.viz_params['time_window'][1])
                    viz_times = times_ms[viz_mask]
                    viz_hep = condition_data[viz_mask]

                    # 计算标准误
                    condition_sem = self.hep_data[condition]['grand_sem']
                    region_sem = np.mean(condition_sem[region_indices, :], axis=0)
                    viz_sem = region_sem[viz_mask]

                    # 绘制波形
                    color = condition_colors[condition]
                    line_style = line_styles[i % len(line_styles)]
                    label = condition

                    # 绘制主波形线
                    ax.plot(viz_times, viz_hep,
                           label=label,
                           color=color,
                           linestyle=line_style,
                           linewidth=2.5,
                           alpha=1.0)

                    # 添加标准误阴影带
                    ax.fill_between(viz_times,
                                   viz_hep - viz_sem,
                                   viz_hep + viz_sem,
                                   color=color,
                                   alpha=0.15,
                                   linewidth=0)

        # 设置图表属性
        ax.axvline(x=0, color='#666666', linestyle='--', alpha=0.8, linewidth=2.5, label='R波')
        ax.axhline(y=0, color='#CCCCCC', linestyle='-', alpha=0.6, linewidth=2.5)
        ax.axvspan(self.viz_params['hep_window'][0], self.viz_params['hep_window'][1],
                  alpha=0.08, color='#808080', label='HEP窗口')

        ax.set_title(title, fontsize=25, fontweight='bold', pad=15, color='#333333')
        ax.set_xlabel('时间 (ms)', fontsize=25, color='#333333')
        ax.set_ylabel('幅度 (μV)', fontsize=25, color='#333333')
        ax.grid(True, alpha=0.2, color='#DDDDDD', linewidth=1.0)
        ax.set_xlim(self.viz_params['time_window'])

        # 强制重新计算Y轴范围 - 基于实际绘制的数据
        all_y_data = []

        # 获取当前子图中所有线条的数据范围
        for line in ax.get_lines():
            y_data = line.get_ydata()
            if len(y_data) > 0 and not np.all(np.isnan(y_data)) and not np.all(y_data == 0):
                all_y_data.extend(y_data)

        # 获取填充区域的数据范围
        for collection in ax.collections:
            if hasattr(collection, 'get_paths'):
                for path in collection.get_paths():
                    vertices = path.vertices
                    if len(vertices) > 0:
                        y_values = vertices[:, 1]
                        if not np.all(np.isnan(y_values)) and not np.all(y_values == 0):
                            all_y_data.extend(y_values)

        if all_y_data:
            y_min = np.min(all_y_data)
            y_max = np.max(all_y_data)

            # 确保有合理的范围
            if abs(y_max - y_min) < 1e-6:
                y_center = (y_max + y_min) / 2
                y_min = y_center - 0.5
                y_max = y_center + 0.5
            else:
                # 添加15%的边距以便更好地显示数据
                margin = (y_max - y_min) * 0.15
                y_min -= margin
                y_max += margin
        else:
            # 如果没有有效数据，使用默认范围
            y_min, y_max = -2.0, 2.0
            print(f"⚠️ 未找到有效的Y轴数据，使用默认范围: {y_min} 到 {y_max}")

        ax.set_ylim(y_min, y_max)
        print(f"📊 Y轴范围设置为: {y_min:.3f} 到 {y_max:.3f}")

        # 计算合适的刻度间隔
        y_range = y_max - y_min
        y_step = y_range / 6  # 大约6个刻度
        if y_step > 0:
            ax.set_yticks(np.arange(
                np.floor(y_min),
                np.ceil(y_max),
                max(y_step, 0.1)  # 确保步长不会太小
            ))

        # 设置X轴刻度更密集
        x_ticks = np.arange(self.viz_params['time_window'][0],
                           self.viz_params['time_window'][1] + 1, 100)
        ax.set_xticks(x_ticks)

        # 设置刻度数字大小与标题一致
        ax.tick_params(axis='both', which='major', labelsize=25)

        # 设置子图框边线条粗细为一半
        for spine in ax.spines.values():
            spine.set_linewidth(3.75)

        # 设置子图横纵轴比例为2:1
        ax.set_aspect('auto')
        box = ax.get_position()
        ax.set_position([box.x0, box.y0, box.width, box.height * 0.5])

        # 设置图例 - 左下角，23pt字体
        ax.legend(loc='lower left', fontsize=23, framealpha=0.9)

        # 添加电极名称标注
        electrode_names = ', '.join(self.electrode_groups[region_name])
        ax.text(0.02, 0.98, f'电极: {electrode_names}',
               transform=ax.transAxes, fontsize=25, fontweight='bold',
               verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        # 添加脑地形图（如果启用）
        if add_topography and hasattr(ax, 'figure'):
            self._add_topography_subplot(ax.figure, ax, conditions, region_name)

    def create_comprehensive_test_rest_comparison(self):
        """创建3行2列的test和rest条件对比图"""
        print("\n📊 生成3行2列test和rest条件对比图...")

        try:
            # 创建4行2列的子图布局 - 调整为2:1横纵比
            fig, axes = plt.subplots(4, 2, figsize=(32, 32))  # 增加宽度以适应2:1比例

            # 定义条件分组
            test_conditions = ['test_1', 'test_2', 'test_3']
            rest_conditions = ['rest_1', 'rest_2', 'rest_3']

            # 定义脑区和对应的行
            regions = ['左前额', '右前额', '额中央区域', '中央区域']

            # 为每个脑区绘制test和rest条件对比
            for row, region_name in enumerate(regions):
                # 左列：test条件
                self._plot_region_conditions(axes[row, 0], region_name, test_conditions, f'{region_name} - Test条件对比')

                # 右列：rest条件
                self._plot_region_conditions(axes[row, 1], region_name, rest_conditions, f'{region_name} - Rest条件对比')

            # 调整子图间距以适应2:1比例
            plt.subplots_adjust(left=0.08, right=0.95, top=0.95, bottom=0.08,
                              wspace=0.25, hspace=0.35)  # 调整间距适应横纵比

            # 保存图像 - 调整DPI控制文件大小
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            plot_filename = f"figure1_comprehensive_test_rest_comparison_{timestamp}.png"
            plot_path = self.output_dir / plot_filename
            plt.savefig(plot_path, dpi=300,  # 降低DPI到300，控制文件大小在3-5MB
                       bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"✅ 图1已保存: {plot_filename}")
            return plot_path

        except Exception as e:
            print(f"❌ 创建3行2列对比图失败: {str(e)}")
            return None

    def create_figure2_corresponding_conditions(self):
        """图2：4行3列 - 对应条件对比 (rest_1 vs test_1, rest_2 vs test_2, rest_3 vs test_3)"""
        print("\n📊 生成图2：对应条件对比...")

        try:
            # 创建4行3列的子图布局
            fig, axes = plt.subplots(4, 3, figsize=(48, 32))  # 3列需要更宽

            # 定义对应条件组合
            condition_pairs = [
                (['rest_1'], ['test_1']),
                (['rest_2'], ['test_2']),
                (['rest_3'], ['test_3'])
            ]

            # 定义脑区
            regions = ['左前额', '右前额', '额中央区域', '中央区域']

            # 为每个脑区和条件组合绘制对比
            for row, region_name in enumerate(regions):
                for col, (rest_conds, test_conds) in enumerate(condition_pairs):
                    title = f'{region_name} - {rest_conds[0]} vs {test_conds[0]}'
                    self._plot_region_conditions(axes[row, col], region_name,
                                                rest_conds + test_conds, title)

            # 调整子图间距
            plt.subplots_adjust(left=0.06, right=0.96, top=0.95, bottom=0.08,
                              wspace=0.2, hspace=0.35)

            # 保存图像 - 降低DPI到200控制文件大小
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            plot_filename = f"figure2_corresponding_conditions_{timestamp}.png"
            plot_path = self.output_dir / plot_filename
            plt.savefig(plot_path, dpi=200, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"✅ 图2已保存: {plot_filename}")
            return plot_path

        except Exception as e:
            print(f"❌ 创建图2失败: {str(e)}")
            return None

    def create_figure3_rest1_vs_tests(self):
        """图3：4行3列 - rest_1与各test条件对比"""
        print("\n📊 生成图3：rest_1与各test条件对比...")

        try:
            # 创建4行3列的子图布局
            fig, axes = plt.subplots(4, 3, figsize=(48, 32))

            # 定义条件组合
            condition_pairs = [
                (['rest_1'], ['test_1']),
                (['rest_1'], ['test_2']),
                (['rest_1'], ['test_3'])
            ]

            # 定义脑区
            regions = ['左前额', '右前额', '额中央区域', '中央区域']

            # 为每个脑区和条件组合绘制对比
            for row, region_name in enumerate(regions):
                for col, (rest_conds, test_conds) in enumerate(condition_pairs):
                    title = f'{region_name} - {rest_conds[0]} vs {test_conds[0]}'
                    self._plot_region_conditions(axes[row, col], region_name,
                                                rest_conds + test_conds, title)

            # 调整子图间距
            plt.subplots_adjust(left=0.06, right=0.96, top=0.95, bottom=0.08,
                              wspace=0.2, hspace=0.35)

            # 保存图像 - 降低DPI到200控制文件大小
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            plot_filename = f"figure3_rest1_vs_tests_{timestamp}.png"
            plot_path = self.output_dir / plot_filename
            plt.savefig(plot_path, dpi=200, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"✅ 图3已保存: {plot_filename}")
            return plot_path

        except Exception as e:
            print(f"❌ 创建图3失败: {str(e)}")
            return None

    def create_figure4_cross_conditions(self):
        """图4：4行2列 - 交叉条件对比 (rest_2 vs test_1, rest_3 vs test_2)"""
        print("\n📊 生成图4：交叉条件对比...")

        try:
            # 创建4行2列的子图布局
            fig, axes = plt.subplots(4, 2, figsize=(32, 32))

            # 定义交叉条件组合
            condition_pairs = [
                (['rest_2'], ['test_1']),
                (['rest_3'], ['test_2'])
            ]

            # 定义脑区
            regions = ['左前额', '右前额', '额中央区域', '中央区域']

            # 为每个脑区和条件组合绘制对比
            for row, region_name in enumerate(regions):
                for col, (rest_conds, test_conds) in enumerate(condition_pairs):
                    title = f'{region_name} - {rest_conds[0]} vs {test_conds[0]}'
                    self._plot_region_conditions(axes[row, col], region_name,
                                                rest_conds + test_conds, title)

            # 调整子图间距
            plt.subplots_adjust(left=0.08, right=0.95, top=0.95, bottom=0.08,
                              wspace=0.25, hspace=0.35)

            # 保存图像
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            plot_filename = f"figure4_cross_conditions_{timestamp}.png"
            plot_path = self.output_dir / plot_filename
            plt.savefig(plot_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"✅ 图4已保存: {plot_filename}")
            return plot_path

        except Exception as e:
            print(f"❌ 创建图4失败: {str(e)}")
            return None

    def create_figure5_difference_waves(self):
        """图5：4行3列 - 差值波形对比"""
        print("\n📊 生成图5：差值波形对比...")

        try:
            # 创建4行3列的子图布局
            fig, axes = plt.subplots(4, 3, figsize=(48, 32))

            # 定义差值组合
            difference_groups = [
                # 左列：test - rest_1
                [('test_1', 'rest_1'), ('test_2', 'rest_1'), ('test_3', 'rest_1')],
                # 中列：test - test
                [('test_2', 'test_1'), ('test_3', 'test_1'), ('test_3', 'test_2')],
                # 右列：rest - rest
                [('rest_2', 'rest_1'), ('rest_3', 'rest_2'), ('rest_3', 'rest_1')]
            ]

            # 定义脑区
            regions = ['左前额', '右前额', '额中央区域', '中央区域']

            # 为每个脑区和差值组合绘制对比
            for row, region_name in enumerate(regions):
                for col, diff_group in enumerate(difference_groups):
                    self._plot_difference_waves(axes[row, col], region_name, diff_group,
                                              f'{region_name} - 差值波形对比')

            # 调整子图间距
            plt.subplots_adjust(left=0.06, right=0.96, top=0.95, bottom=0.08,
                              wspace=0.2, hspace=0.35)

            # 保存图像 - 降低DPI到200控制文件大小
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            plot_filename = f"figure5_difference_waves_{timestamp}.png"
            plot_path = self.output_dir / plot_filename
            plt.savefig(plot_path, dpi=200, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"✅ 图5已保存: {plot_filename}")
            return plot_path

        except Exception as e:
            print(f"❌ 创建图5失败: {str(e)}")
            return None

    def _plot_difference_waves(self, ax, region_name, diff_group, title):
        """绘制差值波形"""

        # 获取该脑区的电极列表
        region_electrodes = self.electrode_groups[region_name]

        # 定义差值波形鲜明对比配色
        diff_colors = ['#1f77b4', '#ff7f0e', '#2ca02c']  # 明亮蓝、活力橙、生机绿

        # 为每个差值对绘制波形
        for i, (cond1, cond2) in enumerate(diff_group):
            if cond1 in self.hep_data and cond2 in self.hep_data:
                # 获取两个条件的数据
                data1 = self.hep_data[cond1]
                data2 = self.hep_data[cond2]

                if ('grand_average' in data1 and 'channel_names' in data1 and
                    'grand_average' in data2 and 'channel_names' in data2):

                    # 找到该脑区电极在数据中的索引
                    channel_names1 = [str(ch) for ch in data1['channel_names']]
                    region_indices = []
                    for electrode in region_electrodes:
                        if electrode in channel_names1:
                            region_indices.append(channel_names1.index(electrode))

                    if region_indices:
                        # 计算差值波形和标准误
                        hep1 = np.mean(data1['grand_average'][region_indices, :], axis=0)
                        hep2 = np.mean(data2['grand_average'][region_indices, :], axis=0)
                        diff_hep = hep1 - hep2

                        # 计算差值的标准误（误差传播）
                        sem1 = np.std(data1['grand_average'][region_indices, :], axis=0) / np.sqrt(len(region_indices))
                        sem2 = np.std(data2['grand_average'][region_indices, :], axis=0) / np.sqrt(len(region_indices))
                        diff_sem = np.sqrt(sem1**2 + sem2**2)  # 差值的标准误

                        # 转换时间为毫秒
                        times_ms = data1['times'] * 1000

                        # 筛选可视化时间窗口
                        viz_mask = (times_ms >= self.viz_params['time_window'][0]) & \
                                   (times_ms <= self.viz_params['time_window'][1])
                        viz_times = times_ms[viz_mask]
                        viz_diff = diff_hep[viz_mask]
                        viz_diff_sem = diff_sem[viz_mask]

                        # 绘制差值波形
                        color = diff_colors[i % len(diff_colors)]
                        label = f'{cond1} - {cond2}'

                        # 绘制主差值线 - 转换回mV单位
                        ax.plot(viz_times, viz_diff / 1000,  # μV转换为mV
                               label=label,
                               color=color,
                               linestyle='-',
                               linewidth=2.5,
                               alpha=1.0)

                        # 添加差值标准误阴影带 - 转换回mV单位
                        ax.fill_between(viz_times,
                                       (viz_diff - viz_diff_sem) / 1000,  # μV转换为mV
                                       (viz_diff + viz_diff_sem) / 1000,  # μV转换为mV
                                       color=color,
                                       alpha=0.15,
                                       linewidth=0)

        # 设置图表属性
        ax.axvline(x=0, color='#666666', linestyle='--', alpha=0.8, linewidth=2.5, label='R波')
        ax.axhline(y=0, color='#CCCCCC', linestyle='-', alpha=0.6, linewidth=2.5)
        ax.axvspan(self.viz_params['hep_window'][0], self.viz_params['hep_window'][1],
                  alpha=0.08, color='#808080', label='HEP窗口')

        ax.set_title(title, fontsize=25, fontweight='bold', pad=15, color='#333333')
        ax.set_xlabel('时间 (ms)', fontsize=25, color='#333333')
        ax.set_ylabel('幅度差值 (mV)', fontsize=25, color='#333333')
        ax.grid(True, alpha=0.2, color='#DDDDDD', linewidth=1.0)
        ax.set_xlim(self.viz_params['time_window'])

        # 强制重新计算Y轴范围 - 基于实际绘制的数据
        all_y_data = []

        # 获取当前子图中所有线条的数据范围
        for line in ax.get_lines():
            y_data = line.get_ydata()
            if len(y_data) > 0 and not np.all(np.isnan(y_data)) and not np.all(y_data == 0):
                all_y_data.extend(y_data)

        # 获取填充区域的数据范围
        for collection in ax.collections:
            if hasattr(collection, 'get_paths'):
                for path in collection.get_paths():
                    vertices = path.vertices
                    if len(vertices) > 0:
                        y_values = vertices[:, 1]
                        if not np.all(np.isnan(y_values)) and not np.all(y_values == 0):
                            all_y_data.extend(y_values)

        if all_y_data:
            y_min = np.min(all_y_data)
            y_max = np.max(all_y_data)

            # 确保有合理的范围
            if abs(y_max - y_min) < 1e-6:
                y_center = (y_max + y_min) / 2
                y_min = y_center - 0.5
                y_max = y_center + 0.5
            else:
                # 添加15%的边距以便更好地显示数据
                margin = (y_max - y_min) * 0.15
                y_min -= margin
                y_max += margin
        else:
            # 如果没有有效数据，使用默认范围
            y_min, y_max = -2.0, 2.0
            print(f"⚠️ 未找到有效的Y轴数据，使用默认范围: {y_min} 到 {y_max}")

        ax.set_ylim(y_min, y_max)
        print(f"📊 Y轴范围设置为: {y_min:.3f} 到 {y_max:.3f}")

        # 计算合适的刻度间隔
        y_range = y_max - y_min
        y_step = y_range / 6  # 大约6个刻度
        if y_step > 0:
            ax.set_yticks(np.arange(
                np.floor(y_min),
                np.ceil(y_max),
                max(y_step, 0.1)  # 确保步长不会太小
            ))

        # 设置X轴刻度更密集
        x_ticks = np.arange(self.viz_params['time_window'][0],
                           self.viz_params['time_window'][1] + 1, 100)
        ax.set_xticks(x_ticks)

        # 设置刻度和框边
        ax.tick_params(axis='both', which='major', labelsize=25)
        for spine in ax.spines.values():
            spine.set_linewidth(3.75)

        # 设置子图横纵轴比例为2:1
        ax.set_aspect('auto')
        box = ax.get_position()
        ax.set_position([box.x0, box.y0, box.width, box.height * 0.5])

        # 设置图例和电极标注 - 左下角，23pt字体
        ax.legend(loc='lower left', fontsize=23, framealpha=0.9)

        electrode_names = ', '.join(self.electrode_groups[region_name])
        ax.text(0.02, 0.98, f'电极: {electrode_names}',
               transform=ax.transAxes, fontsize=25, fontweight='bold',
               verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    def _plot_individual_electrodes(self, ax, electrodes, condition, title):
        """绘制单个电极的对比图"""

        # 定义电极颜色
        electrode_colors = {
            'F1': '#1f77b4',   # 蓝色
            'F5': '#ff7f0e',   # 橙色
            'F2': '#2ca02c',   # 绿色
            'F6': '#d62728',   # 红色
            'Cz': '#9467bd',   # 紫色
            'FCz': '#8c564b',  # 棕色
            'CPz': '#e377c2',  # 粉色
            'FC1': '#17becf',  # 青色
            'FC2': '#bcbd22',  # 黄绿色
            'CP1': '#ff9896',  # 浅红色
            'CP2': '#c5b0d5'   # 浅紫色
        }

        if condition in self.hep_data:
            condition_data = self.hep_data[condition]

            if 'grand_average' in condition_data and 'channel_names' in condition_data:
                grand_average = condition_data['grand_average']
                channel_names = [str(ch) for ch in condition_data['channel_names']]
                times = condition_data['times']

                # 为每个电极绘制波形
                for electrode in electrodes:
                    if electrode in channel_names:
                        electrode_idx = channel_names.index(electrode)

                        # 获取该电极的HEP数据
                        electrode_hep = grand_average[electrode_idx, :]

                        # 计算标准误（这里使用简化方法，实际应该基于试次数据）
                        electrode_sem = np.std(grand_average[electrode_idx:electrode_idx+1, :], axis=0) * 0.1

                        # 转换时间为毫秒
                        times_ms = times * 1000

                        # 筛选可视化时间窗口
                        viz_mask = (times_ms >= self.viz_params['time_window'][0]) & \
                                   (times_ms <= self.viz_params['time_window'][1])
                        viz_times = times_ms[viz_mask]
                        viz_hep = electrode_hep[viz_mask]
                        viz_sem = electrode_sem[viz_mask]

                        # 绘制波形
                        color = electrode_colors.get(electrode, '#000000')

                        # 绘制主波形线 - 转换回mV单位
                        ax.plot(viz_times, viz_hep / 1000,  # μV转换为mV
                               label=electrode,
                               color=color,
                               linestyle='-',
                               linewidth=2.5,
                               alpha=1.0)

                        # 添加标准误阴影带
                        ax.fill_between(viz_times,
                                       (viz_hep - viz_sem) / 1000,  # μV转换为mV
                                       (viz_hep + viz_sem) / 1000,  # μV转换为mV
                                       color=color,
                                       alpha=0.15,
                                       linewidth=0)

        # 设置图表属性
        ax.axvline(x=0, color='#666666', linestyle='--', alpha=0.8, linewidth=2.5, label='R波')
        ax.axhline(y=0, color='#CCCCCC', linestyle='-', alpha=0.6, linewidth=2.5)
        ax.axvspan(self.viz_params['hep_window'][0], self.viz_params['hep_window'][1],
                  alpha=0.08, color='#808080', label='HEP窗口')

        ax.set_title(title, fontsize=25, fontweight='bold', pad=15, color='#333333')
        ax.set_xlabel('时间 (ms)', fontsize=25, color='#333333')
        ax.set_ylabel('幅度 (mV)', fontsize=25, color='#333333')
        ax.grid(True, alpha=0.2, color='#DDDDDD', linewidth=1.0)
        ax.set_xlim(self.viz_params['time_window'])

        # 强制重新计算Y轴范围 - 基于实际绘制的数据
        all_y_data = []

        # 获取当前子图中所有线条的数据范围
        for line in ax.get_lines():
            y_data = line.get_ydata()
            if len(y_data) > 0 and not np.all(np.isnan(y_data)) and not np.all(y_data == 0):
                all_y_data.extend(y_data)

        # 获取填充区域的数据范围
        for collection in ax.collections:
            if hasattr(collection, 'get_paths'):
                for path in collection.get_paths():
                    vertices = path.vertices
                    if len(vertices) > 0:
                        y_values = vertices[:, 1]
                        if not np.all(np.isnan(y_values)) and not np.all(y_values == 0):
                            all_y_data.extend(y_values)

        if all_y_data:
            y_min = np.min(all_y_data)
            y_max = np.max(all_y_data)

            # 确保有合理的范围
            if abs(y_max - y_min) < 1e-6:
                y_center = (y_max + y_min) / 2
                y_min = y_center - 0.5
                y_max = y_center + 0.5
            else:
                # 添加15%的边距以便更好地显示数据
                margin = (y_max - y_min) * 0.15
                y_min -= margin
                y_max += margin
        else:
            # 如果没有有效数据，使用默认范围
            y_min, y_max = -2.0, 2.0
            print(f"⚠️ 未找到有效的Y轴数据，使用默认范围: {y_min} 到 {y_max}")

        ax.set_ylim(y_min, y_max)
        print(f"📊 Y轴范围设置为: {y_min:.3f} 到 {y_max:.3f}")

        # 计算合适的刻度间隔
        y_range = y_max - y_min
        y_step = y_range / 6  # 大约6个刻度
        if y_step > 0:
            ax.set_yticks(np.arange(
                np.floor(y_min),
                np.ceil(y_max),
                max(y_step, 0.1)  # 确保步长不会太小
            ))

        # 设置X轴刻度更密集
        x_ticks = np.arange(self.viz_params['time_window'][0],
                           self.viz_params['time_window'][1] + 1, 100)
        ax.set_xticks(x_ticks)

        # 设置刻度数字大小与标题一致
        ax.tick_params(axis='both', which='major', labelsize=25)

        # 设置子图框边线条粗细
        for spine in ax.spines.values():
            spine.set_linewidth(3.75)

        # 设置子图横纵轴比例为2:1
        ax.set_aspect('auto')
        box = ax.get_position()
        ax.set_position([box.x0, box.y0, box.width, box.height * 0.5])

        # 设置图例 - 左下角，23pt字体
        ax.legend(loc='lower left', fontsize=23, framealpha=0.9)

        # 添加电极名称标注
        electrode_names = ', '.join(electrodes)
        ax.text(0.02, 0.98, f'电极: {electrode_names}',
               transform=ax.transAxes, fontsize=25, fontweight='bold',
               verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    def create_figure6_detailed_frontocentral_analysis(self):
        """图6：5行3列 - 额中央区域详细分析"""
        print("\n📊 生成图6：额中央区域详细分析...")

        try:
            # 创建5行3列的子图布局
            fig, axes = plt.subplots(5, 3, figsize=(48, 40))  # 5行3列

            # 第一排：扩展额中央区域
            # 左：扩展额中央区域 - Rest条件
            self._plot_region_conditions(axes[0, 0], '扩展额中央区域', ['rest_1', 'rest_2', 'rest_3'], '扩展额中央区域 - Rest条件')
            # 中：扩展额中央区域 - Test条件
            self._plot_region_conditions(axes[0, 1], '扩展额中央区域', ['test_1', 'test_2', 'test_3'], '扩展额中央区域 - Test条件')
            # 右：扩展额中央区域 - Rest1 vs Tests
            self._plot_region_conditions(axes[0, 2], '扩展额中央区域', ['rest_1', 'test_1', 'test_2', 'test_3'], '扩展额中央区域 - Rest1 vs Tests')

            # 第二排：F1 F5 在不同Rest条件下的对比
            # 左：F1 F5 在Rest1条件
            self._plot_individual_electrodes(axes[1, 0], ['F1', 'F5'], 'rest_1', 'F1 vs F5 - Rest1条件')
            # 中：F1 F5 在Rest2条件
            self._plot_individual_electrodes(axes[1, 1], ['F1', 'F5'], 'rest_2', 'F1 vs F5 - Rest2条件')
            # 右：F1 F5 在Rest3条件
            self._plot_individual_electrodes(axes[1, 2], ['F1', 'F5'], 'rest_3', 'F1 vs F5 - Rest3条件')

            # 第三排：F1 F5 在不同Test条件下的对比
            # 左：F1 F5 在Test1条件
            self._plot_individual_electrodes(axes[2, 0], ['F1', 'F5'], 'test_1', 'F1 vs F5 - Test1条件')
            # 中：F1 F5 在Test2条件
            self._plot_individual_electrodes(axes[2, 1], ['F1', 'F5'], 'test_2', 'F1 vs F5 - Test2条件')
            # 右：F1 F5 在Test3条件
            self._plot_individual_electrodes(axes[2, 2], ['F1', 'F5'], 'test_3', 'F1 vs F5 - Test3条件')

            # 第四排：F2 F6 在不同Rest条件下的对比
            # 左：F2 F6 在Rest1条件
            self._plot_individual_electrodes(axes[3, 0], ['F2', 'F6'], 'rest_1', 'F2 vs F6 - Rest1条件')
            # 中：F2 F6 在Rest2条件
            self._plot_individual_electrodes(axes[3, 1], ['F2', 'F6'], 'rest_2', 'F2 vs F6 - Rest2条件')
            # 右：F2 F6 在Rest3条件
            self._plot_individual_electrodes(axes[3, 2], ['F2', 'F6'], 'rest_3', 'F2 vs F6 - Rest3条件')

            # 第五排：F2 F6 在不同Test条件下的对比
            # 左：F2 F6 在Test1条件
            self._plot_individual_electrodes(axes[4, 0], ['F2', 'F6'], 'test_1', 'F2 vs F6 - Test1条件')
            # 中：F2 F6 在Test2条件
            self._plot_individual_electrodes(axes[4, 1], ['F2', 'F6'], 'test_2', 'F2 vs F6 - Test2条件')
            # 右：F2 F6 在Test3条件
            self._plot_individual_electrodes(axes[4, 2], ['F2', 'F6'], 'test_3', 'F2 vs F6 - Test3条件')

            # 调整子图间距
            plt.subplots_adjust(left=0.06, right=0.96, top=0.95, bottom=0.08,
                              wspace=0.2, hspace=0.35)

            # 保存图像 - 降低DPI到200控制文件大小
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            plot_filename = f"figure6_detailed_frontocentral_analysis_{timestamp}.png"
            plot_path = self.output_dir / plot_filename
            plt.savefig(plot_path, dpi=200, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"✅ 图6已保存: {plot_filename}")
            return plot_path

        except Exception as e:
            print(f"❌ 创建图6失败: {str(e)}")
            return None

    def create_figure7_central_electrode_analysis(self):
        """图7：3行3列 - 中央电极详细分析"""
        print("\n📊 生成图7：中央电极详细分析...")

        try:
            # 创建3行3列的子图布局
            fig, axes = plt.subplots(3, 3, figsize=(48, 24))  # 3行3列

            # 第一排：Cz FC1 FC2 在不同Rest条件下的对比
            # 左：Cz FC1 FC2 在Rest1条件
            self._plot_individual_electrodes(axes[0, 0], ['Cz', 'FC1', 'FC2'], 'rest_1', 'Cz vs FC1 vs FC2 - Rest1条件')
            # 中：Cz FC1 FC2 在Rest2条件
            self._plot_individual_electrodes(axes[0, 1], ['Cz', 'FC1', 'FC2'], 'rest_2', 'Cz vs FC1 vs FC2 - Rest2条件')
            # 右：Cz FC1 FC2 在Rest3条件
            self._plot_individual_electrodes(axes[0, 2], ['Cz', 'FC1', 'FC2'], 'rest_3', 'Cz vs FC1 vs FC2 - Rest3条件')

            # 第二排：Cz CPz 在不同Test条件下的对比
            # 左：Cz CPz 在Test1条件
            self._plot_individual_electrodes(axes[1, 0], ['Cz', 'CPz'], 'test_1', 'Cz vs CPz - Test1条件')
            # 中：Cz CPz 在Test2条件
            self._plot_individual_electrodes(axes[1, 1], ['Cz', 'CPz'], 'test_2', 'Cz vs CPz - Test2条件')
            # 右：Cz CPz 在Test3条件
            self._plot_individual_electrodes(axes[1, 2], ['Cz', 'CPz'], 'test_3', 'Cz vs CPz - Test3条件')

            # 第三排：Cz CP1 CP2 在不同Rest条件下的对比
            # 左：Cz CP1 CP2 在Rest1条件
            self._plot_individual_electrodes(axes[2, 0], ['Cz', 'CP1', 'CP2'], 'rest_1', 'Cz vs CP1 vs CP2 - Rest1条件')
            # 中：Cz CP1 CP2 在Rest2条件
            self._plot_individual_electrodes(axes[2, 1], ['Cz', 'CP1', 'CP2'], 'rest_2', 'Cz vs CP1 vs CP2 - Rest2条件')
            # 右：Cz CP1 CP2 在Rest3条件
            self._plot_individual_electrodes(axes[2, 2], ['Cz', 'CP1', 'CP2'], 'rest_3', 'Cz vs CP1 vs CP2 - Rest3条件')

            # 调整子图间距
            plt.subplots_adjust(left=0.06, right=0.96, top=0.95, bottom=0.08,
                              wspace=0.2, hspace=0.35)

            # 保存图像 - 降低DPI到200控制文件大小
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            plot_filename = f"figure7_central_electrode_analysis_{timestamp}.png"
            plot_path = self.output_dir / plot_filename
            plt.savefig(plot_path, dpi=200, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"✅ 图7已保存: {plot_filename}")
            return plot_path

        except Exception as e:
            print(f"❌ 创建图7失败: {str(e)}")
            return None

    def generate_all_visualizations(self):
        """生成所有可视化"""
        print("="*80)
        print("🎨 增强版HEP结果可视化 v2.0")
        print("="*80)

        # 生成各种可视化
        paths = []

        try:
            # 1. 生成3行2列的test和rest条件对比图（原图1）
            paths.append(self.create_comprehensive_test_rest_comparison())

            # 2. 图2：4行3列 - 对应条件对比 (rest_1 vs test_1, rest_2 vs test_2, rest_3 vs test_3)
            paths.append(self.create_figure2_corresponding_conditions())

            # 3. 图3：4行3列 - rest_1与各test条件对比 (rest_1 vs test_1, rest_1 vs test_2, rest_1 vs test_3)
            paths.append(self.create_figure3_rest1_vs_tests())

            # 4. 图4：4行2列 - 交叉条件对比 (rest_2 vs test_1, rest_3 vs test_2)
            paths.append(self.create_figure4_cross_conditions())

            # 5. 图5：4行3列 - 差值波形对比
            paths.append(self.create_figure5_difference_waves())

            # 6. 图6：3行3列 - 额中央区域详细分析
            paths.append(self.create_figure6_detailed_frontocentral_analysis())

            # 7. 图7：2行3列 - 中央电极详细分析
            paths.append(self.create_figure7_central_electrode_analysis())

            print(f"\n" + "="*80)
            print("✅ 所有可视化已完成！")
            print(f"结果保存在: {self.output_dir}")
            print("="*80)

        except Exception as e:
            print(f"❌ 可视化过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

        return paths


def main():
    """主函数"""
    try:
        # 创建可视化器（自动查找最新数据）
        visualizer = EnhancedHEPVisualizer()

        # 生成所有可视化
        visualizer.generate_all_visualizations()

    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
