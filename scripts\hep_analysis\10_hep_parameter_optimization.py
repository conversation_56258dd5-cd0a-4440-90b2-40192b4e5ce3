#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HEP参数优化分析器
=================

基于<PERSON><PERSON> et al. (2024)系统性综述，对HEP提取的关键参数进行优化
测试不同参数组合对HEP质量的影响，找到最适合您数据的参数设置

参考文献：
- <PERSON><PERSON><PERSON> et al. (2024). Methodological approaches to derive the heartbeat evoked potential
- 基于97项研究的参数分布分析
"""

import os
import sys
import numpy as np
import pandas as pd
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from itertools import product
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 导入标准化HEP分析器
import importlib.util
spec = importlib.util.spec_from_file_location("standardized_hep_analyzer", "09_standardized_hep_analyzer.py")
standardized_hep_analyzer = importlib.util.module_from_spec(spec)
spec.loader.exec_module(standardized_hep_analyzer)
StandardizedHEPAnalyzer = standardized_hep_analyzer.StandardizedHEPAnalyzer

class HEPParameterOptimizer:
    """
    HEP参数优化器
    
    基于文献的参数分布，系统性测试不同参数组合
    找到最适合特定数据集的参数设置
    """
    
    def __init__(self):
        self.setup_paths()
        self.setup_parameter_ranges()
        
    def setup_paths(self):
        """设置路径"""
        self.base_path = Path("D:/ecgeeg")
        self.data_path = self.base_path / "19-eegecg手动预处理6-ICA4"  # 使用ICA处理的数据
        self.output_path = self.base_path / "30-数据分析/5-HBA/result/parameter_optimization"
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        print(f"📁 数据路径: {self.data_path}")
        print(f"📁 输出路径: {self.output_path}")
    
    def setup_parameter_ranges(self):
        """
        设置基于文献的参数搜索范围
        来源：Virjee et al. (2024) 97项研究的参数分布
        """
        self.parameter_ranges = {
            # 滤波参数 (基于文献分布)
            'highpass': [0.01, 0.05, 0.1, 0.3, 0.5],  # 文献范围：0.01-1Hz
            'lowpass': [20, 25, 30, 35, 40, 50],       # 文献范围：20-100Hz
            
            # 最小RR间隔 (关键参数，97%研究缺失)
            'min_rr_interval': [0.4, 0.5, 0.6, 0.7, 0.8, 1.0],  # 400-1000ms
            
            # HEP分析窗口 (基于文献模态值)
            'hep_window': [
                (0.15, 0.45),  # 早期窗口
                (0.2, 0.5),    # 标准早期
                (0.2, 0.6),    # 文献模态值
                (0.25, 0.55),  # 中期窗口
                (0.3, 0.6),    # 晚期窗口
            ],
            
            # 基线校正窗口
            'baseline_window': [
                (-0.3, -0.05),  # 长基线
                (-0.2, 0.0),    # 文献模态值
                (-0.15, 0.0),   # 中等基线
                (-0.1, 0.0),    # 短基线
            ],
            
            # 振幅拒绝阈值
            'amplitude_threshold': [50e-6, 75e-6, 100e-6, 150e-6, 200e-6],  # 50-200μV
        }
        
        print("🔍 参数搜索范围:")
        for param, values in self.parameter_ranges.items():
            print(f"   {param}: {values}")
    
    def create_parameter_combinations(self, max_combinations=100):
        """
        创建参数组合
        限制组合数量以保证计算可行性
        """
        # 获取所有参数的笛卡尔积
        param_names = list(self.parameter_ranges.keys())
        param_values = list(self.parameter_ranges.values())
        
        all_combinations = list(product(*param_values))
        
        # 如果组合太多，随机采样
        if len(all_combinations) > max_combinations:
            import random
            random.seed(42)  # 保证可重复性
            selected_combinations = random.sample(all_combinations, max_combinations)
            print(f"⚠️ 参数组合过多，随机选择 {max_combinations} 个组合进行测试")
        else:
            selected_combinations = all_combinations
            print(f"📊 将测试 {len(selected_combinations)} 个参数组合")
        
        # 转换为字典格式
        combinations = []
        for combo in selected_combinations:
            param_dict = dict(zip(param_names, combo))
            combinations.append(param_dict)
        
        return combinations
    
    def test_parameter_combination(self, params, test_files):
        """
        测试单个参数组合
        """
        try:
            # 创建临时分析器
            analyzer = StandardizedHEPAnalyzer()
            
            # 更新参数
            analyzer.params.update({
                'highpass': params['highpass'],
                'lowpass': params['lowpass'],
                'min_rr_interval': params['min_rr_interval'],
                'amplitude_threshold': params['amplitude_threshold'],
                'hep_tmin': params['hep_window'][0],
                'hep_tmax': params['hep_window'][1],
                'baseline_tmin': params['baseline_window'][0],
                'baseline_tmax': params['baseline_window'][1],
            })
            
            # 测试文件
            results = []
            for file_path in test_files:
                result = analyzer.process_single_file(file_path, 'test')
                if result is not None:
                    results.append(result)
            
            if not results:
                return None
            
            # 计算平均质量指标
            avg_metrics = {
                'n_files_success': len(results),
                'avg_epochs': np.mean([r['quality_metrics']['n_epochs'] for r in results]),
                'avg_snr': np.mean([r['quality_metrics']['snr'] for r in results]),
                'avg_overall_quality': np.mean([r['quality_metrics']['overall_quality'] for r in results]),
                'avg_trial_consistency': np.mean([r['quality_metrics']['trial_consistency'] for r in results]),
                'avg_statistical_significance': np.mean([r['quality_metrics']['statistical_significance'] for r in results]),
            }
            
            return avg_metrics
            
        except Exception as e:
            print(f"❌ 参数组合测试失败: {e}")
            return None
    
    def run_optimization(self, max_files=10, max_combinations=50):
        """
        运行参数优化
        """
        print("🚀 开始HEP参数优化")
        print("=" * 60)
        
        # 查找测试文件
        test_files = list(self.data_path.glob("*rest*.fif"))[:max_files]
        if not test_files:
            test_files = list(self.data_path.glob("*.fif"))[:max_files]
        
        if not test_files:
            print("❌ 未找到测试文件")
            return None
        
        print(f"📂 使用 {len(test_files)} 个文件进行参数优化")
        
        # 创建参数组合
        combinations = self.create_parameter_combinations(max_combinations)
        
        # 测试每个参数组合
        optimization_results = []
        
        for i, params in enumerate(tqdm(combinations, desc="测试参数组合")):
            print(f"\n🔬 测试组合 {i+1}/{len(combinations)}")
            print(f"   参数: {params}")
            
            metrics = self.test_parameter_combination(params, test_files)
            
            if metrics is not None:
                result = {
                    'combination_id': i,
                    **params,
                    **metrics
                }
                optimization_results.append(result)
                
                print(f"   ✅ 成功: {metrics['n_files_success']}/{len(test_files)} 文件")
                print(f"   📊 质量评分: {metrics['avg_overall_quality']:.1f}")
            else:
                print(f"   ❌ 失败")
        
        if not optimization_results:
            print("❌ 所有参数组合都失败了")
            return None
        
        # 保存结果
        results_df = pd.DataFrame(optimization_results)
        results_file = self.output_path / "parameter_optimization_results.csv"
        results_df.to_csv(results_file, index=False)
        
        # 分析最优参数
        best_params = self.analyze_optimal_parameters(results_df)
        
        # 创建可视化
        self.create_optimization_visualizations(results_df)
        
        print(f"\n🎉 参数优化完成!")
        print(f"📊 测试了 {len(optimization_results)} 个有效参数组合")
        print(f"💾 结果保存至: {results_file}")
        
        return results_df, best_params
    
    def analyze_optimal_parameters(self, results_df):
        """分析最优参数"""
        print(f"\n📈 分析最优参数...")
        
        # 按不同指标排序找到最优参数
        best_by_quality = results_df.loc[results_df['avg_overall_quality'].idxmax()]
        best_by_snr = results_df.loc[results_df['avg_snr'].idxmax()]
        best_by_epochs = results_df.loc[results_df['avg_epochs'].idxmax()]
        
        optimal_params = {
            'best_overall_quality': best_by_quality.to_dict(),
            'best_snr': best_by_snr.to_dict(),
            'best_epochs': best_by_epochs.to_dict()
        }
        
        # 保存最优参数
        optimal_file = self.output_path / "optimal_parameters.json"
        import json
        with open(optimal_file, 'w', encoding='utf-8') as f:
            json.dump(optimal_params, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"🏆 最优参数 (按整体质量):")
        print(f"   滤波: {best_by_quality['highpass']:.2f}-{best_by_quality['lowpass']:.0f} Hz")
        print(f"   最小RR间隔: {best_by_quality['min_rr_interval']:.1f} s")
        print(f"   HEP窗口: {best_by_quality['hep_window']}")
        print(f"   基线窗口: {best_by_quality['baseline_window']}")
        print(f"   振幅阈值: {best_by_quality['amplitude_threshold']*1e6:.0f} μV")
        print(f"   质量评分: {best_by_quality['avg_overall_quality']:.1f}")
        
        return optimal_params
    
    def create_optimization_visualizations(self, results_df):
        """创建优化结果可视化"""
        print(f"📊 创建优化可视化图表...")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('HEP参数优化结果', fontsize=16, fontweight='bold')
        
        # 1. 滤波参数 vs 质量
        ax1 = axes[0, 0]
        scatter = ax1.scatter(results_df['highpass'], results_df['lowpass'], 
                            c=results_df['avg_overall_quality'], 
                            cmap='viridis', s=50, alpha=0.7)
        ax1.set_xlabel('高通滤波 (Hz)')
        ax1.set_ylabel('低通滤波 (Hz)')
        ax1.set_title('滤波参数 vs 质量')
        plt.colorbar(scatter, ax=ax1, label='整体质量')
        
        # 2. 最小RR间隔 vs 质量
        ax2 = axes[0, 1]
        rr_quality = results_df.groupby('min_rr_interval')['avg_overall_quality'].agg(['mean', 'std'])
        ax2.errorbar(rr_quality.index, rr_quality['mean'], yerr=rr_quality['std'], 
                    marker='o', capsize=5)
        ax2.set_xlabel('最小RR间隔 (s)')
        ax2.set_ylabel('平均质量评分')
        ax2.set_title('RR间隔 vs 质量')
        ax2.grid(True, alpha=0.3)
        
        # 3. 振幅阈值 vs epochs数量
        ax3 = axes[0, 2]
        amp_epochs = results_df.groupby('amplitude_threshold')['avg_epochs'].agg(['mean', 'std'])
        ax3.errorbar(amp_epochs.index * 1e6, amp_epochs['mean'], yerr=amp_epochs['std'], 
                    marker='s', capsize=5, color='orange')
        ax3.set_xlabel('振幅阈值 (μV)')
        ax3.set_ylabel('平均epochs数量')
        ax3.set_title('振幅阈值 vs Epochs')
        ax3.grid(True, alpha=0.3)
        
        # 4. 质量评分分布
        ax4 = axes[1, 0]
        ax4.hist(results_df['avg_overall_quality'], bins=20, alpha=0.7, edgecolor='black')
        ax4.axvline(results_df['avg_overall_quality'].mean(), color='red', 
                   linestyle='--', label=f'平均值: {results_df["avg_overall_quality"].mean():.1f}')
        ax4.set_xlabel('整体质量评分')
        ax4.set_ylabel('参数组合数量')
        ax4.set_title('质量评分分布')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # 5. SNR vs 质量
        ax5 = axes[1, 1]
        ax5.scatter(results_df['avg_snr'], results_df['avg_overall_quality'], 
                   alpha=0.7, s=50)
        ax5.set_xlabel('平均SNR (dB)')
        ax5.set_ylabel('整体质量评分')
        ax5.set_title('SNR vs 质量')
        ax5.grid(True, alpha=0.3)
        
        # 6. 成功率分析
        ax6 = axes[1, 2]
        success_rate = results_df['n_files_success'] / results_df['n_files_success'].max()
        quality_bins = pd.cut(results_df['avg_overall_quality'], bins=5)
        success_by_quality = results_df.groupby(quality_bins)['n_files_success'].mean()
        
        success_by_quality.plot(kind='bar', ax=ax6, alpha=0.7)
        ax6.set_xlabel('质量评分区间')
        ax6.set_ylabel('平均成功文件数')
        ax6.set_title('质量 vs 成功率')
        ax6.tick_params(axis='x', rotation=45)
        ax6.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        plot_file = self.output_path / "parameter_optimization_plots.png"
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📈 优化可视化图表已保存至: {plot_file}")

if __name__ == "__main__":
    # 运行参数优化
    optimizer = HEPParameterOptimizer()
    results, best_params = optimizer.run_optimization(max_files=5, max_combinations=30)
    
    if results is not None:
        print("\n🎯 参数优化建议:")
        print("1. 使用最优参数重新分析您的数据")
        print("2. 根据您的研究目标选择合适的参数组合")
        print("3. 考虑数据特性调整参数范围")
        print("\n📚 更多信息请参考:")
        print("   - parameter_optimization_results.csv: 完整结果")
        print("   - optimal_parameters.json: 最优参数")
        print("   - parameter_optimization_plots.png: 可视化分析")
