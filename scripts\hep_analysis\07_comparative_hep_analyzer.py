#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比式HEP分析器 - MATLAB风格 vs 零相位滤波

对比两种R波检测方法：
1. MATLAB风格：基于导数交替性质和幅度阈值
2. 零相位滤波：增强版多方法检测 + 零相位滤波

目标：
- 验证不同R波检测方法的效果
- 对比HEP提取质量
- 评估相位一致性差异
- 生成详细的对比报告

作者：HEP分析团队
日期：2024年12月
版本：1.0
"""

import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import mne
from datetime import datetime
import json
import pickle
from scipy import signal
from scipy.stats import pearsonr, ttest_rel
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
sys.path.append(str(Path(__file__).parent))
try:
    from enhanced_seven_conditions_hep_analyzer import EnhancedSevenConditionsHEPAnalyzer
    from matlab_style_r_wave_detector import MATLABStyleRWaveDetector
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保相关模块文件在同一目录下")
    sys.exit(1)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ComparativeHEPAnalyzer:
    """对比式HEP分析器"""
    
    def __init__(self, data_dir, validation_excel_path):
        """
        初始化对比分析器
        
        Parameters:
        -----------
        data_dir : str
            数据目录路径
        validation_excel_path : str
            验证Excel文件路径
        """
        self.data_dir = Path(data_dir)
        self.validation_excel_path = Path(validation_excel_path)
        
        # 初始化两种分析器
        print("🔧 初始化零相位滤波分析器...")
        self.zero_phase_analyzer = EnhancedSevenConditionsHEPAnalyzer(
            data_dir=data_dir,
            validation_excel_path=validation_excel_path,
            quality_control_level='progressive'
        )
        
        print("🔧 初始化MATLAB风格R波检测器...")
        self.matlab_detector = MATLABStyleRWaveDetector(sampling_rate=500)
        
        # 对比结果存储
        self.comparison_results = {
            'zero_phase_results': {},
            'matlab_style_results': {},
            'comparison_metrics': {},
            'statistical_tests': {}
        }
        
        # 创建输出目录
        self.output_dir = Path("../../result/comparative_hep_analysis")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print("✅ 对比式HEP分析器初始化完成")
    
    def run_comparative_analysis(self, n_files_per_condition=5, test_conditions=['rest1', 'test1']):
        """
        运行对比分析
        
        Parameters:
        -----------
        n_files_per_condition : int
            每个条件测试的文件数量
        test_conditions : list
            测试的实验条件
        """
        print("="*80)
        print("🔬 开始对比式HEP分析")
        print("="*80)
        print(f"测试条件: {test_conditions}")
        print(f"每条件文件数: {n_files_per_condition}")
        print("="*80)
        
        # 第一步：选择测试文件
        print("\n📁 第一步：选择测试文件")
        test_files = self._select_test_files(n_files_per_condition, test_conditions)
        
        if not test_files:
            print("❌ 未找到合适的测试文件")
            return False
        
        # 第二步：零相位滤波方法分析
        print("\n🔵 第二步：零相位滤波方法分析")
        zero_phase_results = self._analyze_with_zero_phase_method(test_files)
        
        # 第三步：MATLAB风格方法分析
        print("\n🔴 第三步：MATLAB风格方法分析")
        matlab_style_results = self._analyze_with_matlab_style_method(test_files)
        
        # 第四步：对比分析
        print("\n📊 第四步：对比分析")
        comparison_metrics = self._perform_comparison_analysis(
            zero_phase_results, matlab_style_results
        )
        
        # 第五步：统计检验
        print("\n📈 第五步：统计检验")
        statistical_results = self._perform_statistical_tests(
            zero_phase_results, matlab_style_results
        )
        
        # 第六步：生成报告
        print("\n📄 第六步：生成对比报告")
        self._generate_comparison_report(
            zero_phase_results, matlab_style_results, 
            comparison_metrics, statistical_results
        )
        
        # 保存结果
        self._save_comparison_results()
        
        print("\n" + "="*80)
        print("✅ 对比式HEP分析完成")
        print("="*80)
        
        return True
    
    def _select_test_files(self, n_files_per_condition, test_conditions):
        """选择测试文件"""
        # 使用零相位分析器的文件选择功能
        success = self.zero_phase_analyzer.select_files_by_condition(n_files_per_condition)
        
        if not success:
            return {}
        
        # 筛选指定条件的文件
        test_files = {}
        for condition in test_conditions:
            if condition in self.zero_phase_analyzer.analysis_results['selected_files']:
                selected = self.zero_phase_analyzer.analysis_results['selected_files'][condition]
                test_files[condition] = selected[:n_files_per_condition]
                print(f"  条件 {condition}: {len(test_files[condition])} 个文件")
        
        return test_files
    
    def _analyze_with_zero_phase_method(self, test_files):
        """使用零相位滤波方法分析"""
        results = {}
        
        for condition, files in test_files.items():
            print(f"  🔵 分析条件 {condition} ({len(files)} 个文件)")
            condition_results = []
            
            for i, file_info in enumerate(files):
                print(f"    文件 {i+1}/{len(files)}: {file_info['file_name']}")
                
                # 使用零相位分析器分析单个文件
                result = self.zero_phase_analyzer.analyze_single_file(file_info, condition)
                
                if result['success']:
                    condition_results.append(result)
                    print(f"      ✅ 成功 - R波: {result['valid_heartbeats']}")
                else:
                    print(f"      ❌ 失败 - {result['reason']}")
            
            results[condition] = condition_results
            print(f"  条件 {condition} 完成: {len(condition_results)}/{len(files)} 成功")
        
        return results
    
    def _analyze_with_matlab_style_method(self, test_files):
        """使用MATLAB风格方法分析"""
        results = {}
        
        for condition, files in test_files.items():
            print(f"  🔴 分析条件 {condition} ({len(files)} 个文件)")
            condition_results = []
            
            for i, file_info in enumerate(files):
                print(f"    文件 {i+1}/{len(files)}: {file_info['file_name']}")
                
                try:
                    # 加载数据
                    raw = mne.io.read_raw_fif(file_info['file_path'], preload=True, verbose=False)
                    data = raw.get_data()
                    sampling_rate = raw.info['sfreq']
                    
                    # 分离EEG和ECG数据
                    eeg_data = data[:61, :]
                    ecg_data = data[61:, :]
                    
                    # 使用MATLAB风格检测R波
                    r_peaks, detection_info = self.matlab_detector.detect_r_waves_multiple_channels(
                        ecg_data, [f'ECG{i+1}' for i in range(ecg_data.shape[0])]
                    )
                    
                    if len(r_peaks) >= 50:  # 最小R波数量要求
                        # 应用零相位滤波到EEG（保持一致性）
                        eeg_filtered = self.zero_phase_analyzer.apply_eeg_filter(eeg_data, sampling_rate)
                        
                        # 提取HEP
                        hep_epochs, times = self.zero_phase_analyzer.extract_hep_epochs(
                            eeg_filtered, r_peaks, sampling_rate
                        )
                        
                        if hep_epochs is not None:
                            # 计算平均HEP
                            hep_average = np.mean(hep_epochs, axis=0)
                            
                            # 质量指标
                            quality_metrics = self.zero_phase_analyzer._calculate_hep_quality_metrics(
                                hep_epochs, times
                            )
                            
                            result = {
                                'file_name': file_info['file_name'],
                                'condition': condition,
                                'success': True,
                                'hep_epochs': hep_epochs,
                                'hep_average': hep_average,
                                'times': times,
                                'valid_heartbeats': len(r_peaks),
                                'quality_metrics': quality_metrics,
                                'detection_info': detection_info,
                                'channel_names': raw.ch_names[:61],
                                'method': 'matlab_style'
                            }
                            
                            condition_results.append(result)
                            print(f"      ✅ 成功 - R波: {len(r_peaks)}")
                        else:
                            print(f"      ❌ HEP提取失败")
                    else:
                        print(f"      ❌ R波数量不足: {len(r_peaks)}")
                        
                except Exception as e:
                    print(f"      ❌ 分析失败: {str(e)}")
            
            results[condition] = condition_results
            print(f"  条件 {condition} 完成: {len(condition_results)}/{len(files)} 成功")
        
        return results
    
    def _perform_comparison_analysis(self, zero_phase_results, matlab_style_results):
        """执行对比分析"""
        comparison_metrics = {}
        
        for condition in zero_phase_results.keys():
            if condition not in matlab_style_results:
                continue
            
            print(f"  📊 对比条件 {condition}")
            
            zp_results = zero_phase_results[condition]
            ms_results = matlab_style_results[condition]
            
            # 匹配相同文件的结果
            matched_pairs = self._match_results_by_filename(zp_results, ms_results)
            
            if len(matched_pairs) == 0:
                print(f"    ⚠️ 条件 {condition} 无匹配的成功结果")
                continue
            
            # 计算对比指标
            condition_metrics = self._calculate_comparison_metrics(matched_pairs)
            comparison_metrics[condition] = condition_metrics
            
            print(f"    ✅ 匹配文件数: {len(matched_pairs)}")
            print(f"    📈 R波检测相关性: {condition_metrics['r_peak_correlation']:.3f}")
            print(f"    🧠 HEP相关性: {condition_metrics['hep_correlation']:.3f}")
        
        return comparison_metrics
    
    def _match_results_by_filename(self, results1, results2):
        """按文件名匹配结果"""
        matched_pairs = []
        
        # 创建文件名到结果的映射
        results1_dict = {r['file_name']: r for r in results1}
        results2_dict = {r['file_name']: r for r in results2}
        
        # 找到共同的文件
        common_files = set(results1_dict.keys()) & set(results2_dict.keys())
        
        for filename in common_files:
            matched_pairs.append({
                'filename': filename,
                'zero_phase': results1_dict[filename],
                'matlab_style': results2_dict[filename]
            })
        
        return matched_pairs
    
    def _calculate_comparison_metrics(self, matched_pairs):
        """计算对比指标"""
        metrics = {
            'n_matched_files': len(matched_pairs),
            'r_peak_counts': {'zero_phase': [], 'matlab_style': []},
            'hep_amplitudes': {'zero_phase': [], 'matlab_style': []},
            'quality_scores': {'zero_phase': [], 'matlab_style': []},
            'r_peak_correlation': 0,
            'hep_correlation': 0,
            'amplitude_difference': 0
        }
        
        for pair in matched_pairs:
            zp_result = pair['zero_phase']
            ms_result = pair['matlab_style']
            
            # R波数量
            metrics['r_peak_counts']['zero_phase'].append(zp_result['valid_heartbeats'])
            metrics['r_peak_counts']['matlab_style'].append(ms_result['valid_heartbeats'])
            
            # HEP平均幅度（选择代表性电极）
            zp_hep_amp = np.mean(np.abs(zp_result['hep_average'][10, :]))  # 电极10作为代表
            ms_hep_amp = np.mean(np.abs(ms_result['hep_average'][10, :]))
            
            metrics['hep_amplitudes']['zero_phase'].append(zp_hep_amp)
            metrics['hep_amplitudes']['matlab_style'].append(ms_hep_amp)
            
            # 质量评分
            zp_quality = zp_result['quality_metrics']['snr']
            ms_quality = ms_result['quality_metrics']['snr']
            
            metrics['quality_scores']['zero_phase'].append(zp_quality)
            metrics['quality_scores']['matlab_style'].append(ms_quality)
        
        # 计算相关性
        if len(matched_pairs) > 1:
            # R波数量相关性
            r_corr, _ = pearsonr(
                metrics['r_peak_counts']['zero_phase'],
                metrics['r_peak_counts']['matlab_style']
            )
            metrics['r_peak_correlation'] = r_corr
            
            # HEP幅度相关性
            hep_corr, _ = pearsonr(
                metrics['hep_amplitudes']['zero_phase'],
                metrics['hep_amplitudes']['matlab_style']
            )
            metrics['hep_correlation'] = hep_corr
            
            # 平均幅度差异
            zp_mean_amp = np.mean(metrics['hep_amplitudes']['zero_phase'])
            ms_mean_amp = np.mean(metrics['hep_amplitudes']['matlab_style'])
            metrics['amplitude_difference'] = (zp_mean_amp - ms_mean_amp) / ms_mean_amp * 100
        
        return metrics
    
    def _perform_statistical_tests(self, zero_phase_results, matlab_style_results):
        """执行统计检验"""
        statistical_results = {}
        
        for condition in zero_phase_results.keys():
            if condition not in matlab_style_results:
                continue
            
            print(f"  📈 统计检验条件 {condition}")
            
            zp_results = zero_phase_results[condition]
            ms_results = matlab_style_results[condition]
            
            matched_pairs = self._match_results_by_filename(zp_results, ms_results)
            
            if len(matched_pairs) < 3:  # 至少需要3对数据
                print(f"    ⚠️ 数据不足，跳过统计检验")
                continue
            
            # 提取配对数据
            zp_r_peaks = [pair['zero_phase']['valid_heartbeats'] for pair in matched_pairs]
            ms_r_peaks = [pair['matlab_style']['valid_heartbeats'] for pair in matched_pairs]
            
            zp_snr = [pair['zero_phase']['quality_metrics']['snr'] for pair in matched_pairs]
            ms_snr = [pair['matlab_style']['quality_metrics']['snr'] for pair in matched_pairs]
            
            # 配对t检验
            try:
                r_peak_t_stat, r_peak_p_val = ttest_rel(zp_r_peaks, ms_r_peaks)
                snr_t_stat, snr_p_val = ttest_rel(zp_snr, ms_snr)
                
                statistical_results[condition] = {
                    'n_pairs': len(matched_pairs),
                    'r_peak_test': {
                        't_statistic': r_peak_t_stat,
                        'p_value': r_peak_p_val,
                        'significant': r_peak_p_val < 0.05
                    },
                    'snr_test': {
                        't_statistic': snr_t_stat,
                        'p_value': snr_p_val,
                        'significant': snr_p_val < 0.05
                    }
                }
                
                print(f"    R波数量差异: t={r_peak_t_stat:.3f}, p={r_peak_p_val:.3f}")
                print(f"    SNR差异: t={snr_t_stat:.3f}, p={snr_p_val:.3f}")
                
            except Exception as e:
                print(f"    ❌ 统计检验失败: {str(e)}")
        
        return statistical_results
    
    def _generate_comparison_report(self, zero_phase_results, matlab_style_results, 
                                  comparison_metrics, statistical_results):
        """生成对比报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = self.output_dir / f"comparative_hep_analysis_report_{timestamp}.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# HEP分析方法对比报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**对比方法**: 零相位滤波 vs MATLAB风格检测\n\n")
            
            # 方法说明
            f.write("## 🔬 对比方法说明\n\n")
            f.write("### 零相位滤波方法\n")
            f.write("- **R波检测**: 多方法增强检测（正峰、负峰、自适应阈值等）\n")
            f.write("- **EEG滤波**: 零相位带通滤波 (0.1-30Hz)\n")
            f.write("- **ECG预处理**: 零相位高通滤波去除基线漂移\n")
            f.write("- **优势**: 保持相位一致性，无波形失真\n\n")
            
            f.write("### MATLAB风格方法\n")
            f.write("- **R波检测**: 基于导数交替性质和幅度阈值\n")
            f.write("- **EEG滤波**: 零相位带通滤波 (保持一致性)\n")
            f.write("- **检测原理**: 导数零交叉点 + 局部极值验证\n")
            f.write("- **优势**: 文献标准方法，计算简单\n\n")
            
            # 对比结果
            f.write("## 📊 对比结果总结\n\n")
            
            for condition, metrics in comparison_metrics.items():
                f.write(f"### 条件 {condition}\n\n")
                f.write(f"- **匹配文件数**: {metrics['n_matched_files']}\n")
                f.write(f"- **R波检测相关性**: {metrics['r_peak_correlation']:.3f}\n")
                f.write(f"- **HEP幅度相关性**: {metrics['hep_correlation']:.3f}\n")
                f.write(f"- **平均幅度差异**: {metrics['amplitude_difference']:.1f}%\n")
                
                # 统计检验结果
                if condition in statistical_results:
                    stat_result = statistical_results[condition]
                    f.write(f"- **R波数量差异**: p={stat_result['r_peak_test']['p_value']:.3f}")
                    f.write(f" ({'显著' if stat_result['r_peak_test']['significant'] else '不显著'})\n")
                    f.write(f"- **SNR差异**: p={stat_result['snr_test']['p_value']:.3f}")
                    f.write(f" ({'显著' if stat_result['snr_test']['significant'] else '不显著'})\n")
                
                f.write("\n")
            
            # 结论和建议
            f.write("## 💡 结论和建议\n\n")
            f.write("### 主要发现\n")
            f.write("1. **R波检测一致性**: 两种方法在R波检测数量上的相关性\n")
            f.write("2. **HEP质量对比**: 不同方法对HEP提取质量的影响\n")
            f.write("3. **方法适用性**: 各方法在不同数据质量下的表现\n\n")
            
            f.write("### 使用建议\n")
            f.write("- **高质量数据**: 两种方法均可使用，零相位滤波方法相位一致性更好\n")
            f.write("- **噪声较多数据**: 零相位滤波方法的多方法检测更稳健\n")
            f.write("- **文献对比**: MATLAB风格方法更接近传统文献方法\n")
            f.write("- **精度要求高**: 推荐零相位滤波方法\n")
        
        print(f"📄 对比报告已保存: {report_path}")
        return report_path
    
    def _save_comparison_results(self):
        """保存对比结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存完整结果
        results_path = self.output_dir / f"comparative_results_{timestamp}.pkl"
        with open(results_path, 'wb') as f:
            pickle.dump(self.comparison_results, f)
        
        print(f"💾 对比结果已保存: {results_path}")


def main():
    """主函数"""
    # 配置路径
    data_dir = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"
    validation_excel_path = r"D:\ecgeeg\30-数据分析\5-HBA\result\data_validation\data_validation_results_20241201_143654.xlsx"
    
    # 创建对比分析器
    analyzer = ComparativeHEPAnalyzer(data_dir, validation_excel_path)
    
    # 运行对比分析
    analyzer.run_comparative_analysis(
        n_files_per_condition=3,  # 每个条件测试3个文件
        test_conditions=['rest1', 'test1']  # 测试两个条件
    )


if __name__ == "__main__":
    main()
