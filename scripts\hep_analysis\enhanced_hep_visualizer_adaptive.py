#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版HEP结果可视化脚本 v3.1 - 自适应Y轴版本
专门用于读取和可视化修正版增强HEP分析数据
修改：自适应Y轴范围，确保所有数据都能完整显示

作者：研究团队
日期：2025年6月
版本：3.1 - 自适应Y轴版本
"""

import pickle
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
from pathlib import Path
from datetime import datetime
import pandas as pd
import mne
from mpl_toolkits.axes_grid1 import make_axes_locatable
import warnings
warnings.filterwarnings('ignore')

# 设置自定义字体
font_path = r"C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf"
try:
    if Path(font_path).exists():
        custom_font = fm.FontProperties(fname=font_path)
        plt.rcParams['font.family'] = custom_font.get_name()
        print(f"✅ 成功加载自定义字体: {custom_font.get_name()}")
    else:
        raise FileNotFoundError("字体文件不存在")
except Exception as e:
    print(f"⚠️ 无法加载自定义字体，使用默认字体: {e}")
    # 设置中文字体备选方案
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']

plt.rcParams['axes.unicode_minus'] = False

class EnhancedHEPVisualizerAdaptive:
    """增强版HEP结果可视化器 v3.1 - 自适应Y轴版本"""

    def __init__(self, data_dir=None, metadata_file=None, timestamp=None):
        """
        初始化可视化器

        Parameters:
        -----------
        data_dir : str, optional
            HEP数据目录路径，如果为None则自动查找最新数据
        metadata_file : str, optional
            元数据文件路径，如果为None则自动查找最新数据
        timestamp : str, optional
            指定时间戳，如果为None则使用最新数据
        """
        # 使用绝对路径确保正确找到数据目录
        script_dir = Path(__file__).parent
        self.base_dir = script_dir.parent.parent / "result" / "enhanced_hep_analysis"

        # 自动查找最新数据
        if timestamp is None:
            timestamp = self._find_latest_timestamp()

        self.timestamp = timestamp
        self.data_dir = Path(data_dir) if data_dir else self.base_dir / "hep_data"
        self.metadata_file = Path(metadata_file) if metadata_file else self.base_dir / f"enhanced_hep_metadata_{timestamp}.json"

        self.metadata = None
        self.hep_data = {}  # 存储加载的HEP数据
        self.output_dir = script_dir.parent.parent / "result" / "enhanced_hep_visualization_adaptive"
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 可视化参数 - 移除固定Y轴范围
        self.viz_params = {
            'figsize': (20, 12),     # 图像大小
            'subplot_ratio': (2, 1), # 子图宽高比 2:1
            'time_window': (-200, 650),  # 显示时间窗口(ms)
            'hep_window': (200, 600),    # HEP成分窗口(ms)
            'colors': {
                'prac': '#1f77b4',
                'rest1': '#ff7f0e',
                'rest2': '#2ca02c',
                'rest3': '#d62728',
                'test1': '#9467bd',
                'test2': '#8c564b',
                'test3': '#e377c2'
            },
            'alpha': 0.3,
            'linewidth': 2.0,
            'figure_dpi': 300,
            'y_margin_factor': 0.1  # Y轴边距因子，10%的额外空间
        }

        self.condition_names = {
            'prac': '练习状态',
            'rest1': '静息状态1',
            'rest2': '静息状态2',
            'rest3': '静息状态3',
            'test1': '测试状态1',
            'test2': '测试状态2',
            'test3': '测试状态3'
        }

        # 电极分组 - 4个脑区用于新的图表分析
        self.electrode_groups = {
            '左前额': ['Fp1', 'AF3', 'AF7', 'F1', 'F3', 'F5', 'F7'],
            '右前额': ['Fp2', 'AF4', 'AF8', 'F2', 'F4', 'F6', 'F8'],
            '额中央区域': ['Fpz', 'Fz', 'FC1', 'FC2', 'FC3', 'FC4'],
            '中央区域': ['FC5', 'FC6', 'C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'Cz', 'CPz'],
            # 图6和图7专用电极组合
            '扩展额中央区域': ['Fpz', 'Fz', 'FC1', 'FC2', 'FC3', 'FC4', 'F1', 'F2', 'F3', 'F4', 'C1', 'C2', 'Cz'],
            'F1_F5组合': ['F1', 'F5'],
            'F2_F6组合': ['F2', 'F6'],
            'Cz_FCz组合': ['Cz', 'FCz'],
            'Cz_CPz组合': ['Cz', 'CPz']
        }

        # 设置字体属性
        self.font_prop = fm.FontProperties(fname=font_path) if Path(font_path).exists() else None

        # 加载数据
        self.load_data()

        print("🎨 自适应Y轴增强HEP可视化器初始化完成")
        print(f"📁 数据目录: {self.data_dir}")
        print(f"📊 支持的实验条件: {list(self.condition_names.keys())}")
        print(f"🧠 脑区分组: {list(self.electrode_groups.keys())}")
        print(f"⏰ 使用时间戳: {self.timestamp}")

    def _find_latest_timestamp(self):
        """查找最新的时间戳"""
        metadata_files = list(self.base_dir.glob("enhanced_hep_metadata_*.json"))
        if not metadata_files:
            raise FileNotFoundError("未找到任何元数据文件")

        # 提取时间戳并排序
        timestamps = []
        for file in metadata_files:
            timestamp = file.stem.replace("enhanced_hep_metadata_", "")
            timestamps.append(timestamp)

        return max(timestamps)

    def load_data(self):
        """加载MNE格式的分析结果和元数据"""
        print("📂 加载修正版增强HEP分析结果...")

        # 加载元数据
        if not self.metadata_file.exists():
            raise FileNotFoundError(f"元数据文件不存在: {self.metadata_file}")

        with open(self.metadata_file, 'r', encoding='utf-8') as f:
            self.metadata = json.load(f)

        print(f"✅ 成功加载元数据")
        print(f"   分析时间: {self.metadata['timestamp']}")
        print(f"   版本: {self.metadata['version']}")

        # 计算总文件数和总epochs数
        total_files = sum(info['n_files'] for info in self.metadata['saved_files'].values())
        total_epochs = sum(info['n_epochs'] for info in self.metadata['saved_files'].values())
        print(f"   总文件数: {total_files}")
        print(f"   总epochs数: {total_epochs}")

        # 加载各条件的HEP数据
        print("📊 加载各条件HEP数据...")
        for condition, file_info in self.metadata['saved_files'].items():
            print(f"  加载 {condition} 条件...")

            condition_data = {}

            # 加载平均HEP数据 (MNE格式)
            avg_file = self.data_dir / f"hep_average_{condition}_{self.timestamp}.fif"
            if avg_file.exists():
                try:
                    evoked = mne.read_evokeds(str(avg_file))[0]
                    condition_data['evoked'] = evoked
                    condition_data['times'] = evoked.times
                    condition_data['data'] = evoked.data
                    condition_data['ch_names'] = evoked.ch_names
                    condition_data['n_epochs'] = file_info['n_epochs']
                    print(f"    ✅ 成功加载平均HEP数据")
                except Exception as e:
                    print(f"    ❌ 加载平均HEP数据失败: {e}")

            # 加载统计信息
            stats_file = self.data_dir / f"hep_stats_{condition}_{self.timestamp}.npz"
            if stats_file.exists():
                try:
                    stats = np.load(str(stats_file), allow_pickle=True)
                    condition_data['grand_average'] = stats['grand_average']
                    condition_data['grand_sem'] = stats['grand_sem']
                    condition_data['group_averages'] = stats['group_averages'].item()
                    condition_data['times'] = stats['times']
                    condition_data['channel_names'] = stats['channel_names']
                    print(f"    ✅ 成功加载统计数据")
                except Exception as e:
                    print(f"    ❌ 加载统计数据失败: {e}")

            # 加载详细信息
            info_file = self.data_dir / f"hep_epochs_info_{condition}_{self.timestamp}.json"
            if info_file.exists():
                try:
                    with open(info_file, 'r', encoding='utf-8') as f:
                        info = json.load(f)
                    condition_data['info'] = info
                    print(f"    ✅ 成功加载详细信息")
                except Exception as e:
                    print(f"    ❌ 加载详细信息失败: {e}")

            if condition_data:  # 只有成功加载数据的条件才添加
                self.hep_data[condition] = condition_data
                print(f"    ✅ {condition}: {file_info['n_epochs']} epochs")
            else:
                print(f"    ❌ {condition}: 无法加载数据")

        print(f"✅ 成功加载 {len(self.hep_data)} 个条件的数据")

    def _calculate_adaptive_ylim(self, ax, conditions, region_name):
        """计算自适应Y轴范围"""
        all_y_values = []
        
        # 获取该脑区的电极列表
        region_electrodes = self.electrode_groups[region_name]
        
        # 收集所有条件在该脑区的数据
        for condition in conditions:
            if condition in self.hep_data:
                condition_data = self.hep_data[condition]
                
                if 'grand_average' in condition_data and 'channel_names' in condition_data:
                    grand_average = condition_data['grand_average']
                    channel_names = [str(ch) for ch in condition_data['channel_names']]
                    times = condition_data['times']
                    
                    # 找到该脑区电极在数据中的索引
                    region_indices = []
                    for electrode in region_electrodes:
                        if electrode in channel_names:
                            region_indices.append(channel_names.index(electrode))
                    
                    if region_indices:
                        # 计算该脑区的平均HEP
                        region_hep = np.mean(grand_average[region_indices, :], axis=0)
                        region_sem = np.std(grand_average[region_indices, :], axis=0) / np.sqrt(len(region_indices))
                        
                        # 转换时间为毫秒
                        times_ms = times * 1000
                        
                        # 筛选可视化时间窗口
                        viz_mask = (times_ms >= self.viz_params['time_window'][0]) & \
                                   (times_ms <= self.viz_params['time_window'][1])
                        viz_hep = region_hep[viz_mask]
                        viz_sem = region_sem[viz_mask]
                        
                        # 转换为显示单位（μV后再缩小10000倍）
                        viz_hep_display = viz_hep * 1e6 / 1e4
                        viz_sem_display = viz_sem * 1e6 / 1e4
                        
                        # 收集数据范围（包括误差带）
                        all_y_values.extend(viz_hep_display + viz_sem_display)
                        all_y_values.extend(viz_hep_display - viz_sem_display)
        
        if all_y_values:
            y_min = np.min(all_y_values)
            y_max = np.max(all_y_values)
            
            # 添加边距
            y_range = y_max - y_min
            margin = y_range * self.viz_params['y_margin_factor']
            
            y_min_with_margin = y_min - margin
            y_max_with_margin = y_max + margin
            
            # 确保范围合理（至少0.1的范围）
            if y_range < 0.1:
                center = (y_min + y_max) / 2
                y_min_with_margin = center - 0.05
                y_max_with_margin = center + 0.05
            
            return y_min_with_margin, y_max_with_margin
        else:
            # 默认范围
            return -0.4, 0.2

    def _create_montage(self):
        """创建电极位置信息"""
        try:
            # 使用MNE的标准10-20电极位置
            montage = mne.channels.make_standard_montage('standard_1020')
            return montage
        except Exception as e:
            print(f"⚠️ 创建电极位置失败: {e}")
            return None

    def _plot_region_conditions(self, ax, region_name, conditions, title, add_topography=True):
        """在指定的轴上绘制单个脑区的条件对比 - 自适应Y轴版本"""

        # 获取该脑区的电极列表
        region_electrodes = self.electrode_groups[region_name]

        # 定义鲜明对比但不压抑的配色方案
        condition_colors = {
            'test1': '#1f77b4',  # 明亮蓝色
            'test2': '#ff7f0e',  # 活力橙色
            'test3': '#2ca02c',  # 生机绿色
            'rest1': '#d62728',  # 深红色
            'rest2': '#9467bd',  # 紫色
            'rest3': '#8c564b'   # 棕色
        }
        line_styles = ['-', '-', '-']  # 统一实线

        # 为每个条件绘制该脑区的波形
        for i, condition in enumerate(conditions):
            if condition in self.hep_data:
                condition_data = self.hep_data[condition]

                if 'grand_average' in condition_data and 'channel_names' in condition_data:
                    grand_average = condition_data['grand_average']
                    channel_names = [str(ch) for ch in condition_data['channel_names']]
                    times = condition_data['times']

                    # 找到该脑区电极在数据中的索引
                    region_indices = []
                    for electrode in region_electrodes:
                        if electrode in channel_names:
                            region_indices.append(channel_names.index(electrode))

                    if region_indices:
                        # 计算该脑区的平均HEP和标准误
                        region_hep = np.mean(grand_average[region_indices, :], axis=0)
                        region_sem = np.std(grand_average[region_indices, :], axis=0) / np.sqrt(len(region_indices))

                        # 转换时间为毫秒
                        times_ms = times * 1000

                        # 筛选可视化时间窗口
                        viz_mask = (times_ms >= self.viz_params['time_window'][0]) & \
                                   (times_ms <= self.viz_params['time_window'][1])
                        viz_times = times_ms[viz_mask]
                        viz_hep = region_hep[viz_mask]
                        viz_sem = region_sem[viz_mask]

                        # 绘制波形
                        color = condition_colors[condition]
                        line_style = line_styles[i % len(line_styles)]
                        label = condition

                        # 绘制主波形线 - Y轴数值直接缩小10000倍显示
                        ax.plot(viz_times, viz_hep * 1e6 / 1e4,
                               label=label,
                               color=color,
                               linestyle=line_style,
                               linewidth=2.5,
                               alpha=1.0)

                        # 添加标准误阴影带
                        ax.fill_between(viz_times,
                                       (viz_hep - viz_sem) * 1e6 / 1e4,
                                       (viz_hep + viz_sem) * 1e6 / 1e4,
                                       color=color,
                                       alpha=0.15,
                                       linewidth=0)

        # 设置图表属性
        ax.axvline(x=0, color='#666666', linestyle='--', alpha=0.8, linewidth=2.5, label='R波')
        ax.axhline(y=0, color='#CCCCCC', linestyle='-', alpha=0.6, linewidth=2.5)
        ax.axvspan(self.viz_params['hep_window'][0], self.viz_params['hep_window'][1],
                  alpha=0.08, color='#808080', label='HEP窗口')

        ax.set_title(title, fontsize=25, fontweight='bold', pad=15, color='#333333')
        ax.set_xlabel('时间 (ms)', fontsize=25, color='#333333')
        ax.set_ylabel('幅度 (μV)', fontsize=25, color='#333333')
        ax.grid(True, alpha=0.2, color='#DDDDDD', linewidth=1.0)
        ax.set_xlim(self.viz_params['time_window'])

        # 🔧 关键修改：使用自适应Y轴范围
        y_min, y_max = self._calculate_adaptive_ylim(ax, conditions, region_name)
        ax.set_ylim(y_min, y_max)

        # 设置Y轴刻度（自适应）
        y_range = y_max - y_min
        if y_range > 1:
            tick_interval = 0.2
        elif y_range > 0.5:
            tick_interval = 0.1
        else:
            tick_interval = 0.05

        y_ticks = np.arange(np.ceil(y_min/tick_interval)*tick_interval,
                           np.floor(y_max/tick_interval)*tick_interval + tick_interval,
                           tick_interval)
        ax.set_yticks(y_ticks)

        # 设置X轴刻度
        x_ticks = np.arange(self.viz_params['time_window'][0],
                           self.viz_params['time_window'][1] + 1, 100)
        ax.set_xticks(x_ticks)

        # 设置刻度数字大小
        ax.tick_params(axis='both', which='major', labelsize=25)

        # 设置子图框边线条粗细
        for spine in ax.spines.values():
            spine.set_linewidth(3.75)

        # 设置子图横纵轴比例为2:1
        ax.set_aspect('auto')
        box = ax.get_position()
        ax.set_position([box.x0, box.y0, box.width, box.height * 0.5])

        # 设置图例
        ax.legend(loc='lower left', fontsize=23, framealpha=0.9)

        # 添加电极名称标注
        electrode_names = ', '.join(self.electrode_groups[region_name])
        ax.text(0.02, 0.98, f'电极: {electrode_names}',
               transform=ax.transAxes, fontsize=25, fontweight='bold',
               verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        print(f"  📊 {region_name}: Y轴范围 [{y_min:.3f}, {y_max:.3f}]")

    def create_comprehensive_test_rest_comparison(self):
        """创建4行2列的test和rest条件对比图 - 自适应Y轴版本"""
        print("\n📊 生成自适应Y轴的test和rest条件对比图...")

        try:
            # 创建4行2列的子图布局
            fig, axes = plt.subplots(4, 2, figsize=(32, 32))

            # 定义条件分组
            test_conditions = ['test1', 'test2', 'test3']
            rest_conditions = ['rest1', 'rest2', 'rest3']

            # 定义脑区和对应的行
            regions = ['左前额', '右前额', '额中央区域', '中央区域']

            # 为每个脑区绘制test和rest条件对比
            for row, region_name in enumerate(regions):
                # 左列：test条件
                self._plot_region_conditions(axes[row, 0], region_name, test_conditions, f'{region_name} - Test条件对比')

                # 右列：rest条件
                self._plot_region_conditions(axes[row, 1], region_name, rest_conditions, f'{region_name} - Rest条件对比')

            # 调整子图间距
            plt.subplots_adjust(left=0.08, right=0.95, top=0.95, bottom=0.08,
                              wspace=0.25, hspace=0.35)

            # 保存图像
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            plot_filename = f"adaptive_figure1_comprehensive_test_rest_comparison_{timestamp}.png"
            plot_path = self.output_dir / plot_filename
            plt.savefig(plot_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"✅ 自适应Y轴图1已保存: {plot_filename}")
            return plot_path

        except Exception as e:
            print(f"❌ 创建自适应Y轴对比图失败: {str(e)}")
            return None

    def create_figure2_corresponding_conditions(self):
        """图2：4行3列 - 对应条件对比 - 自适应Y轴版本"""
        print("\n📊 生成自适应Y轴图2：对应条件对比...")

        try:
            fig, axes = plt.subplots(4, 3, figsize=(48, 32))

            # 定义对应条件组合
            condition_pairs = [
                (['rest1'], ['test1']),
                (['rest2'], ['test2']),
                (['rest3'], ['test3'])
            ]

            # 定义脑区
            regions = ['左前额', '右前额', '额中央区域', '中央区域']

            # 为每个脑区和条件组合绘制对比
            for row, region_name in enumerate(regions):
                for col, (rest_conds, test_conds) in enumerate(condition_pairs):
                    title = f'{region_name} - {rest_conds[0]} vs {test_conds[0]}'
                    self._plot_region_conditions(axes[row, col], region_name,
                                                rest_conds + test_conds, title)

            plt.subplots_adjust(left=0.06, right=0.96, top=0.95, bottom=0.08,
                              wspace=0.2, hspace=0.35)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            plot_filename = f"adaptive_figure2_corresponding_conditions_{timestamp}.png"
            plot_path = self.output_dir / plot_filename
            plt.savefig(plot_path, dpi=200, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"✅ 自适应Y轴图2已保存: {plot_filename}")
            return plot_path

        except Exception as e:
            print(f"❌ 创建自适应Y轴图2失败: {str(e)}")
            return None

    def create_figure3_rest1_vs_tests(self):
        """图3：4行3列 - rest1与各test条件对比 - 自适应Y轴版本"""
        print("\n📊 生成自适应Y轴图3：rest1与各test条件对比...")

        try:
            fig, axes = plt.subplots(4, 3, figsize=(48, 32))

            condition_pairs = [
                (['rest1'], ['test1']),
                (['rest1'], ['test2']),
                (['rest1'], ['test3'])
            ]

            regions = ['左前额', '右前额', '额中央区域', '中央区域']

            for row, region_name in enumerate(regions):
                for col, (rest_conds, test_conds) in enumerate(condition_pairs):
                    title = f'{region_name} - {rest_conds[0]} vs {test_conds[0]}'
                    self._plot_region_conditions(axes[row, col], region_name,
                                                rest_conds + test_conds, title)

            plt.subplots_adjust(left=0.06, right=0.96, top=0.95, bottom=0.08,
                              wspace=0.2, hspace=0.35)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            plot_filename = f"adaptive_figure3_rest1_vs_tests_{timestamp}.png"
            plot_path = self.output_dir / plot_filename
            plt.savefig(plot_path, dpi=200, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"✅ 自适应Y轴图3已保存: {plot_filename}")
            return plot_path

        except Exception as e:
            print(f"❌ 创建自适应Y轴图3失败: {str(e)}")
            return None

    def create_figure4_cross_conditions(self):
        """图4：4行2列 - 交叉条件对比 - 自适应Y轴版本"""
        print("\n📊 生成自适应Y轴图4：交叉条件对比...")

        try:
            fig, axes = plt.subplots(4, 2, figsize=(32, 32))

            condition_pairs = [
                (['rest2'], ['test1']),
                (['rest3'], ['test2'])
            ]

            regions = ['左前额', '右前额', '额中央区域', '中央区域']

            for row, region_name in enumerate(regions):
                for col, (rest_conds, test_conds) in enumerate(condition_pairs):
                    title = f'{region_name} - {rest_conds[0]} vs {test_conds[0]}'
                    self._plot_region_conditions(axes[row, col], region_name,
                                                rest_conds + test_conds, title)

            plt.subplots_adjust(left=0.08, right=0.95, top=0.95, bottom=0.08,
                              wspace=0.25, hspace=0.35)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            plot_filename = f"adaptive_figure4_cross_conditions_{timestamp}.png"
            plot_path = self.output_dir / plot_filename
            plt.savefig(plot_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"✅ 自适应Y轴图4已保存: {plot_filename}")
            return plot_path

        except Exception as e:
            print(f"❌ 创建自适应Y轴图4失败: {str(e)}")
            return None

    def generate_all_visualizations(self):
        """生成所有可视化 - 自适应Y轴版本"""
        print("="*80)
        print("🎨 自适应Y轴增强版HEP结果可视化 v3.1")
        print("="*80)

        paths = []

        try:
            # 1. 生成4行2列的test和rest条件对比图
            paths.append(self.create_comprehensive_test_rest_comparison())

            # 2. 图2：4行3列 - 对应条件对比
            paths.append(self.create_figure2_corresponding_conditions())

            # 3. 图3：4行3列 - rest1与各test条件对比
            paths.append(self.create_figure3_rest1_vs_tests())

            # 4. 图4：4行2列 - 交叉条件对比
            paths.append(self.create_figure4_cross_conditions())

            print(f"\n" + "="*80)
            print("✅ 所有自适应Y轴可视化已完成！")
            print(f"结果保存在: {self.output_dir}")
            print("🔧 每个图的Y轴范围都已自动调整以完整显示数据")
            print("="*80)

        except Exception as e:
            print(f"❌ 可视化过程中出现错误: {e}")
            import traceback
            traceback.print_exc()

        return paths


def main():
    """主函数"""
    try:
        # 创建自适应Y轴可视化器（自动查找最新数据）
        visualizer = EnhancedHEPVisualizerAdaptive()

        # 生成所有可视化
        visualizer.generate_all_visualizations()

    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
