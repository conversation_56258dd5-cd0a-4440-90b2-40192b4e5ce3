#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
七个条件HEP数据提取器

专门用于提取所有七个实验条件的HEP数据，
基于完整测试系统的成功经验，
确保数据的完整性和可靠性。

作者：HEP分析团队
日期：2024年12月
版本：1.0 - 七个条件完整版
"""

import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import mne
from datetime import datetime
import json
import pickle
import warnings
from scipy import signal
import re

warnings.filterwarnings('ignore')

# 添加脚本路径
sys.path.append(str(Path(__file__).parent))

class SevenConditionsHEPExtractor:
    """七个条件HEP数据提取器"""
    
    def __init__(self, data_dir, validation_excel_path):
        """
        初始化提取器
        
        Parameters:
        -----------
        data_dir : str
            数据目录路径
        validation_excel_path : str
            验证Excel文件路径
        """
        self.data_dir = Path(data_dir)
        self.validation_excel_path = Path(validation_excel_path)
        
        # 七个实验条件的详细配置（基于实际文件命名模式）
        self.conditions_config = {
            'rest1': {
                'name': '静息状态1',
                'patterns': ['_01_.*_rest_'],  # 第一次会话的静息状态
                'description': '第一次会话静息状态记录',
                'regex': True
            },
            'rest2': {
                'name': '静息状态2',
                'patterns': ['_02_.*_rest_'],  # 第二次会话的静息状态
                'description': '第二次会话静息状态记录',
                'regex': True
            },
            'rest3': {
                'name': '静息状态3',
                'patterns': ['_03_.*_rest_'],  # 第三次会话的静息状态
                'description': '第三次会话静息状态记录',
                'regex': True
            },
            'test1': {
                'name': '测试状态1',
                'patterns': ['_01_.*_test_'],  # 第一次会话的测试状态
                'description': '第一次会话测试状态记录',
                'regex': True
            },
            'test2': {
                'name': '测试状态2',
                'patterns': ['_02_.*_test_'],  # 第二次会话的测试状态
                'description': '第二次会话测试状态记录',
                'regex': True
            },
            'test3': {
                'name': '测试状态3',
                'patterns': ['_03_.*_test_'],  # 第三次会话的测试状态
                'description': '第三次会话测试状态记录',
                'regex': True
            },
            'prac': {
                'name': '练习状态',
                'patterns': ['_prac_'],  # 练习状态（只有一次）
                'description': '练习状态记录',
                'regex': False
            }
        }
        
        # 提取配置
        self.extraction_config = {
            'n_files_per_condition': 10,  # 每个条件最多处理10个文件
            'sampling_rate': 500,
            'methods': ['zero_phase', 'matlab_style'],
            'min_file_size_mb': 10,
            'max_file_size_mb': 1000,
            'min_r_waves': 50,
            'ecg_amplitude_range': (1e-3, 1.0)
        }
        
        # 创建输出目录
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.output_dir = Path(f"../../result/seven_conditions_hep_extraction_{timestamp}")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 存储提取的数据
        self.extracted_data = {
            'zero_phase': {},
            'matlab_style': {},
            'metadata': {
                'extraction_config': self.extraction_config,
                'conditions_config': self.conditions_config,
                'timestamp': timestamp
            }
        }
        
        print("🚀 七个条件HEP数据提取器初始化完成")
        print(f"📁 数据目录: {self.data_dir}")
        print(f"💾 输出目录: {self.output_dir}")
        print(f"🎯 目标条件: {list(self.conditions_config.keys())}")
    
    def scan_and_classify_files(self):
        """扫描并分类所有文件"""
        print("\n📁 扫描并分类数据文件...")
        
        # 查找所有.fif文件
        fif_files = list(self.data_dir.glob("*.fif"))
        print(f"📄 找到 {len(fif_files)} 个.fif文件")
        
        if not fif_files:
            print("❌ 未找到.fif文件")
            return {}
        
        # 按条件分类文件
        classified_files = {condition: [] for condition in self.conditions_config.keys()}
        unclassified_files = []
        
        for file_path in fif_files:
            file_name_lower = file_path.name.lower()
            classified = False
            
            # 尝试匹配每个条件
            for condition, config in self.conditions_config.items():
                patterns = config['patterns']
                use_regex = config.get('regex', False)

                # 检查是否匹配任何模式
                for pattern in patterns:
                    matched = False

                    if use_regex:
                        # 使用正则表达式匹配
                        if re.search(pattern, file_name_lower):
                            matched = True
                    else:
                        # 使用简单字符串匹配
                        if pattern in file_name_lower:
                            matched = True

                    if matched:
                        # 检查文件大小
                        try:
                            file_size = file_path.stat().st_size / (1024 * 1024)  # MB
                            if (self.extraction_config['min_file_size_mb'] < file_size <
                                self.extraction_config['max_file_size_mb']):

                                classified_files[condition].append({
                                    'file_path': file_path,
                                    'file_name': file_path.name,
                                    'file_size_mb': file_size,
                                    'matched_pattern': pattern
                                })
                                classified = True
                                break
                        except:
                            continue

                if classified:
                    break
            
            if not classified:
                unclassified_files.append(file_path.name)
        
        # 报告分类结果
        print("\n📊 文件分类结果:")
        total_classified = 0
        for condition, files in classified_files.items():
            condition_name = self.conditions_config[condition]['name']
            print(f"  ✅ {condition} ({condition_name}): {len(files)} 个文件")
            total_classified += len(files)
        
        print(f"  📊 总计分类: {total_classified} 个文件")
        print(f"  ⚠️ 未分类: {len(unclassified_files)} 个文件")
        
        if len(unclassified_files) > 0 and len(unclassified_files) <= 10:
            print("  未分类文件示例:")
            for file_name in unclassified_files[:5]:
                print(f"    - {file_name}")
        
        # 限制每个条件的文件数量
        for condition in classified_files:
            if len(classified_files[condition]) > self.extraction_config['n_files_per_condition']:
                # 按文件大小排序，选择中等大小的文件
                files = classified_files[condition]
                files.sort(key=lambda x: x['file_size_mb'])
                
                # 选择中间部分的文件
                start_idx = len(files) // 4
                end_idx = start_idx + self.extraction_config['n_files_per_condition']
                classified_files[condition] = files[start_idx:end_idx]
                
                condition_name = self.conditions_config[condition]['name']
                print(f"  🔧 {condition} ({condition_name}): 限制为 {len(classified_files[condition])} 个文件")
        
        return classified_files
    
    def extract_all_conditions(self):
        """提取所有条件的HEP数据"""
        print("\n🧠 开始提取所有条件的HEP数据...")
        
        # 扫描和分类文件
        classified_files = self.scan_and_classify_files()
        
        if not any(classified_files.values()):
            print("❌ 没有找到可处理的文件")
            return False
        
        # 初始化数据存储
        for method in self.extraction_config['methods']:
            for condition in self.conditions_config.keys():
                self.extracted_data[method][condition] = []
        
        # 处理每个条件
        total_success = 0
        total_files = 0
        
        for condition, files in classified_files.items():
            if not files:
                continue
            
            condition_name = self.conditions_config[condition]['name']
            print(f"\n📊 处理条件: {condition} ({condition_name})")
            print(f"📄 文件数量: {len(files)}")
            
            condition_success = 0
            
            for i, file_info in enumerate(files):
                print(f"  📄 文件 {i+1}/{len(files)}: {file_info['file_name']}")
                print(f"    💾 大小: {file_info['file_size_mb']:.1f} MB")
                
                # 提取单个文件的HEP数据
                file_results = self._extract_single_file_hep(file_info, condition)
                
                # 保存结果
                for method in self.extraction_config['methods']:
                    if file_results[method]['success']:
                        self.extracted_data[method][condition].append(file_results[method])
                        print(f"    ✅ {method}: 成功")
                    else:
                        print(f"    ❌ {method}: {file_results[method]['error']}")
                
                # 统计成功率
                if any(file_results[method]['success'] for method in self.extraction_config['methods']):
                    condition_success += 1
                
                total_files += 1
            
            print(f"  📊 条件 {condition} 完成: {condition_success}/{len(files)} 文件成功")
            total_success += condition_success
        
        print(f"\n✅ 所有条件处理完成: {total_success}/{total_files} 文件成功")
        
        # 保存提取的数据
        self._save_extracted_data()
        
        return total_success > 0
    
    def _extract_single_file_hep(self, file_info, condition):
        """提取单个文件的HEP数据（基于完整测试的成功经验）"""
        results = {
            'zero_phase': {'success': False, 'data': None, 'error': None},
            'matlab_style': {'success': False, 'data': None, 'error': None}
        }
        
        try:
            # 加载数据
            raw = mne.io.read_raw_fif(file_info['file_path'], preload=True, verbose=False)
            data = raw.get_data()
            sampling_rate = raw.info['sfreq']
            
            # 分离EEG和ECG数据
            eeg_data = data[:61, :]
            ecg_data = data[61:, :]
            
            # 选择最佳ECG通道
            best_ecg = self._select_best_ecg_channel(ecg_data)
            if best_ecg is None:
                error_msg = "无法找到合适的ECG通道"
                results['zero_phase']['error'] = error_msg
                results['matlab_style']['error'] = error_msg
                return results
            
            # 两种方法的R波检测
            r_peaks_zp = self._simplified_r_wave_detection(best_ecg, sampling_rate, method='zero_phase')
            r_peaks_ms = self._simplified_r_wave_detection(best_ecg, sampling_rate, method='matlab_style')
            
            # 检查R波数量
            if len(r_peaks_zp) < self.extraction_config['min_r_waves']:
                results['zero_phase']['error'] = f'R波数量不足: {len(r_peaks_zp)}'
            else:
                # 零相位方法HEP提取
                zp_result = self._extract_hep_with_method(
                    eeg_data, r_peaks_zp, sampling_rate, file_info, condition, 'zero_phase'
                )
                results['zero_phase'] = zp_result
            
            if len(r_peaks_ms) < self.extraction_config['min_r_waves']:
                results['matlab_style']['error'] = f'R波数量不足: {len(r_peaks_ms)}'
            else:
                # MATLAB风格方法HEP提取
                ms_result = self._extract_hep_with_method(
                    eeg_data, r_peaks_ms, sampling_rate, file_info, condition, 'matlab_style'
                )
                results['matlab_style'] = ms_result
            
        except Exception as e:
            error_msg = f"文件处理失败: {str(e)}"
            results['zero_phase']['error'] = error_msg
            results['matlab_style']['error'] = error_msg
        
        return results
    
    def _select_best_ecg_channel(self, ecg_data):
        """选择最佳ECG通道（基于完整测试的成功经验）"""
        best_channel = None
        best_score = 0
        
        for i in range(min(10, ecg_data.shape[0])):
            channel_data = ecg_data[i, :]
            amplitude = np.std(channel_data)
            
            # 使用修正的ECG幅度范围
            if (self.extraction_config['ecg_amplitude_range'][0] < amplitude < 
                self.extraction_config['ecg_amplitude_range'][1]):
                score = amplitude
                if score > best_score:
                    best_score = score
                    best_channel = channel_data
        
        return best_channel
    
    def _simplified_r_wave_detection(self, ecg_signal, sampling_rate, method='zero_phase'):
        """简化的R波检测（基于完整测试的成功经验）"""
        try:
            # 预处理
            sos = signal.butter(2, [0.5, 40], btype='band', fs=sampling_rate, output='sos')
            ecg_filtered = signal.sosfiltfilt(sos, ecg_signal)
            
            if method == 'matlab_style':
                # MATLAB风格：基于导数
                derivative = np.gradient(ecg_filtered)
                
                # 寻找导数零交叉点
                zero_crossings = []
                for i in range(1, len(derivative) - 1):
                    if derivative[i-1] > 0 and derivative[i+1] < 0:
                        zero_crossings.append(i)
                
                # 在零交叉点附近寻找峰值
                candidate_peaks = []
                search_window = int(0.02 * sampling_rate)
                
                for crossing in zero_crossings:
                    start_idx = max(0, crossing - search_window)
                    end_idx = min(len(ecg_filtered), crossing + search_window + 1)
                    
                    local_segment = ecg_filtered[start_idx:end_idx]
                    local_peak_idx = np.argmax(np.abs(local_segment))
                    global_peak_idx = start_idx + local_peak_idx
                    
                    candidate_peaks.append(global_peak_idx)
                
                # 幅度阈值筛选
                if len(candidate_peaks) > 0:
                    peak_amplitudes = np.abs(ecg_filtered[candidate_peaks])
                    threshold = np.median(peak_amplitudes) + 1.5 * np.std(peak_amplitudes)
                    filtered_peaks = [peak for peak, amp in zip(candidate_peaks, peak_amplitudes) 
                                    if amp >= threshold]
                else:
                    filtered_peaks = []
                
            else:  # zero_phase method
                # 零相位方法：多方法检测
                threshold = np.std(ecg_filtered) * 2
                
                # 正峰检测
                peaks_pos, _ = signal.find_peaks(
                    ecg_filtered, 
                    height=threshold, 
                    distance=int(0.3 * sampling_rate)
                )
                
                # 负峰检测
                peaks_neg, _ = signal.find_peaks(
                    -ecg_filtered, 
                    height=threshold, 
                    distance=int(0.3 * sampling_rate)
                )
                
                # 选择数量更多的结果
                if len(peaks_pos) >= len(peaks_neg):
                    filtered_peaks = peaks_pos
                else:
                    filtered_peaks = peaks_neg
            
            # 距离约束
            if len(filtered_peaks) > 1:
                min_distance = int(0.3 * sampling_rate)
                final_peaks = [filtered_peaks[0]]
                
                for peak in filtered_peaks[1:]:
                    if peak - final_peaks[-1] >= min_distance:
                        final_peaks.append(peak)
            else:
                final_peaks = filtered_peaks
            
            return np.array(final_peaks)
            
        except Exception as e:
            print(f"R波检测失败: {e}")
            return np.array([])
    
    def _extract_hep_with_method(self, eeg_data, r_peaks, sampling_rate, file_info, condition, method):
        """使用指定方法提取HEP（基于完整测试的成功经验）"""
        try:
            # EEG零相位滤波
            sos = signal.butter(4, [0.1, 30], btype='band', fs=sampling_rate, output='sos')
            eeg_filtered = signal.sosfiltfilt(sos, eeg_data, axis=1)
            
            # HEP提取
            hep_epochs, times = self._extract_hep_epochs_simplified(eeg_filtered, r_peaks, sampling_rate)
            
            if hep_epochs is None:
                return {'success': False, 'error': 'HEP提取失败'}
            
            # 计算平均HEP
            hep_average = np.mean(hep_epochs, axis=0)
            
            # 质量指标
            quality_metrics = self._calculate_quality_metrics_simplified(hep_epochs, times)
            
            return {
                'success': True,
                'data': {
                    'hep_epochs': hep_epochs,
                    'hep_average': hep_average,
                    'times': times,
                    'valid_heartbeats': len(r_peaks),
                    'quality_metrics': quality_metrics,
                    'file_info': file_info,
                    'condition': condition,
                    'method': method
                },
                'error': None
            }
            
        except Exception as e:
            return {'success': False, 'error': f'{method}提取失败: {str(e)}'}
    
    def _extract_hep_epochs_simplified(self, eeg_data, r_peaks, sampling_rate):
        """简化的HEP时期提取（基于完整测试的成功经验）"""
        try:
            # HEP参数
            time_window = (-0.2, 0.8)  # -200ms到800ms
            baseline_window = (-0.2, 0)  # 基线校正窗口
            
            # 计算样本点
            start_samples = int(time_window[0] * sampling_rate)
            end_samples = int(time_window[1] * sampling_rate)
            baseline_start = int(baseline_window[0] * sampling_rate)
            baseline_end = int(baseline_window[1] * sampling_rate)
            
            # 提取时期
            epochs = []
            valid_r_peaks = []
            
            for r_peak in r_peaks:
                epoch_start = r_peak + start_samples
                epoch_end = r_peak + end_samples
                
                # 检查边界
                if epoch_start >= 0 and epoch_end < eeg_data.shape[1]:
                    epoch = eeg_data[:, epoch_start:epoch_end]
                    
                    # 基线校正
                    baseline_start_idx = -start_samples + baseline_start
                    baseline_end_idx = -start_samples + baseline_end
                    baseline = np.mean(epoch[:, baseline_start_idx:baseline_end_idx], axis=1, keepdims=True)
                    epoch_corrected = epoch - baseline
                    
                    # 自适应伪迹检测（基于完整测试的成功经验）
                    max_amplitude = np.max(np.abs(epoch_corrected))
                    amplitude_threshold = np.percentile(np.abs(epoch_corrected), 95) * 3  # 95%分位数的3倍
                    
                    if max_amplitude < amplitude_threshold:
                        epochs.append(epoch_corrected)
                        valid_r_peaks.append(r_peak)
            
            if len(epochs) == 0:
                return None, None
            
            # 转换为numpy数组
            hep_epochs = np.array(epochs)
            
            # 时间轴
            times = np.linspace(time_window[0], time_window[1], hep_epochs.shape[2])
            
            return hep_epochs, times
            
        except Exception as e:
            print(f"HEP提取失败: {e}")
            return None, None
    
    def _calculate_quality_metrics_simplified(self, hep_epochs, times):
        """简化的质量指标计算（基于完整测试的成功经验）"""
        try:
            # 基本统计
            n_epochs = hep_epochs.shape[0]
            n_channels = hep_epochs.shape[1]
            
            # 信噪比（简化计算）
            signal_power = np.mean(np.var(hep_epochs, axis=2))
            noise_power = np.mean(np.var(hep_epochs, axis=0))
            snr = 10 * np.log10(signal_power / (noise_power + 1e-10))
            
            # 平均幅度
            mean_amplitude = np.mean(np.abs(hep_epochs))
            
            # 时间窗口内的峰值
            hep_window_mask = (times >= 0.455) & (times <= 0.595)  # 455-595ms
            hep_window_data = hep_epochs[:, :, hep_window_mask]
            hep_amplitude = np.mean(np.abs(hep_window_data))
            
            return {
                'n_epochs': n_epochs,
                'n_channels': n_channels,
                'snr': snr,
                'mean_amplitude': mean_amplitude,
                'hep_amplitude': hep_amplitude,
                'quality_score': min(10, max(0, snr + 5))  # 简化评分
            }
            
        except Exception as e:
            return {
                'n_epochs': 0,
                'snr': 0,
                'mean_amplitude': 0,
                'hep_amplitude': 0,
                'quality_score': 0,
                'error': str(e)
            }
    
    def _save_extracted_data(self):
        """保存提取的HEP数据"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 保存完整数据（pickle格式）
            data_path = self.output_dir / f"seven_conditions_hep_data_{timestamp}.pkl"
            with open(data_path, 'wb') as f:
                pickle.dump(self.extracted_data, f)
            
            print(f"\n💾 完整数据已保存: {data_path}")
            
            # 保存元数据（JSON格式）
            metadata = self.extracted_data['metadata'].copy()
            
            # 统计数据
            metadata['data_summary'] = {}
            for method in self.extraction_config['methods']:
                metadata['data_summary'][method] = {}
                for condition in self.conditions_config.keys():
                    if condition in self.extracted_data[method]:
                        successful_files = [d for d in self.extracted_data[method][condition] if d['success']]
                        metadata['data_summary'][method][condition] = {
                            'n_files': len(successful_files),
                            'total_epochs': sum(d['data']['valid_heartbeats'] for d in successful_files),
                            'avg_quality': np.mean([d['data']['quality_metrics']['quality_score'] for d in successful_files]) if successful_files else 0
                        }
            
            metadata_path = self.output_dir / f"seven_conditions_metadata_{timestamp}.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            print(f"📄 元数据已保存: {metadata_path}")
            
            # 保存CSV摘要
            self._save_csv_summary(timestamp)
            
            return True
            
        except Exception as e:
            print(f"❌ 数据保存失败: {str(e)}")
            return False
    
    def _save_csv_summary(self, timestamp):
        """保存CSV格式的数据摘要"""
        try:
            summary_data = []
            
            for method in self.extraction_config['methods']:
                for condition in self.conditions_config.keys():
                    if condition in self.extracted_data[method]:
                        for i, result in enumerate(self.extracted_data[method][condition]):
                            if result['success']:
                                data = result['data']
                                summary_data.append({
                                    'method': method,
                                    'condition': condition,
                                    'condition_name': self.conditions_config[condition]['name'],
                                    'file_index': i,
                                    'file_name': data['file_info']['file_name'],
                                    'valid_heartbeats': data['valid_heartbeats'],
                                    'quality_score': data['quality_metrics']['quality_score'],
                                    'snr': data['quality_metrics']['snr'],
                                    'hep_amplitude': data['quality_metrics']['hep_amplitude'],
                                    'n_channels': data['quality_metrics']['n_channels']
                                })
            
            if summary_data:
                df = pd.DataFrame(summary_data)
                csv_path = self.output_dir / f"seven_conditions_summary_{timestamp}.csv"
                df.to_csv(csv_path, index=False, encoding='utf-8')
                print(f"📊 CSV摘要已保存: {csv_path}")
                
                # 打印统计摘要
                print("\n📊 提取结果统计:")
                for method in self.extraction_config['methods']:
                    method_data = df[df['method'] == method]
                    print(f"  {method}:")
                    for condition in self.conditions_config.keys():
                        condition_data = method_data[method_data['condition'] == condition]
                        if len(condition_data) > 0:
                            condition_name = self.conditions_config[condition]['name']
                            total_epochs = condition_data['valid_heartbeats'].sum()
                            avg_quality = condition_data['quality_score'].mean()
                            print(f"    {condition} ({condition_name}): {len(condition_data)} 文件, {total_epochs} 时期, 质量 {avg_quality:.2f}")
            
        except Exception as e:
            print(f"⚠️ CSV摘要保存失败: {str(e)}")


def main():
    """主函数"""
    # 配置路径
    data_dir = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"
    validation_excel_path = r"D:\ecgeeg\30-数据分析\5-HBA\result\data_validation\data_validation_results_20241201_143654.xlsx"
    
    # 创建提取器
    extractor = SevenConditionsHEPExtractor(data_dir, validation_excel_path)
    
    # 运行提取
    success = extractor.extract_all_conditions()
    
    if success:
        print("\n🎉 七个条件HEP数据提取成功完成！")
        print(f"📁 所有结果保存在: {extractor.output_dir}")
    else:
        print("\n❌ 提取过程中出现问题，请检查日志")


if __name__ == "__main__":
    main()
