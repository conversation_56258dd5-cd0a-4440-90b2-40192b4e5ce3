#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MATLAB风格R波检测器 - 基于导数和幅度阈值的峰值检测

实现基于Kruczyk等人(2013)描述的MATLAB峰值查找器函数的Python版本：
- 使用导数的交替性质
- 用户定义的幅度阈值
- 快速识别噪声矢量中的局部峰值或谷值

参考文献：
<PERSON>, <PERSON>, <PERSON>, H. M., Enroth, S., & <PERSON>, J. (2013). 
Peak Finder Metaserver - a novel application for finding peaks in ChIP-seq data.

作者：HEP分析团队
日期：2024年12月
版本：1.0
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from scipy.stats import zscore
import mne
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class MATLABStyleRWaveDetector:
    """
    MATLAB风格R波检测器
    
    基于导数交替性质和幅度阈值的峰值检测算法
    """
    
    def __init__(self, sampling_rate=500):
        """
        初始化检测器
        
        Parameters:
        -----------
        sampling_rate : float
            采样率
        """
        self.sampling_rate = sampling_rate
        self.detection_params = {
            'derivative_order': 1,           # 导数阶数
            'amplitude_threshold_factor': 2.0,  # 幅度阈值因子
            'min_peak_distance': 0.3,       # 最小峰值间距(秒)
            'prominence_factor': 0.5,       # 峰值突出度因子
            'width_range': (0.05, 0.15),    # R波宽度范围(秒)
        }
        
        print("🔍 MATLAB风格R波检测器初始化完成")
        print(f"   采样率: {sampling_rate} Hz")
        print(f"   检测参数: {self.detection_params}")
    
    def detect_r_waves_matlab_style(self, ecg_signal, channel_name="ECG"):
        """
        使用MATLAB风格算法检测R波
        
        Parameters:
        -----------
        ecg_signal : array
            ECG信号
        channel_name : str
            通道名称
            
        Returns:
        --------
        r_peaks : array
            R波位置
        detection_info : dict
            检测信息
        """
        print(f"🔍 使用MATLAB风格算法检测R波 (通道: {channel_name})")
        
        try:
            # 步骤1: 预处理 - 轻度滤波去除噪声
            ecg_preprocessed = self._preprocess_ecg(ecg_signal)
            
            # 步骤2: 计算导数
            derivative_signal = self._compute_derivative(ecg_preprocessed)
            
            # 步骤3: 基于导数交替性质检测候选峰值
            candidate_peaks = self._find_derivative_based_peaks(
                ecg_preprocessed, derivative_signal
            )
            
            # 步骤4: 应用幅度阈值筛选
            amplitude_filtered_peaks = self._apply_amplitude_threshold(
                ecg_preprocessed, candidate_peaks
            )
            
            # 步骤5: 应用距离和宽度约束
            final_peaks = self._apply_distance_and_width_constraints(
                ecg_preprocessed, amplitude_filtered_peaks
            )
            
            # 步骤6: 质量评估
            quality_score = self._evaluate_detection_quality(
                ecg_preprocessed, final_peaks
            )
            
            detection_info = {
                'method': 'matlab_style_derivative_amplitude',
                'channel': channel_name,
                'n_candidates': len(candidate_peaks),
                'n_amplitude_filtered': len(amplitude_filtered_peaks),
                'n_final': len(final_peaks),
                'quality_score': quality_score,
                'detection_params': self.detection_params.copy()
            }
            
            print(f"   ✅ 检测完成: {len(final_peaks)} 个R波 (质量评分: {quality_score:.2f})")
            
            return final_peaks, detection_info
            
        except Exception as e:
            print(f"   ❌ MATLAB风格R波检测失败: {str(e)}")
            return np.array([]), {'method': 'matlab_style', 'error': str(e)}
    
    def _preprocess_ecg(self, ecg_signal):
        """
        ECG预处理 - 轻度滤波
        
        Parameters:
        -----------
        ecg_signal : array
            原始ECG信号
            
        Returns:
        --------
        ecg_preprocessed : array
            预处理后的ECG信号
        """
        try:
            # 轻度带通滤波 (0.5-40 Hz) 去除基线漂移和高频噪声
            # 使用零相位滤波保持波形形态
            sos = signal.butter(2, [0.5, 40], btype='band', fs=self.sampling_rate, output='sos')
            ecg_filtered = signal.sosfiltfilt(sos, ecg_signal)
            
            return ecg_filtered
            
        except Exception as e:
            print(f"      ⚠️ ECG预处理失败，使用原始信号: {str(e)}")
            return ecg_signal
    
    def _compute_derivative(self, ecg_signal):
        """
        计算ECG信号的导数
        
        Parameters:
        -----------
        ecg_signal : array
            ECG信号
            
        Returns:
        --------
        derivative : array
            导数信号
        """
        # 计算一阶导数
        derivative = np.gradient(ecg_signal)
        
        # 可选：计算二阶导数增强QRS检测
        if self.detection_params['derivative_order'] == 2:
            derivative = np.gradient(derivative)
        
        return derivative
    
    def _find_derivative_based_peaks(self, ecg_signal, derivative_signal):
        """
        基于导数交替性质检测候选峰值
        
        这是MATLAB峰值查找器的核心算法：
        1. 寻找导数的零交叉点
        2. 检查交替性质（正到负的交叉表示峰值）
        3. 验证局部极值特性
        
        Parameters:
        -----------
        ecg_signal : array
            ECG信号
        derivative_signal : array
            导数信号
            
        Returns:
        --------
        candidate_peaks : array
            候选峰值位置
        """
        candidate_peaks = []
        
        # 寻找导数的零交叉点
        zero_crossings = np.where(np.diff(np.signbit(derivative_signal)))[0]
        
        for crossing in zero_crossings:
            # 检查是否为正到负的交叉（峰值）
            if (crossing > 0 and crossing < len(derivative_signal) - 1 and
                derivative_signal[crossing] > 0 and derivative_signal[crossing + 1] < 0):
                
                # 在零交叉点附近寻找真正的峰值
                search_window = int(0.02 * self.sampling_rate)  # ±20ms搜索窗口
                start_idx = max(0, crossing - search_window)
                end_idx = min(len(ecg_signal), crossing + search_window + 1)
                
                # 寻找局部最大值
                local_segment = ecg_signal[start_idx:end_idx]
                local_peak_idx = np.argmax(np.abs(local_segment))  # 支持倒置R波
                
                # 转换为全局索引
                global_peak_idx = start_idx + local_peak_idx
                
                # 验证是否为真正的局部极值
                if self._is_local_extremum(ecg_signal, global_peak_idx):
                    candidate_peaks.append(global_peak_idx)
        
        return np.array(candidate_peaks)
    
    def _is_local_extremum(self, signal, peak_idx, window_size=None):
        """
        验证是否为局部极值
        
        Parameters:
        -----------
        signal : array
            信号
        peak_idx : int
            峰值索引
        window_size : int, optional
            验证窗口大小
            
        Returns:
        --------
        is_extremum : bool
            是否为局部极值
        """
        if window_size is None:
            window_size = int(0.05 * self.sampling_rate)  # 50ms窗口
        
        start_idx = max(0, peak_idx - window_size)
        end_idx = min(len(signal), peak_idx + window_size + 1)
        
        local_segment = signal[start_idx:end_idx]
        local_peak_idx = peak_idx - start_idx
        
        # 检查是否为局部最大值或最小值
        is_max = np.all(np.abs(signal[peak_idx]) >= np.abs(local_segment))
        
        return is_max
    
    def _apply_amplitude_threshold(self, ecg_signal, candidate_peaks):
        """
        应用幅度阈值筛选峰值
        
        Parameters:
        -----------
        ecg_signal : array
            ECG信号
        candidate_peaks : array
            候选峰值
            
        Returns:
        --------
        filtered_peaks : array
            通过幅度阈值的峰值
        """
        if len(candidate_peaks) == 0:
            return candidate_peaks
        
        # 计算候选峰值的幅度
        peak_amplitudes = np.abs(ecg_signal[candidate_peaks])
        
        # 计算自适应阈值
        median_amplitude = np.median(peak_amplitudes)
        std_amplitude = np.std(peak_amplitudes)
        
        # 阈值 = 中位数 + 因子 * 标准差
        amplitude_threshold = median_amplitude + \
                            self.detection_params['amplitude_threshold_factor'] * std_amplitude
        
        # 筛选超过阈值的峰值
        filtered_peaks = candidate_peaks[peak_amplitudes >= amplitude_threshold]
        
        return filtered_peaks
    
    def _apply_distance_and_width_constraints(self, ecg_signal, peaks):
        """
        应用距离和宽度约束
        
        Parameters:
        -----------
        ecg_signal : array
            ECG信号
        peaks : array
            峰值位置
            
        Returns:
        --------
        constrained_peaks : array
            满足约束的峰值
        """
        if len(peaks) <= 1:
            return peaks
        
        # 距离约束：移除过近的峰值
        min_distance_samples = int(self.detection_params['min_peak_distance'] * self.sampling_rate)
        
        constrained_peaks = [peaks[0]]  # 保留第一个峰值
        
        for peak in peaks[1:]:
            if peak - constrained_peaks[-1] >= min_distance_samples:
                # 宽度验证：检查R波宽度是否合理
                if self._validate_r_wave_width(ecg_signal, peak):
                    constrained_peaks.append(peak)
        
        return np.array(constrained_peaks)
    
    def _validate_r_wave_width(self, ecg_signal, peak_idx):
        """
        验证R波宽度
        
        Parameters:
        -----------
        ecg_signal : array
            ECG信号
        peak_idx : int
            峰值索引
            
        Returns:
        --------
        is_valid : bool
            宽度是否有效
        """
        try:
            # 计算R波的半高宽
            peak_amplitude = ecg_signal[peak_idx]
            half_amplitude = peak_amplitude / 2
            
            # 向左寻找半高点
            left_idx = peak_idx
            while (left_idx > 0 and 
                   np.abs(ecg_signal[left_idx]) > np.abs(half_amplitude)):
                left_idx -= 1
            
            # 向右寻找半高点
            right_idx = peak_idx
            while (right_idx < len(ecg_signal) - 1 and 
                   np.abs(ecg_signal[right_idx]) > np.abs(half_amplitude)):
                right_idx += 1
            
            # 计算宽度
            width_samples = right_idx - left_idx
            width_seconds = width_samples / self.sampling_rate
            
            # 检查是否在合理范围内
            min_width, max_width = self.detection_params['width_range']
            is_valid = min_width <= width_seconds <= max_width
            
            return is_valid
            
        except Exception:
            return True  # 如果验证失败，默认接受
    
    def _evaluate_detection_quality(self, ecg_signal, peaks):
        """
        评估检测质量
        
        Parameters:
        -----------
        ecg_signal : array
            ECG信号
        peaks : array
            检测到的峰值
            
        Returns:
        --------
        quality_score : float
            质量评分 (0-10)
        """
        if len(peaks) < 2:
            return 0.0
        
        try:
            # 1. R-R间隔规律性评分
            rr_intervals = np.diff(peaks) / self.sampling_rate
            rr_cv = np.std(rr_intervals) / np.mean(rr_intervals)  # 变异系数
            regularity_score = max(0, 3 - rr_cv * 10)  # 0-3分
            
            # 2. 峰值幅度一致性评分
            peak_amplitudes = np.abs(ecg_signal[peaks])
            amp_cv = np.std(peak_amplitudes) / np.mean(peak_amplitudes)
            amplitude_score = max(0, 3 - amp_cv * 10)  # 0-3分
            
            # 3. 心率合理性评分
            mean_hr = 60 / np.mean(rr_intervals)
            if 50 <= mean_hr <= 120:
                hr_score = 2.0
            elif 40 <= mean_hr <= 150:
                hr_score = 1.0
            else:
                hr_score = 0.0
            
            # 4. 检测数量评分
            if len(peaks) >= 100:
                count_score = 2.0
            elif len(peaks) >= 50:
                count_score = 1.0
            else:
                count_score = 0.0
            
            total_score = regularity_score + amplitude_score + hr_score + count_score
            return min(10.0, total_score)
            
        except Exception:
            return 5.0  # 默认中等评分
    
    def detect_r_waves_multiple_channels(self, ecg_data, channel_names=None):
        """
        在多个ECG通道上检测R波
        
        Parameters:
        -----------
        ecg_data : array
            多通道ECG数据 (channels x samples)
        channel_names : list, optional
            通道名称列表
            
        Returns:
        --------
        best_peaks : array
            最佳R波位置
        detection_info : dict
            检测信息
        """
        if ecg_data.ndim == 1:
            ecg_data = ecg_data.reshape(1, -1)
        
        if channel_names is None:
            channel_names = [f'ECG{i+1}' for i in range(ecg_data.shape[0])]
        
        print(f"🔍 在 {ecg_data.shape[0]} 个ECG通道上检测R波...")
        
        best_peaks = np.array([])
        best_quality = 0
        best_channel = None
        all_results = {}
        
        for i, channel_name in enumerate(channel_names):
            if i >= ecg_data.shape[0]:
                break
                
            peaks, info = self.detect_r_waves_matlab_style(
                ecg_data[i, :], channel_name
            )
            
            all_results[channel_name] = {
                'peaks': peaks,
                'info': info
            }
            
            # 选择质量最好的结果
            quality = info.get('quality_score', 0)
            if quality > best_quality:
                best_quality = quality
                best_peaks = peaks
                best_channel = channel_name
        
        detection_info = {
            'best_channel': best_channel,
            'best_quality': best_quality,
            'all_results': all_results,
            'method': 'matlab_style_multi_channel'
        }
        
        print(f"✅ 最佳结果来自通道 {best_channel}: {len(best_peaks)} 个R波 (质量: {best_quality:.2f})")
        
        return best_peaks, detection_info
    
    def visualize_detection_process(self, ecg_signal, peaks, save_path=None):
        """
        可视化检测过程
        
        Parameters:
        -----------
        ecg_signal : array
            ECG信号
        peaks : array
            检测到的R波
        save_path : str, optional
            保存路径
        """
        fig, axes = plt.subplots(3, 1, figsize=(15, 10))
        
        # 时间轴
        times = np.arange(len(ecg_signal)) / self.sampling_rate
        
        # 1. 原始ECG信号和检测到的R波
        axes[0].plot(times, ecg_signal, 'b-', linewidth=1, label='ECG信号')
        if len(peaks) > 0:
            axes[0].plot(times[peaks], ecg_signal[peaks], 'ro', markersize=6, label='检测到的R波')
        axes[0].set_title('MATLAB风格R波检测结果', fontsize=14, fontweight='bold')
        axes[0].set_ylabel('幅度')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 2. 导数信号
        derivative = self._compute_derivative(ecg_signal)
        axes[1].plot(times, derivative, 'g-', linewidth=1, label='导数信号')
        axes[1].axhline(y=0, color='k', linestyle='--', alpha=0.5)
        if len(peaks) > 0:
            axes[1].plot(times[peaks], derivative[peaks], 'ro', markersize=4, label='R波位置')
        axes[1].set_title('导数信号和零交叉检测', fontsize=12)
        axes[1].set_ylabel('导数幅度')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        # 3. R-R间隔分析
        if len(peaks) > 1:
            rr_intervals = np.diff(peaks) / self.sampling_rate * 1000  # 转换为毫秒
            rr_times = times[peaks[1:]]
            axes[2].plot(rr_times, rr_intervals, 'r.-', linewidth=2, markersize=4)
            axes[2].set_title(f'R-R间隔 (平均: {np.mean(rr_intervals):.1f}ms, 标准差: {np.std(rr_intervals):.1f}ms)', fontsize=12)
            axes[2].set_ylabel('R-R间隔 (ms)')
            axes[2].grid(True, alpha=0.3)
        else:
            axes[2].text(0.5, 0.5, '检测到的R波数量不足', ha='center', va='center', transform=axes[2].transAxes)
        
        axes[2].set_xlabel('时间 (s)')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 检测过程可视化已保存: {save_path}")
        
        plt.show()


def test_matlab_style_detector():
    """测试MATLAB风格检测器"""
    print("🧪 测试MATLAB风格R波检测器")
    
    # 创建检测器
    detector = MATLABStyleRWaveDetector(sampling_rate=500)
    
    # 生成测试ECG信号
    t = np.linspace(0, 10, 5000)  # 10秒，500Hz
    # 模拟ECG信号：基础心率60bpm
    ecg_test = np.sin(2 * np.pi * 1 * t) + 0.5 * np.sin(2 * np.pi * 2 * t) + 0.1 * np.random.randn(len(t))
    
    # 检测R波
    peaks, info = detector.detect_r_waves_matlab_style(ecg_test, "测试ECG")
    
    print(f"检测结果: {len(peaks)} 个R波")
    print(f"质量评分: {info['quality_score']:.2f}")
    
    # 可视化
    detector.visualize_detection_process(ecg_test, peaks)


if __name__ == "__main__":
    test_matlab_style_detector()
